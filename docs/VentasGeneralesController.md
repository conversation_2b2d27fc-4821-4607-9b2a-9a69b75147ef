# Documentación del VentasGeneralesController

## Descripción General

El `VentasGeneralesController` es un controlador de Symfony que maneja la funcionalidad de reportes de ventas generales en el sistema PV360. Este controlador proporciona una interfaz para que los super administradores puedan visualizar y filtrar información de ventas por empresa y clase de productos.

## Ubicación
```
src/Controller/VentasGeneralesController.php
```

## Namespace
```php
App\Controller
```

## Herencia
Extiende de `Symfony\Bundle\FrameworkBundle\Controller\AbstractController`

## Dependencias

### Imports
- `Symfony\Bundle\FrameworkBundle\Controller\AbstractController`
- `Symfony\Component\HttpFoundation\Response`
- `Symfony\Component\Routing\Annotation\Route`
- `Doctrine\ORM\EntityManagerInterface`
- `App\Entity\Empresa`
- `Symfony\Component\HttpFoundation\Request`
- `App\Entity\Clase`
- `Sensio\Bundle\FrameworkExtraBundle\Configuration\Security`

### Entidades Relacionadas
- **Empresa**: Entidad que representa las empresas del sistema
- **Clase**: Entidad que representa las clases de productos asociadas a cada empresa

## Métodos

### 1. index()

**Ruta**: `/reporte/ventas-generales`
**Nombre**: `app_ventas_generales`
**Método HTTP**: GET
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método principal que renderiza la página de reportes de ventas generales. Obtiene todas las empresas disponibles en el sistema y las pasa a la vista.

#### Parámetros
- `EntityManagerInterface $em`: Inyección de dependencia del Entity Manager de Doctrine

#### Retorno
- `Response`: Respuesta HTTP con la vista renderizada

#### Funcionalidad
1. Obtiene todas las empresas del sistema usando el repositorio de Doctrine
2. Renderiza la plantilla `ventas_generales/index.html.twig` con los datos necesarios
3. Pasa las siguientes variables a la vista:
   - `controller_name`: Nombre del controlador
   - `empresas`: Array con todas las empresas
   - `is_supervisor`: Booleano indicando si es supervisor (false)
   - `is_hardcoded`: Booleano para configuraciones hardcoded (false)

#### Código
```php
public function index(EntityManagerInterface $em): Response
{
    // Para administradores, mostrar todas las empresas
    $empresas = $em->getRepository(Empresa::class)->findAll();
    
    return $this->render('oficial/index.html.twig', [
        'controller_name' => 'VentasGeneralesController',
        'empresas' => $empresas,
        'is_supervisor' => false,
        'is_hardcoded' => false,
    ]);
}
```

### 2. obtenerClases()

**Ruta**: `/ajax/clases-generales`  
**Nombre**: `ventas-generales-obtener-clase`  
**Método HTTP**: GET (AJAX)  
**Seguridad**: Sin restricciones específicas

#### Descripción
Método AJAX que obtiene las clases de productos asociadas a una empresa específica. Se utiliza para filtrar dinámicamente las clases según la empresa seleccionada.

#### Parámetros
- `Request $request`: Objeto de solicitud HTTP
- `EntityManagerInterface $em`: Inyección de dependencia del Entity Manager

#### Retorno
- `Response`: Respuesta HTTP con la vista parcial renderizada

#### Funcionalidad
1. Obtiene el parámetro `idempresa` de la query string
2. Busca todas las clases asociadas a esa empresa
3. Renderiza la plantilla parcial `almacen/clases.html.twig`
4. Retorna la vista con las clases filtradas

#### Código
```php
public function obtenerClases(Request $request, EntityManagerInterface $em): Response
{
    $idempresa = $request->query->get('idempresa');
    $clases = $em->getRepository(Clase::class)->findBy(['empresaIdempresa' => $idempresa]);
    return $this->render('ventas_generales/clases.html.twig', [
        'clases' => $clases,
    ]);
}
```

### 3. obtenerSucursales()

**Ruta**: `/ajax/sucursales-generales`
**Nombre**: `ventas-generales-obtener-sucursales`
**Método HTTP**: GET (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método AJAX que obtiene las sucursales activas asociadas a una empresa específica. Se utiliza para filtrar dinámicamente las sucursales según la empresa seleccionada.

#### Parámetros
- `Request $request`: Objeto de solicitud HTTP
- `EntityManagerInterface $em`: Inyección de dependencia del Entity Manager

#### Retorno
- `Response`: Respuesta HTTP con la vista parcial renderizada

#### Funcionalidad
1. Obtiene el parámetro `idempresa` de la query string
2. Valida que sea un valor numérico
3. Busca todas las sucursales activas asociadas a esa empresa
4. Renderiza la plantilla parcial `ventas_generales/sucursales.html.twig`
5. Retorna la vista con las sucursales filtradas ordenadas por nombre

### 4. generarReporte()

**Ruta**: `/ajax/generar-reporte-ventas-generales`
**Nombre**: `ventas-generales-generar-reporte`
**Método HTTP**: POST (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método principal para generar el reporte de ventas generales según los filtros seleccionados. Incluye validaciones avanzadas y consultas optimizadas.

#### Parámetros
- `Request $request`: Objeto de solicitud HTTP con los filtros
- `EntityManagerInterface $em`: Inyección de dependencia del Entity Manager

#### Retorno
- `JsonResponse`: Respuesta JSON con los datos del reporte

#### Funcionalidad
1. Valida parámetros de entrada (empresa, fechas, sucursales, clases)
2. Construye consulta SQL optimizada con Doctrine QueryBuilder
3. Aplica filtros dinámicos según los parámetros recibidos
4. Calcula métricas: ventas totales, pagos, pendientes, promedios
5. Retorna datos formateados en JSON

### 5. obtenerGraficasIngresos()

**Ruta**: `/ajax/graficas-ingresos-diarios`
**Nombre**: `ventas-generales-graficas-ingresos`
**Método HTTP**: GET (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método AJAX para obtener datos de gráficas de ingresos diarios, incluyendo ingresos por sucursal, tipos de pago y deuda por sucursal.

#### Funcionalidad
- Proporciona datos para gráficas de barras, pie y columnas
- Incluye información de ingresos, tipos de pago y deudas
- Retorna datos estructurados para ApexCharts

### 6. obtenerVentasAnuales()

**Ruta**: `/ajax/ventas-anuales`
**Nombre**: `ventas-generales-ventas-anuales`
**Método HTTP**: GET (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método AJAX para obtener datos de ventas anuales, incluyendo ventas mensuales y pagos anuales por sucursal.

#### Funcionalidad
- Proporciona datos mensuales para gráficas de líneas
- Incluye distribución de pagos por sucursal
- Permite filtrado por año específico

### 7. obtenerProductosMarcas()

**Ruta**: `/ajax/productos-marcas`
**Nombre**: `ventas-generales-productos-marcas`
**Método HTTP**: GET (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método AJAX para obtener datos de productos y marcas, incluyendo análisis de marcas, tratamientos y tabla de productos.

#### Funcionalidad
- Proporciona datos para gráficas de marcas y tratamientos
- Incluye datos tabulares para DataTables
- Análisis de distribución de productos por marca

### 8. obtenerDatosFacturacion()

**Ruta**: `/ajax/datos-facturacion`
**Nombre**: `ventas-generales-datos-facturacion`
**Método HTTP**: GET (AJAX)
**Seguridad**: Requiere rol `ROLE_SUPER_ADMIN`

#### Descripción
Método AJAX para obtener datos de facturación, incluyendo facturación por estado, totales y evolución mensual.

#### Funcionalidad
- Proporciona datos de facturación por estado (pagado, pendiente, vencido)
- Incluye métricas de total facturado
- Evolución mensual de facturación para análisis de tendencias

## Plantillas Utilizadas

### 1. oficial/index.html.twig
- **Propósito**: Vista principal del reporte de ventas generales
- **Características**:
  - Dashboard ejecutivo con tema claro/oscuro
  - Integración con Select2 para selectores avanzados
  - DataTables para manejo de tablas
  - Botón de cerrar sesión
  - Controles de filtrado por empresa

### 2. almacen/clases.html.twig
- **Propósito**: Vista parcial para mostrar clases de productos
- **Características**:
  - Lista de checkboxes para seleccionar clases
  - Switch "Todas" para seleccionar/deseleccionar todas las clases
  - Scroll vertical para listas largas
  - JavaScript para toggle de selección

## Seguridad

### Control de Acceso
- El método `index()` está protegido con `@Security("is_granted('ROLE_SUPER_ADMIN')")`
- Solo usuarios con rol de Super Administrador pueden acceder al reporte principal
- El método AJAX `obtenerClases()` no tiene restricciones específicas

### Consideraciones de Seguridad
- Se recomienda agregar validación de permisos al método `obtenerClases()`
- Validar que el parámetro `idempresa` sea un entero válido
- Implementar sanitización de entrada para prevenir inyección SQL

## Flujo de Trabajo

1. **Acceso Inicial**: Usuario con rol SUPER_ADMIN accede a `/reporte/ventas-generales`
2. **Carga de Datos**: Se cargan todas las empresas disponibles
3. **Selección de Empresa**: Usuario selecciona una empresa del dropdown
4. **Filtrado AJAX**: Se ejecuta llamada AJAX a `/ajax/clases-generales` con el ID de empresa
5. **Actualización de Clases**: Se actualiza dinámicamente la lista de clases disponibles
6. **Generación de Reporte**: Usuario puede proceder con la generación del reporte

## Mejoras Sugeridas

### Seguridad
1. Agregar validación de permisos al método `obtenerClases()`
2. Implementar validación de entrada para `idempresa`
3. Agregar logging de accesos para auditoría

### Funcionalidad
1. Implementar caché para las consultas de empresas y clases
2. Agregar paginación para listas grandes
3. Implementar filtros adicionales (fecha, estado, etc.)

### Performance
1. Optimizar consultas con joins cuando sea necesario
2. Implementar lazy loading para datos grandes
3. Agregar índices en base de datos si es necesario

## Relaciones con Otras Entidades

### Empresa
- **Campos principales**: `idempresa`, `nombre`, `status`
- **Relación**: Una empresa puede tener múltiples clases

### Clase
- **Campos principales**: `idclase`, `nombre`, `status`, `empresaIdempresa`
- **Relación**: Pertenece a una empresa específica

## Notas Técnicas

- El controlador utiliza Doctrine ORM para el manejo de datos
- Las plantillas están basadas en Twig
- Se utiliza Bootstrap 5 para el diseño
- Integración con librerías JavaScript: Select2, DataTables
- Soporte para temas claro/oscuro en la interfaz
