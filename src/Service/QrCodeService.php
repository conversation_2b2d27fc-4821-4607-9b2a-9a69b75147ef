<?php

namespace App\Service;

class QrCodeService
{
    private $projectDir;

    public function __construct(string $projectDir)
    {
        $this->projectDir = $projectDir;
    }

    /**
     * Genera un código QR para una orden de laboratorio
     * 
     * @param string $folio Folio de la venta
     * @param int $idOrdenLaboratorio ID de la orden de laboratorio
     * @param string $baseUrl URL base de la aplicación
     * @return string Nombre del archivo generado (ej: 2556-25445.jpg)
     */
    public function generarQrOrdenLaboratorio(string $folio, int $idOrdenLaboratorio, string $baseUrl): string
    {
        // Validar parámetros
        if (empty($folio) || $idOrdenLaboratorio <= 0 || empty($baseUrl)) {
            throw new \InvalidArgumentException('Parámetros inválidos para generar QR');
        }

        // Crear la URL para ver la información de la orden sin seguridad
        $url = $baseUrl . '/orden/external/info/' . $idOrdenLaboratorio;

        // Nombre del archivo QR (sanitizar el folio para evitar caracteres problemáticos)
        $folioSanitizado = preg_replace('/[^a-zA-Z0-9\-_]/', '', $folio);
        $nombreArchivo = $folioSanitizado . '-' . $idOrdenLaboratorio . '.png';
        
        // Ruta completa donde se guardará el QR
        $directorioQrs = $this->projectDir . '/public/uploads/qrs/';
        
        // Crear directorio si no existe con manejo de errores
        if (!is_dir($directorioQrs)) {
            try {
                mkdir($directorioQrs, 0755, true);
            } catch (\Exception $e) {
                // Fallback: usar directorio temporal
                $directorioQrs = sys_get_temp_dir() . '/pv360_qrs/';
                if (!is_dir($directorioQrs)) {
                    mkdir($directorioQrs, 0755, true);
                }
                $rutaCompleta = $directorioQrs . $nombreArchivo;
            }
        }
        
        $rutaCompleta = $directorioQrs . $nombreArchivo;
        
        // Usar Google Charts API directamente
        return $this->generarQrConGoogleCharts($url, $rutaCompleta, $nombreArchivo);
    }

    /**
     * Método alternativo usando Google Charts API
     */
    private function generarQrConGoogleCharts(string $url, string $rutaCompleta, string $nombreArchivo): string
    {
        $qrUrl = 'https://chart.googleapis.com/chart?chs=200x200&cht=qr&chl=' . urlencode($url);
        
        $qrData = file_get_contents($qrUrl);
        
        if ($qrData !== false) {
            file_put_contents($rutaCompleta, $qrData);
            return $nombreArchivo;
        }
        
        throw new \Exception('No se pudo generar el código QR');
    }

    /**
     * Verifica si existe un QR para una orden específica
     */
    public function existeQr(string $folio, int $idOrdenLaboratorio): bool
    {
        $nombreArchivo = $folio . '-' . $idOrdenLaboratorio . '.png';
        $rutaCompleta = $this->projectDir . '/public/uploads/qrs/' . $nombreArchivo;
        
        return file_exists($rutaCompleta);
    }

    /**
     * Obtiene la ruta relativa del QR
     */
    public function obtenerRutaQr(string $folio, int $idOrdenLaboratorio): string
    {
        $nombreArchivo = $folio . '-' . $idOrdenLaboratorio . '.png';
        return '/uploads/qrs/' . $nombreArchivo;
    }

    /**
     * Elimina un QR específico
     */
    public function eliminarQr(string $folio, int $idOrdenLaboratorio): bool
    {
        $nombreArchivo = $folio . '-' . $idOrdenLaboratorio . '.png';
        $rutaCompleta = $this->projectDir . '/public/uploads/qrs/' . $nombreArchivo;
        
        if (file_exists($rutaCompleta)) {
            return unlink($rutaCompleta);
        }
        
        return false;
    }
}
