<?php

namespace App\Service;

use App\Entity\Ordensalida;
use App\Entity\Producto;
use App\Entity\Productostranspasoalmacen;
use App\Entity\Stock;
use App\Entity\Sucursal;
use App\Entity\Transpasoalmacen;
use App\Entity\Usuario;
use Doctrine\ORM\EntityManagerInterface;
use Psr\Log\LoggerInterface;

class TraspasoMasivoService
{
    private EntityManagerInterface $em;
    private LoggerInterface $logger;

    public function __construct(EntityManagerInterface $em, LoggerInterface $logger)
    {
        $this->em = $em;
        $this->logger = $logger;
    }

    /**
     * Procesa un traspaso masivo desde Google Sheets
     * 
     * @param array $productos Array de productos: [['sku' => '', 'cantidad' => '', 'origen' => '', 'destino' => ''], ...]
     * @param Usuario $usuario Usuario que ejecuta el traspaso
     * @param string $notas Notas del traspaso
     * @return array Resultado del procesamiento
     */
    public function procesarTraspasoMasivo(array $productos, Usuario $usuario, string $notas = '', bool $modoEmergencia = false): array
    {
        $this->em->beginTransaction();
        
        try {
            error_log("Procesando traspaso masivo - Modo emergencia: " . ($modoEmergencia ? 'SÍ' : 'NO'));

            // Validar datos de entrada
            $validacion = $this->validarDatosEntrada($productos);
            if (!$validacion['valido']) {
                return [
                    'exito' => false,
                    'errores' => $validacion['errores'],
                    'traspasos_creados' => []
                ];
            }

            // Agrupar productos por combinación origen-destino
            $gruposPorSucursales = $this->agruparPorSucursales($productos);

            error_log("=== GRUPOS CREADOS ===");
            foreach ($gruposPorSucursales as $key => $grupo) {
                error_log("Grupo '$key': " . count($grupo['productos']) . " productos");
                foreach ($grupo['productos'] as $i => $prod) {
                    error_log("  - Producto $i: SKU {$prod['sku']}, Cantidad {$prod['cantidad']}");
                }
            }

            // Validar stock solo si NO es modo de emergencia
            if (!$modoEmergencia) {
                $validacionStock = $this->validarStockMasivo($gruposPorSucursales);
                if (!$validacionStock['valido']) {
                    return [
                        'exito' => false,
                        'errores' => $validacionStock['errores'],
                        'traspasos_creados' => []
                    ];
                }
            } else {
                error_log("MODO EMERGENCIA: Saltando validaciones de stock");
            }

            // Crear traspasos para cada combinación origen-destino
            $traspasosCreados = [];
            error_log("=== CREANDO TRASPASOS ===");
            error_log("Total grupos a procesar: " . count($gruposPorSucursales));

            foreach ($gruposPorSucursales as $key => $grupo) {
                error_log("Creando traspaso para grupo '$key' con " . count($grupo['productos']) . " productos");
                $traspaso = $this->crearTraspaso($grupo, $usuario, $notas, $modoEmergencia);
                $traspasosCreados[] = $traspaso;
                error_log("Traspaso creado con ID: {$traspaso->getIdtranspasoalmacen()}");
            }

            $this->em->commit();
            
            // Preparar información detallada de los traspasos
            $traspasosInfo = [];
            foreach ($traspasosCreados as $traspaso) {
                // Buscar el grupo correspondiente por IDs
                $keyBuscado = $traspaso->getSucursalIdsucursalorigen()->getIdsucursal() . '->' . $traspaso->getSucursalIdsucursaldestino()->getIdsucursal();
                $productosCount = 0;

                if (isset($gruposPorSucursales[$keyBuscado])) {
                    $productosCount = count($gruposPorSucursales[$keyBuscado]['productos']);
                }

                $traspasosInfo[] = [
                    'idtranspasoalmacen' => $traspaso->getIdtranspasoalmacen(),
                    'sucursal_origen' => $traspaso->getSucursalIdsucursalorigen()->getNombre(),
                    'sucursal_destino' => $traspaso->getSucursalIdsucursaldestino()->getNombre(),
                    'productos_count' => $productosCount
                ];
            }

            return [
                'exito' => true,
                'errores' => [],
                'traspasos_creados' => $traspasosCreados,
                'traspasos_info' => $traspasosInfo,
                'total_productos' => count($productos),
                'total_traspasos' => count($traspasosCreados),
                'modo_emergencia' => $modoEmergencia
            ];

        } catch (\Exception $e) {
            $this->em->rollback();
            $this->logger->error('Error en traspaso masivo: ' . $e->getMessage(), [
                'productos_count' => count($productos),
                'usuario' => $usuario->getUsername(),
                'trace' => $e->getTraceAsString()
            ]);
            
            return [
                'exito' => false,
                'errores' => [['codigo' => 'SYSTEM_ERROR', 'mensaje' => 'Error interno: ' . $e->getMessage()]],
                'traspasos_creados' => []
            ];
        }
    }

    /**
     * Valida los datos de entrada del Google Sheet
     */
    private function validarDatosEntrada(array $productos): array
    {
        $errores = [];
        
        foreach ($productos as $index => $producto) {
            $fila = $index + 2; // +2 porque empezamos en fila 2 del Excel
            
            // Validar SKU
            if (empty($producto['sku'])) {
                $errores[] = ['codigo' => 'SKU_VACIO', 'mensaje' => "Fila {$fila}: SKU vacío"];
                continue;
            }
            
            // Validar cantidad
            if (!isset($producto['cantidad']) || !is_numeric($producto['cantidad']) || $producto['cantidad'] <= 0) {
                $errores[] = ['codigo' => 'CANTIDAD_INVALIDA', 'mensaje' => "Fila {$fila}: Cantidad inválida para SKU {$producto['sku']}"];
            }
            
            // Validar sucursal origen
            if (empty($producto['origen'])) {
                $errores[] = ['codigo' => 'ORIGEN_VACIO', 'mensaje' => "Fila {$fila}: Sucursal origen vacía para SKU {$producto['sku']}"];
            }
            
            // Validar sucursal destino
            if (empty($producto['destino'])) {
                $errores[] = ['codigo' => 'DESTINO_VACIO', 'mensaje' => "Fila {$fila}: Sucursal destino vacía para SKU {$producto['sku']}"];
            }
            
            // Validar que origen y destino sean diferentes
            if (!empty($producto['origen']) && !empty($producto['destino']) && $producto['origen'] == $producto['destino']) {
                $errores[] = ['codigo' => 'ORIGEN_DESTINO_IGUALES', 'mensaje' => "Fila {$fila}: Sucursal origen y destino son iguales para SKU {$producto['sku']}"];
            }
        }
        
        return [
            'valido' => empty($errores),
            'errores' => $errores
        ];
    }

    /**
     * Agrupa productos por combinación de sucursal origen-destino
     */
    private function agruparPorSucursales(array $productos): array
    {
        $grupos = [];

        foreach ($productos as $producto) {
            // Buscar las sucursales para obtener sus IDs
            $sucursalOrigen = $this->buscarSucursal($producto['origen']);
            $sucursalDestino = $this->buscarSucursal($producto['destino']);

            if (!$sucursalOrigen || !$sucursalDestino) {
                error_log("ERROR: No se pudieron encontrar sucursales - Origen: {$producto['origen']}, Destino: {$producto['destino']}");
                continue; // Saltar este producto si no se encuentran las sucursales
            }

            $key = $sucursalOrigen->getIdsucursal() . '->' . $sucursalDestino->getIdsucursal();

            if (!isset($grupos[$key])) {
                $grupos[$key] = [
                    'sucursal_origen_id' => $sucursalOrigen->getIdsucursal(),
                    'sucursal_destino_id' => $sucursalDestino->getIdsucursal(),
                    'sucursal_origen_nombre' => $sucursalOrigen->getNombre(),
                    'sucursal_destino_nombre' => $sucursalDestino->getNombre(),
                    'productos' => []
                ];
            }

            $grupos[$key]['productos'][] = $producto;
        }

        return $grupos;
    }

    /**
     * Valida que existe stock suficiente para todos los productos
     */
    private function validarStockMasivo(array $gruposPorSucursales): array
    {
        $errores = [];

        foreach ($gruposPorSucursales as $grupo) {
            // Buscar sucursal origen (por nombre o ID)
            $sucursalOrigen = $this->buscarSucursal($grupo['sucursal_origen_id']);
            if (!$sucursalOrigen) {
                $sugerencias = $this->obtenerSugerenciasSucursales($grupo['sucursal_origen_id']);
                $mensaje = "Sucursal origen '{$grupo['sucursal_origen_id']}' no existe";
                if (!empty($sugerencias)) {
                    $mensaje .= ". Sugerencias: " . implode(', ', $sugerencias);
                }
                $errores[] = ['codigo' => 'SUCURSAL_ORIGEN_NO_EXISTE', 'mensaje' => $mensaje];
                continue;
            }

            // Buscar sucursal destino (por nombre o ID)
            $sucursalDestino = $this->buscarSucursal($grupo['sucursal_destino_id']);
            if (!$sucursalDestino) {
                $sugerencias = $this->obtenerSugerenciasSucursales($grupo['sucursal_destino_id']);
                $mensaje = "Sucursal destino '{$grupo['sucursal_destino_id']}' no existe";
                if (!empty($sugerencias)) {
                    $mensaje .= ". Sugerencias: " . implode(', ', $sugerencias);
                }
                $errores[] = ['codigo' => 'SUCURSAL_DESTINO_NO_EXISTE', 'mensaje' => $mensaje];
                continue;
            }

            $sucursalOrigenId = $sucursalOrigen->getIdsucursal();
            
            // Validar stock para cada producto del grupo
            foreach ($grupo['productos'] as $producto) {
                $stockValidacion = $this->validarStockProducto($producto['sku'], $sucursalOrigenId, $producto['cantidad']);
                if (!$stockValidacion['valido']) {
                    $errores = array_merge($errores, $stockValidacion['errores']);
                }
            }
        }
        
        return [
            'valido' => empty($errores),
            'errores' => $errores
        ];
    }

    /**
     * Valida stock de un producto específico
     */
    private function validarStockProducto(string $sku, int $sucursalId, int $cantidadRequerida): array
    {
        $query = $this->em->createQuery(
            'SELECT s FROM App\Entity\Stock s
             INNER JOIN s.productoIdproducto p
             WHERE s.status = :status
             AND s.sucursalIdsucursal = :sucursal
             AND (s.codigobarras = :sku OR p.codigobarrasuniversal = :sku)
             AND s.cantidad >= :cantidad'
        )->setParameters([
            'status' => '1',
            'sucursal' => $sucursalId,
            'sku' => $sku,
            'cantidad' => $cantidadRequerida,
        ]);

        $stock = $query->getOneOrNullResult();

        if (!$stock) {
            // Buscar si el producto existe en alguna forma para dar mejor feedback
            $debugInfo = $this->buscarProductoParaDebug($sku, $sucursalId);
            $mensajeError = "SKU {$sku}: Stock insuficiente en sucursal {$sucursalId} (requerido: {$cantidadRequerida})";

            if (!empty($debugInfo)) {
                $mensajeError .= ". " . $debugInfo;
            }

            return [
                'valido' => false,
                'errores' => [['codigo' => 'STOCK_INSUFICIENTE', 'mensaje' => $mensajeError]]
            ];
        }

        return ['valido' => true, 'errores' => []];
    }

    /**
     * Crea un traspaso para un grupo de productos con la misma combinación origen-destino
     */
    private function crearTraspaso(array $grupo, Usuario $usuario, string $notas, bool $modoEmergencia = false): Transpasoalmacen
    {
        // Obtener sucursales por ID (ya vienen como IDs desde agruparPorSucursales)
        $sucursalOrigen = $this->em->getRepository(Sucursal::class)->find($grupo['sucursal_origen_id']);
        $sucursalDestino = $this->em->getRepository(Sucursal::class)->find($grupo['sucursal_destino_id']);
        
        // Crear el traspaso principal
        $traspaso = new Transpasoalmacen();
        $traspaso->setCreacion(new \DateTime("now"));
        $traspaso->setModificacion(new \DateTime("now"));
        $traspaso->setEstado("0"); // Pendiente
        $traspaso->setSucursalIdsucursalorigen($sucursalOrigen);
        $traspaso->setSucursalIdsucursaldestino($sucursalDestino);
        $traspaso->setUsuarioIdusuario($usuario);
        $notasCompletas = $notas . " (Traspaso masivo desde Google Sheets)";
        if ($modoEmergencia) {
            $notasCompletas .= " - MODO EMERGENCIA";
        }
        $traspaso->setNotas($notasCompletas);
        
        $this->em->persist($traspaso);
        $this->em->flush(); // Para obtener el ID
        
        // Procesar cada producto del grupo
        error_log("=== PROCESANDO PRODUCTOS DEL GRUPO ===");
        error_log("Total productos en grupo: " . count($grupo['productos']));

        foreach ($grupo['productos'] as $index => $producto) {
            error_log("Procesando producto $index: SKU {$producto['sku']}, Cantidad {$producto['cantidad']}");
            $this->procesarProductoTraspaso($producto, $traspaso, $modoEmergencia);
        }
        
        // Crear orden de salida
        $this->crearOrdenSalida($traspaso, $usuario, $modoEmergencia);

        // Flush para asegurar que se persistan productos y orden de salida
        $this->em->flush();
        error_log("Flush completado para traspaso ID: {$traspaso->getIdtranspasoalmacen()}");

        return $traspaso;
    }

    /**
     * Procesa un producto individual del traspaso
     */
    private function procesarProductoTraspaso(array $producto, Transpasoalmacen $traspaso, bool $modoEmergencia = false): void
    {
        error_log("=== PROCESANDO PRODUCTO INDIVIDUAL ===");
        error_log("SKU: {$producto['sku']}, Cantidad: {$producto['cantidad']}, Traspaso ID: {$traspaso->getIdtranspasoalmacen()}");

        if ($modoEmergencia) {
            error_log("MODO EMERGENCIA: Procesando producto {$producto['sku']} sin validar stock");

            // En modo emergencia, buscar stock sin validar cantidad
            $query = $this->em->createQuery(
                'SELECT s FROM App\Entity\Stock s
                 INNER JOIN s.productoIdproducto p
                 WHERE s.status = :status
                 AND s.sucursalIdsucursal = :sucursal
                 AND (s.codigobarras = :sku OR p.codigobarrasuniversal = :sku)
                 AND s.defective = :defective'
            )->setParameters([
                'status' => '1',
                'sucursal' => $traspaso->getSucursalIdsucursalorigen()->getIdsucursal(),
                'sku' => $producto['sku'],
                'defective' => 'DISPONIBLE'
            ]);
        } else {
            // Modo normal: validar que hay stock suficiente
            $query = $this->em->createQuery(
                'SELECT s FROM App\Entity\Stock s
                 INNER JOIN s.productoIdproducto p
                 WHERE s.status = :status
                 AND s.sucursalIdsucursal = :sucursal
                 AND (s.codigobarras = :sku OR p.codigobarrasuniversal = :sku)
                 AND s.cantidad >= :cantidad
                 AND s.defective = :defective'
            )->setParameters([
                'status' => '1',
                'sucursal' => $traspaso->getSucursalIdsucursalorigen()->getIdsucursal(),
                'sku' => $producto['sku'],
                'cantidad' => $producto['cantidad'],
                'defective' => 'DISPONIBLE'
            ]);
        }

        $stock = $query->setMaxResults(1)->getOneOrNullResult();

        if ($stock) {
            // Validar que el stock tiene una relación válida con producto
            if (!$stock->getProductoIdproducto()) {
                error_log("ERROR: Stock ID {$stock->getIdstock()} no tiene producto asociado válido para SKU {$producto['sku']}");
                return; // Saltar este producto
            }
            // CORRECCIÓN: NO reducir stock aquí - se hará al aceptar la orden de salida
            error_log("Stock encontrado para SKU {$producto['sku']} - ID: {$stock->getIdstock()}, Cantidad disponible: {$stock->getCantidad()}, Cantidad solicitada: {$producto['cantidad']}");

            if ($modoEmergencia) {
                error_log("MODO EMERGENCIA: Stock será movido al aceptar la orden de salida (sin validaciones previas)");
            } else {
                error_log("MODO NORMAL: Stock será validado y movido al aceptar la orden de salida");
            }

            // NO persistir cambios en stock aquí - mantener cantidad original para que esté disponible al aceptar la orden
            
            // Crear registro de producto en traspaso
            $productoTraspaso = new Productostranspasoalmacen();
            $productoTraspaso->setCantidad($producto['cantidad']);
            $productoTraspaso->setStatus('1');
            $productoTraspaso->setAceptada('0'); // Pendiente
            $productoTraspaso->setStockIdstock($stock);
            $productoTraspaso->setTranspasoalmacenIdtranspasoalmacen($traspaso);

            error_log("Creando ProductoTraspaso: SKU {$producto['sku']}, Cantidad {$producto['cantidad']}, Stock ID: {$stock->getIdstock()}, Producto ID: {$stock->getProductoIdproducto()->getIdproducto()}");
            error_log("IMPORTANTE: Stock NO reducido aquí - se mantendrá disponible para validación al aceptar orden de salida");

            $this->em->persist($productoTraspaso);
            error_log("ProductoTraspaso persistido en memoria para SKU: {$producto['sku']}");
        } else {
            error_log("ERROR: No se encontró stock válido para SKU: {$producto['sku']} en sucursal {$traspaso->getSucursalIdsucursalorigen()->getIdsucursal()}");
        }
    }

    /**
     * Crea la orden de salida para el traspaso
     */
    private function crearOrdenSalida(Transpasoalmacen $traspaso, Usuario $usuario, bool $modoEmergencia = false): void
    {
        $ordenSalida = new Ordensalida();
        $fechaCreacion = new \DateTime("now");
        $ordenSalida->setCreacion($fechaCreacion);
        $ordenSalida->setStatus('1');
        $ordenSalida->setAceptada('2'); // Pendiente
        $ordenSalida->setTranspasoalmacenIdtranspasoalmacen($traspaso);
        $ordenSalida->setResponsablesalida($usuario);

        // Si es modo de emergencia, agregar información especial en las notas
        if ($modoEmergencia) {
            // Nota corta para el campo de 45 caracteres
            $notaCorta = sprintf(
                "🚨EMERGENCIA %s %s",
                $usuario->getUsername(),
                $fechaCreacion->format('d/m H:i')
            );

            // Truncar si es necesario para que quepa en 45 caracteres
            if (strlen($notaCorta) > 45) {
                $notaCorta = substr($notaCorta, 0, 42) . '...';
            }

            $ordenSalida->setNota($notaCorta);

            error_log("ORDEN DE SALIDA DE EMERGENCIA creada:");
            error_log("- Usuario: {$usuario->getUsername()}");
            error_log("- Fecha: {$fechaCreacion->format('Y-m-d H:i:s')}");
            error_log("- Nota: $notaCorta");
        }

        $this->em->persist($ordenSalida);
        error_log("OrdenSalida persistida en memoria para traspaso ID: {$traspaso->getIdtranspasoalmacen()}");
    }

    /**
     * Busca una sucursal por nombre o ID con búsqueda flexible
     */
    private function buscarSucursal($identificador): ?Sucursal
    {
        // Si es numérico, buscar por ID
        if (is_numeric($identificador)) {
            return $this->em->getRepository(Sucursal::class)->find($identificador);
        }

        // Normalizar el identificador para búsqueda flexible
        $identificadorNormalizado = $this->normalizarTexto($identificador);

        // Log para debugging
        error_log("Buscando sucursal: '$identificador' -> normalizado: '$identificadorNormalizado'");

        // Test específico para casos problemáticos
        if (strtoupper($identificador) === 'ALMACEN3') {
            error_log("TEST: Normalizando 'ALMACÉN 3': '" . $this->normalizarTexto('ALMACÉN 3') . "'");
            error_log("TEST: ¿Coinciden? " . ($this->normalizarTexto('ALMACÉN 3') === $identificadorNormalizado ? 'SÍ' : 'NO'));
        }

        // Obtener todas las sucursales activas y buscar en PHP
        $sucursales = $this->em->getRepository(Sucursal::class)->createQueryBuilder('s')
            ->where('s.status = :status')
            ->setParameter('status', '1')
            ->getQuery()
            ->getResult();

        // Buscar coincidencia exacta normalizada
        foreach ($sucursales as $sucursal) {
            $nombreNormalizado = $this->normalizarTexto($sucursal->getNombre());
            error_log("Comparando: '$identificadorNormalizado' con '$nombreNormalizado' (original: '{$sucursal->getNombre()}')");

            if ($nombreNormalizado === $identificadorNormalizado) {
                error_log("Coincidencia exacta encontrada: '{$sucursal->getNombre()}'");
                return $sucursal;
            }
        }

        // Si no encuentra coincidencia exacta, buscar por coincidencia parcial
        foreach ($sucursales as $sucursal) {
            $nombreNormalizado = $this->normalizarTexto($sucursal->getNombre());

            // Búsqueda bidireccional más flexible
            if (strpos($nombreNormalizado, $identificadorNormalizado) !== false ||
                strpos($identificadorNormalizado, $nombreNormalizado) !== false) {
                error_log("Coincidencia parcial encontrada: '{$sucursal->getNombre()}'");
                return $sucursal;
            }

            // Búsqueda adicional: verificar si el identificador contiene partes del nombre
            $partesNombre = explode(' ', strtolower($sucursal->getNombre()));
            $coincidencias = 0;
            foreach ($partesNombre as $parte) {
                $parteNormalizada = $this->normalizarTexto($parte);
                if (!empty($parteNormalizada) && strpos($identificadorNormalizado, $parteNormalizada) !== false) {
                    $coincidencias++;
                }
            }

            // Si coincide con al menos la mitad de las partes del nombre
            if ($coincidencias > 0 && $coincidencias >= (count($partesNombre) / 2)) {
                error_log("Coincidencia por partes encontrada: '{$sucursal->getNombre()}' (coincidencias: $coincidencias/" . count($partesNombre) . ")");
                return $sucursal;
            }
        }

        error_log("No se encontró sucursal para: '$identificador'");
        return null;
    }

    /**
     * Normaliza texto para búsqueda flexible
     */
    private function normalizarTexto(string $texto): string
    {
        $original = $texto;

        // Convertir a minúsculas
        $texto = strtolower(trim($texto));

        // Remover acentos ANTES de remover espacios
        $texto = $this->removerAcentos($texto);

        // Remover espacios, guiones, guiones bajos y otros caracteres especiales
        $texto = str_replace([' ', '_', '-', '.', ',', ':', ';', '(', ')', '[', ']'], '', $texto);

        // Remover caracteres no alfanuméricos restantes
        $texto = preg_replace('/[^a-z0-9]/', '', $texto);

        error_log("Normalización: '$original' -> '$texto'");

        return $texto;
    }

    /**
     * Remueve acentos y caracteres especiales
     */
    private function removerAcentos(string $texto): string
    {
        // Mapeo completo de acentos y caracteres especiales
        $acentos = [
            // Vocales con acentos
            'á' => 'a', 'à' => 'a', 'ä' => 'a', 'â' => 'a', 'ā' => 'a', 'ã' => 'a', 'å' => 'a',
            'é' => 'e', 'è' => 'e', 'ë' => 'e', 'ê' => 'e', 'ē' => 'e',
            'í' => 'i', 'ì' => 'i', 'ï' => 'i', 'î' => 'i', 'ī' => 'i',
            'ó' => 'o', 'ò' => 'o', 'ö' => 'o', 'ô' => 'o', 'ō' => 'o', 'õ' => 'o', 'ø' => 'o',
            'ú' => 'u', 'ù' => 'u', 'ü' => 'u', 'û' => 'u', 'ū' => 'u',
            // Consonantes especiales
            'ñ' => 'n', 'ç' => 'c', 'ß' => 's',
            // Mayúsculas
            'Á' => 'a', 'À' => 'a', 'Ä' => 'a', 'Â' => 'a', 'Ā' => 'a', 'Ã' => 'a', 'Å' => 'a',
            'É' => 'e', 'È' => 'e', 'Ë' => 'e', 'Ê' => 'e', 'Ē' => 'e',
            'Í' => 'i', 'Ì' => 'i', 'Ï' => 'i', 'Î' => 'i', 'Ī' => 'i',
            'Ó' => 'o', 'Ò' => 'o', 'Ö' => 'o', 'Ô' => 'o', 'Ō' => 'o', 'Õ' => 'o', 'Ø' => 'o',
            'Ú' => 'u', 'Ù' => 'u', 'Ü' => 'u', 'Û' => 'u', 'Ū' => 'u',
            'Ñ' => 'n', 'Ç' => 'c'
        ];

        // Aplicar mapeo de acentos
        $resultado = strtr($texto, $acentos);

        // Usar iconv como respaldo si está disponible
        if (function_exists('iconv')) {
            $iconv_result = iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $resultado);
            if ($iconv_result !== false) {
                $resultado = $iconv_result;
            }
        }

        return $resultado;
    }

    /**
     * Obtiene sugerencias de sucursales similares
     */
    private function obtenerSugerenciasSucursales(string $busqueda): array
    {
        $busquedaNormalizada = $this->normalizarTexto($busqueda);

        // Obtener todas las sucursales activas
        $sucursales = $this->em->getRepository(Sucursal::class)->createQueryBuilder('s')
            ->where('s.status = :status')
            ->setParameter('status', '1')
            ->orderBy('s.nombre', 'ASC')
            ->getQuery()
            ->getResult();

        $sugerencias = [];

        // Buscar sucursales que contengan parte del texto buscado
        foreach ($sucursales as $sucursal) {
            $nombreNormalizado = $this->normalizarTexto($sucursal->getNombre());
            if (strpos($nombreNormalizado, $busquedaNormalizada) !== false) {
                $sugerencias[] = $sucursal->getNombre();
                if (count($sugerencias) >= 3) {
                    break;
                }
            }
        }

        // Si no encuentra sugerencias por coincidencia parcial, mostrar las primeras 3 sucursales
        if (empty($sugerencias)) {
            $contador = 0;
            foreach ($sucursales as $sucursal) {
                $sugerencias[] = $sucursal->getNombre();
                $contador++;
                if ($contador >= 3) {
                    break;
                }
            }
        }

        return $sugerencias;
    }

    /**
     * Busca información de debug cuando un producto no se encuentra
     */
    private function buscarProductoParaDebug(string $sku, int $sucursalId): string
    {
        $info = [];

        // Buscar si existe el producto en stock pero sin cantidad suficiente
        $queryStockInsuficiente = $this->em->createQuery(
            'SELECT s.cantidad FROM App\Entity\Stock s
             INNER JOIN s.productoIdproducto p
             WHERE s.status = :status
             AND s.sucursalIdsucursal = :sucursal
             AND (s.codigobarras = :sku OR p.codigobarrasuniversal = :sku)
             AND s.defective = :defective'
        )->setParameters([
            'status' => '1',
            'sucursal' => $sucursalId,
            'sku' => $sku,
            'defective' => 'DISPONIBLE'
        ]);

        $stockInsuficiente = $queryStockInsuficiente->getOneOrNullResult();
        if ($stockInsuficiente) {
            $info[] = "Producto encontrado pero cantidad disponible: {$stockInsuficiente['cantidad']}";
        }

        // Buscar si existe en otras sucursales
        $queryOtrasSucursales = $this->em->createQuery(
            'SELECT suc.nombre, s.cantidad FROM App\Entity\Stock s
             INNER JOIN s.productoIdproducto p
             INNER JOIN s.sucursalIdsucursal suc
             WHERE s.status = :status
             AND (s.codigobarras = :sku OR p.codigobarrasuniversal = :sku)
             AND s.cantidad > 0'
        )->setParameters([
            'status' => '1',
            'sku' => $sku,
        ])->setMaxResults(3);

        $stockOtrasSucursales = $queryOtrasSucursales->getResult();
        if (!empty($stockOtrasSucursales)) {
            $sucursalesDisponibles = array_map(function($item) {
                return "{$item['nombre']} ({$item['cantidad']})";
            }, $stockOtrasSucursales);
            $info[] = "Disponible en: " . implode(', ', $sucursalesDisponibles);
        }

        // Si no se encuentra en ningún lado
        if (empty($stockInsuficiente) && empty($stockOtrasSucursales)) {
            $info[] = "Producto no encontrado en el sistema";
        }

        return implode('. ', $info);
    }
}
