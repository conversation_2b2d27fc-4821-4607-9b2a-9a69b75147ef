<?php

namespace App\Controller;

use App\Entity\Ordenlaboratorio;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controlador para mostrar información de órdenes sin autenticación
 * Usado para códigos QR
 */
class OrdenExternalController extends AbstractController
{
    private $entityManager;

    public function __construct(EntityManagerInterface $entityManager)
    {
        $this->entityManager = $entityManager;
    }

    /**
     * @Route("/orden/external/info/{idOrden}", name="orden_external_info", methods={"GET"})
     */
    public function mostrarInfoOrden(int $idOrden): Response
    {
        try {
            // Buscar la orden de laboratorio
            $orden = $this->entityManager->getRepository(Ordenlaboratorio::class)->find($idOrden);

            if (!$orden) {
                return $this->render('orden_external/no_encontrada.html.twig', [
                    'idOrden' => $idOrden
                ]);
            }

            // Obtener información relacionada
            $venta = $orden->getVentaIdventa();
            $cliente = $venta ? $venta->getClienteIdcliente() : null;
            $sucursal = $venta ? $venta->getSucursalIdsucursal() : null;

            return $this->render('orden_external/info.html.twig', [
                'orden' => $orden,
                'venta' => $venta,
                'cliente' => $cliente,
                'sucursal' => $sucursal,
                'folio' => $venta ? $venta->getFolio() : 'N/A'
            ]);

        } catch (\Exception $e) {
            return $this->render('orden_external/error.html.twig', [
                'error' => 'Error al cargar la información de la orden',
                'idOrden' => $idOrden
            ]);
        }
    }

    /**
     * @Route("/orden/external/estado/{idOrden}", name="orden_external_estado", methods={"GET"})
     */
    public function obtenerEstadoOrden(int $idOrden): Response
    {
        try {
            $orden = $this->entityManager->getRepository(Ordenlaboratorio::class)->find($idOrden);

            if (!$orden) {
                return $this->json([
                    'success' => false,
                    'message' => 'Orden no encontrada'
                ], 404);
            }

            $venta = $orden->getVentaIdventa();

            return $this->json([
                'success' => true,
                'data' => [
                    'idOrden' => $orden->getIdordenlaboratorio(),
                    'estado' => $orden->getEstado(),
                    'folio' => $venta ? $venta->getFolio() : null,
                    'fechaCreacion' => $orden->getFechacreacion() ? $orden->getFechacreacion()->format('d/m/Y H:i') : null,
                    'fechaEntrega' => $orden->getFechaentrega() ? $orden->getFechaentrega()->format('d/m/Y') : null,
                    'observaciones' => $orden->getObservaciones()
                ]
            ]);

        } catch (\Exception $e) {
            return $this->json([
                'success' => false,
                'message' => 'Error al obtener el estado de la orden'
            ], 500);
        }
    }
}
