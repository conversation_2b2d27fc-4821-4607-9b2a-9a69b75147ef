<?php

namespace App\Controller;

use App\Entity\Tipoventa;
use App\Entity\Paymenttype;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;
use Doctrine\ORM\EntityManagerInterface;
use App\Entity\Empresa;
use App\Entity\Sucursal;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\JsonResponse;
use App\Entity\Clase;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\Security;
use Psr\Log\LoggerInterface;

use App\Entity\Stock;
use App\Entity\Producto;
use App\Entity\Productostranspasoalmacen;

/**
 * Controlador para el módulo de Ventas Generales
 *
 * Este controlador proporciona una interfaz para que los super administradores
 * puedan visualizar y filtrar información de ventas por empresa y clase de productos.
 * A diferencia del OficialController, este controlador está diseñado para trabajar
 * con todas las empresas y sucursales sin restricciones.
 */
class VentasGeneralesController extends AbstractController
{
    private $logger;

    /**
     * Página principal del reporte de ventas generales
     *
     * @Route("/reporte/ventas-generales", name="app_ventas_generales")
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     *
     * @param EntityManagerInterface $em Gestor de entidades para consultas a la base de datos
     * @return Response Respuesta HTTP con la vista renderizada
     */
    public function index(EntityManagerInterface $em): Response
    {
        // Obtener todas las empresas disponibles en el sistema
        $empresas = $em->getRepository(Empresa::class)->findAll();

        // Los tipos de venta ahora se cargan desde FilterController
        // Obtener tipos de venta usando el FilterController centralizado
        $tiposVenta = $em->getRepository(Tipoventa::class)->findBy(['status' => '1'], ['nombre' => 'ASC']);

        // Logging para debug
        error_log("=== CARGA INICIAL VENTAS GENERALES ===");
        error_log("Empresas encontradas: " . count($empresas));
        error_log("Tipos de venta encontrados: " . count($tiposVenta));
        error_log("Nota: Los filtros ahora usan FilterController centralizado");

        if (count($tiposVenta) > 0) {
            error_log("Primer tipo de venta: " . $tiposVenta[0]->getNombre());
        }

        // Renderizar la plantilla con los datos necesarios
        return $this->render('ventas_generales/index.html.twig', [
            'controller_name' => 'VentasGeneralesController',
            'empresas' => $empresas,
            'tipos_venta' => $tiposVenta,
            'is_supervisor' => false,
            'is_hardcoded' => false,
        ]);
    }

    /**
     * Obtiene las sucursales asociadas a una empresa específica
     *
     * Este método es llamado vía AJAX cuando se selecciona una empresa en la interfaz
     *
     * @Route("/ajax/sucursales-generales", name="ventas-generales-obtener-sucursales")
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     *
     * @param Request $request Objeto de solicitud HTTP
     * @param EntityManagerInterface $em Gestor de entidades para consultas a la base de datos
     * @return Response Respuesta HTTP con la vista parcial renderizada
     */
    public function obtenerSucursales(Request $request, EntityManagerInterface $em): Response
    {
        try {
            // Obtener el ID de empresa desde la solicitud
            $idempresa = $request->query->get('idempresa');

            // Log para debugging
            error_log("VentasGeneralesController::obtenerSucursales - ID Empresa: " . $idempresa);

            // Validar que el ID de empresa sea un valor numérico
            if (!is_numeric($idempresa) || $idempresa <= 0) {
                error_log("VentasGeneralesController::obtenerSucursales - ID de empresa inválido: " . $idempresa);
                throw $this->createNotFoundException('ID de empresa inválido');
            }

            // Buscar todas las sucursales activas asociadas a la empresa seleccionada
            $sucursales = $em->getRepository(Sucursal::class)->findBy(
                ['empresaIdempresa' => $idempresa, 'status' => '1'],
                ['tipo' => 'ASC', 'nombre' => 'ASC'] // Ordenar por tipo primero, luego por nombre
            );

            // Log para debugging
            error_log("VentasGeneralesController::obtenerSucursales - Sucursales encontradas: " . count($sucursales));

            // Agrupar sucursales por tipo
            $sucursalesPorTipo = [];
            $contadorTipos = [];

            foreach ($sucursales as $sucursal) {
                $tipo = $sucursal->getTipo() ?? 'sin_tipo'; // Manejar casos donde tipo sea null

                if (!isset($sucursalesPorTipo[$tipo])) {
                    $sucursalesPorTipo[$tipo] = [];
                    $contadorTipos[$tipo] = 0;
                }

                $sucursalesPorTipo[$tipo][] = $sucursal;
                $contadorTipos[$tipo]++;
            }

            // Log de agrupación
            foreach ($contadorTipos as $tipo => $cantidad) {
                error_log("VentasGeneralesController::obtenerSucursales - Tipo '$tipo': $cantidad sucursales");
            }

            // Renderizar la plantilla parcial del wizard con las sucursales agrupadas
            return $this->render('ventas_generales/sucursales_wizard.html.twig', [
                'sucursalesPorTipo' => $sucursalesPorTipo,
                'contadorTipos' => $contadorTipos,
                'totalSucursales' => count($sucursales)
            ]);

        } catch (\Exception $e) {
            error_log("VentasGeneralesController::obtenerSucursales - Error: " . $e->getMessage());

            return $this->render('ventas_generales/sucursales_wizard.html.twig', [
                'sucursales' => [],
                'error' => 'Error al cargar las sucursales: ' . $e->getMessage()
            ]);
        }
    }

    /**
     * Genera el reporte de ventas generales según los filtros seleccionados
     *
     * @Route("/ajax/generar-reporte-ventas-generales", name="ventas-generales-generar-reporte")
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     *
     * @param Request $request Objeto de solicitud HTTP
     * @param EntityManagerInterface $em Gestor de entidades para consultas a la base de datos
     * @return JsonResponse Respuesta JSON con los datos del reporte
     */
    public function generarReporte(Request $request, EntityManagerInterface $em): JsonResponse
    {
        // Validar parámetros
        $idempresa = $request->request->get('idempresa');
        $sucursales = $request->request->get('sucursales', []);
        $clases = $request->request->get('clases', []);
        $fechaInicio = $request->request->get('fecha_inicio');
        $fechaFin = $request->request->get('fecha_fin');

        // Validaciones mejoradas
        if (!is_numeric($idempresa)) {
            return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
        }

        // Validar fechas
        try {
            $fechaInicioObj = new \DateTime($fechaInicio);
            $fechaFinObj = new \DateTime($fechaFin);

            if ($fechaInicioObj > $fechaFinObj) {
                return new JsonResponse(['error' => 'La fecha de inicio no puede ser mayor a la fecha fin'], 400);
            }
        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Formato de fecha inválido'], 400);
        }

        // Consulta real para obtener datos de ventas (excluir cotizaciones)
        $qb = $em->createQueryBuilder();
        $qb->select('
            SUM(v.total) as ventas_total,
            SUM(CASE WHEN p.monto IS NOT NULL THEN p.monto ELSE 0 END) as pagos_total,
            COUNT(v.idventa) as total_ventas,
            AVG(v.total) as promedio_venta
        ')
            ->from('App\Entity\Venta', 'v')
            ->leftJoin('App\Entity\Pago', 'p', 'WITH', 'p.ventaIdventa = v.idventa')
            ->leftJoin('v.sucursalIdsucursal', 's')
            ->where('v.cotizacion != :cotizacion')
            ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
            ->setParameter('cotizacion', '1')
            ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
            ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));

        // Filtrar por empresa si se especifica
        if ($idempresa) {
            $qb->andWhere('s.empresaIdempresa = :empresa')
               ->setParameter('empresa', $idempresa);
        }

        // Filtrar por sucursales si se especifican
        if (!empty($sucursales)) {
            $qb->andWhere('s.idsucursal IN (:sucursales)')
                ->setParameter('sucursales', $sucursales);
        }

        // Filtrar por clases si se especifican
        if (!empty($clases)) {
            $qb->leftJoin('App\Entity\Productoventa', 'pv', 'WITH', 'pv.ventaIdventa = v.idventa')
                ->leftJoin('App\Entity\Producto', 'prod', 'WITH', 'prod.idproducto = pv.productoIdproducto')
                ->andWhere('prod.claseIdclase IN (:clases)')
                ->setParameter('clases', $clases);
        }

        $resultado = $qb->getQuery()->getSingleResult();

        $ventasTotal = $resultado['ventas_total'] ?? 0;
        $pagosTotal = $resultado['pagos_total'] ?? 0;

        return new JsonResponse([
            'success' => true,
            'datos' => [
                'ventas_total' => number_format($ventasTotal, 2),
                'pagos_total' => number_format($pagosTotal, 2),
                'por_cobrar_total' => number_format($ventasTotal - $pagosTotal, 2),
                'total_ventas' => $resultado['total_ventas'],
                'promedio_venta' => number_format($resultado['promedio_venta'] ?? 0, 2)
            ]
        ]);
    }

    /**
     * Obtiene métricas específicas para un período determinado
     *
     * @Route("/ajax/metricas-ventas", name="ventas-generales-metricas", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     *
     * @param Request $request Objeto de solicitud HTTP
     * @param EntityManagerInterface $em Gestor de entidades para consultas a la base de datos
     * @return JsonResponse Respuesta JSON con las métricas
     */
    public function obtenerMetricas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa'); // Opcional - si no se pasa, todas las empresas
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');
            $sucursales = $request->query->get('sucursales', []);
            $tipoVenta = $request->query->get('tipo_venta'); // Nuevo parámetro

            // Logging para debug
            error_log("=== DEBUG MÉTRICAS ===");
            error_log("Empresa ID: " . ($idempresa ?: 'TODAS'));
            error_log("Fecha Inicio: " . $fechaInicio);
            error_log("Fecha Fin: " . $fechaFin);
            error_log("Tipo Venta: " . ($tipoVenta ?: 'TODOS'));
            error_log("Sucursales: " . json_encode($sucursales));

            // Validaciones (empresa es opcional ahora)
            if ($idempresa && !is_numeric($idempresa)) {
                return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
            }

            // Si no se especifican fechas, usar el día actual
            if (!$fechaInicio || !$fechaFin) {
                $hoy = new \DateTime();
                $fechaInicio = $hoy->format('Y-m-d');
                $fechaFin = $hoy->format('Y-m-d');
            }

            // Validar fechas
            try {
                $fechaInicioObj = new \DateTime($fechaInicio);
                $fechaFinObj = new \DateTime($fechaFin);
            } catch (\Exception $e) {
                return new JsonResponse(['error' => 'Formato de fecha inválido'], 400);
            }

            // Debug: Verificar si hay ventas en general (excluyendo cotizaciones)
            $countQuery = $em->createQueryBuilder()
                ->select('COUNT(v.idventa)')
                ->from('App\Entity\Venta', 'v')
                ->leftJoin('v.sucursalIdsucursal', 's')
                ->where('v.cotizacion != :cotizacion')
                ->setParameter('cotizacion', '1');

            if ($idempresa) {
                $countQuery->andWhere('s.empresaIdempresa = :empresa')
                          ->setParameter('empresa', $idempresa);
            }

            $totalVentas = $countQuery->getQuery()->getSingleScalarResult();
            $empresaTexto = $idempresa ? "empresa $idempresa" : "todas las empresas";
            error_log("Total de ventas para $empresaTexto: $totalVentas");

            // Consulta para obtener métricas de ventas (excluir cotizaciones)
            $qb = $em->createQueryBuilder();
            $qb->select('
                SUM(v.pagado) as ventas_total,
                SUM(CASE WHEN p.monto IS NOT NULL THEN p.monto ELSE 0 END) as pagos_total,
                COUNT(DISTINCT v.idventa) as total_ventas,
                AVG(v.pagado) as promedio_venta
            ')
                ->from('App\Entity\Venta', 'v')
                ->leftJoin('App\Entity\Pago', 'p', 'WITH', 'p.ventaIdventa = v.idventa')
                ->leftJoin('v.sucursalIdsucursal', 's')
                ->where('v.cotizacion != :cotizacion')
                ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                ->setParameter('cotizacion', '1')
                ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
                ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));

            // Filtrar por empresa si se especifica
            if ($idempresa) {
                $qb->andWhere('s.empresaIdempresa = :empresa')
                   ->setParameter('empresa', $idempresa);
            }

            // Filtrar por tipo de venta si se especifica
            if ($tipoVenta) {
                $qb->leftJoin('v.tipoventaIdtipoventa', 'tv')
                   ->andWhere('tv.idtipoventa = :tipoVenta')
                   ->setParameter('tipoVenta', $tipoVenta);
            }

            // Filtrar por sucursales si se especifican
            if (!empty($sucursales) && is_array($sucursales)) {
                $qb->andWhere('s.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }

            // Logging del query generado
            $query = $qb->getQuery();
            error_log("Query DQL: " . $query->getDQL());
            error_log("Parámetros: " . json_encode($query->getParameters()->toArray()));

            $resultado = $query->getSingleResult();

            error_log("Resultado query: " . json_encode($resultado));

            $ventasTotal = floatval($resultado['ventas_total'] ?? 0);
            $pagosTotal = floatval($resultado['pagos_total'] ?? 0);
            $porCobrar = $ventasTotal - $pagosTotal;

            error_log("Ventas Total: $ventasTotal, Pagos Total: $pagosTotal, Por Cobrar: $porCobrar");

            // Calcular tendencias comparando con período anterior
            $tendencias = $this->calcularTendencias($em, $idempresa, $fechaInicioObj, $fechaFinObj, $sucursales);

            return new JsonResponse([
                'success' => true,
                'datos' => [
                    'ventas_total' => $ventasTotal,
                    'pagos_total' => $pagosTotal,
                    'por_cobrar_total' => $porCobrar,
                    'total_ventas' => intval($resultado['total_ventas'] ?? 0),
                    'promedio_venta' => floatval($resultado['promedio_venta'] ?? 0),
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin,
                    'tendencias' => $tendencias
                ]
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error al obtener métricas: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Calcula tendencias comparando con el período anterior
     */
    private function calcularTendencias(EntityManagerInterface $em, $idempresa, \DateTime $fechaInicio, \DateTime $fechaFin, $sucursales = []): array
    {
        try {
            // Calcular duración del período
            $duracion = $fechaInicio->diff($fechaFin)->days + 1;

            // Calcular fechas del período anterior
            $fechaInicioAnterior = clone $fechaInicio;
            $fechaFinAnterior = clone $fechaInicio;
            $fechaInicioAnterior->sub(new \DateInterval('P' . $duracion . 'D'));
            $fechaFinAnterior->sub(new \DateInterval('P1D'));

            // Consulta para período anterior (excluir cotizaciones)
            $qb = $em->createQueryBuilder();
            $qb->select('
                SUM(v.total) as ventas_total,
                SUM(CASE WHEN p.monto IS NOT NULL THEN p.monto ELSE 0 END) as pagos_total
            ')
                ->from('App\Entity\Venta', 'v')
                ->leftJoin('App\Entity\Pago', 'p', 'WITH', 'p.ventaIdventa = v.idventa')
                ->leftJoin('v.sucursalIdsucursal', 's')
                ->where('v.cotizacion != :cotizacion')
                ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                ->setParameter('cotizacion', '1')
                ->setParameter('fechaInicio', $fechaInicioAnterior->setTime(0, 0, 0))
                ->setParameter('fechaFin', $fechaFinAnterior->setTime(23, 59, 59));

            // Filtrar por empresa si se especifica
            if ($idempresa) {
                $qb->andWhere('s.empresaIdempresa = :empresa')
                   ->setParameter('empresa', $idempresa);
            }

            if (!empty($sucursales) && is_array($sucursales)) {
                $qb->andWhere('s.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }

            $resultadoAnterior = $qb->getQuery()->getSingleResult();

            $ventasAnterior = floatval($resultadoAnterior['ventas_total'] ?? 0);
            $pagosAnterior = floatval($resultadoAnterior['pagos_total'] ?? 0);
            $porCobrarAnterior = $ventasAnterior - $pagosAnterior;

            // Calcular porcentajes de cambio (necesitamos los valores actuales para comparar)
            // Por ahora retornamos 0, se calculará correctamente cuando se llame con los valores actuales
            $ventasTrend = 0;
            $pagosTrend = 0;
            $deudaTrend = 0;

            return [
                'ventas' => round($ventasTrend, 1),
                'pagos' => round($pagosTrend, 1),
                'deuda' => round($deudaTrend, 1)
            ];

        } catch (\Exception $e) {
            // En caso de error, retornar tendencias neutras
            return [
                'ventas' => 0,
                'pagos' => 0,
                'deuda' => 0
            ];
        }
    }

    /**
     * Método AJAX para obtener datos de gráficas de ingresos diarios
     *
     * @Route("/ajax/graficas-ingresos-diarios", name="ventas-generales-graficas-ingresos", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerGraficasIngresos(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');

            // Validaciones
            if (!is_numeric($idempresa) || $idempresa <= 0) {
                return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
            }

            // TODO: Implementar consultas reales
            $datosEjemplo = [
                'ingresos_sucursal' => [
                    'labels' => ['Sucursal 1', 'Sucursal 2', 'Sucursal 3'],
                    'data' => [30000, 25000, 35000]
                ],
                'tipos_pago' => [
                    'labels' => ['Efectivo', 'Tarjeta', 'Transferencia'],
                    'data' => [44, 55, 13]
                ],
                'deuda_sucursal' => [
                    'labels' => ['Sucursal 1', 'Sucursal 2', 'Sucursal 3'],
                    'data' => [5000, 3000, 7000]
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'datos' => $datosEjemplo
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error interno del servidor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Método AJAX para obtener datos de ventas anuales
     *
     * @Route("/ajax/ventas-anuales", name="ventas-generales-ventas-anuales", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerVentasAnuales(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $year = $request->query->get('year', date('Y'));

            // Validaciones
            if (!is_numeric($idempresa) || $idempresa <= 0) {
                return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
            }

            // TODO: Implementar consultas reales
            $datosEjemplo = [
                'ventas_mensuales' => [
                    'labels' => ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                    'data' => [120000, 135000, 145000, 160000, 155000, 170000, 180000, 175000, 190000, 185000, 200000, 195000]
                ],
                'pagos_anuales' => [
                    'labels' => ['Sucursal 1', 'Sucursal 2', 'Sucursal 3'],
                    'data' => [44, 55, 13]
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'datos' => $datosEjemplo
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error interno del servidor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Método AJAX para obtener datos de productos y marcas
     *
     * @Route("/ajax/productos-marcas", name="ventas-generales-productos-marcas", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerProductosMarcas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');

            // Validaciones
            if (!is_numeric($idempresa) || $idempresa <= 0) {
                return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
            }

            // TODO: Implementar consultas reales
            $datosEjemplo = [
                'marcas' => [
                    'labels' => ['Toyota', 'Honda', 'Ford', 'Chevrolet'],
                    'data' => [15, 12, 8, 20]
                ],
                'tratamientos' => [
                    'labels' => ['Mantenimiento', 'Reparación', 'Diagnóstico'],
                    'data' => [35, 25, 20]
                ],
                'tabla_productos' => [
                    ['Toyota Corolla', 'Toyota', 'Sedán', '15', '$25,000'],
                    ['Honda Civic', 'Honda', 'Sedán', '12', '$22,000']
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'datos' => $datosEjemplo
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error interno del servidor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Método AJAX para obtener datos de facturación
     *
     * @Route("/ajax/datos-facturacion", name="ventas-generales-datos-facturacion", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerDatosFacturacion(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $year = $request->query->get('year', date('Y'));

            // Validaciones
            if (!is_numeric($idempresa) || $idempresa <= 0) {
                return new JsonResponse(['error' => 'ID de empresa inválido'], 400);
            }

            // TODO: Implementar consultas reales
            $datosEjemplo = [
                'facturacion_estado' => [
                    'labels' => ['Pagado', 'Pendiente', 'Vencido'],
                    'data' => [65, 25, 10]
                ],
                'total_facturado' => 750000,
                'facturacion_mensual' => [
                    'labels' => ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic'],
                    'data' => [80000, 95000, 110000, 125000, 140000, 135000, 150000, 145000, 160000, 155000, 170000, 165000]
                ]
            ];

            return new JsonResponse([
                'success' => true,
                'datos' => $datosEjemplo
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error interno del servidor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene datos para gráfica de ventas por sucursal
     *
     * @Route("/ajax/grafica-sucursales", name="ventas-generales-grafica-sucursales", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerDatosGraficaSucursales(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');
            $sucursales = $request->query->get('sucursales', []);
            $tipoVenta = $request->query->get('tipo_venta');

            // Validar fechas
            if (!$fechaInicio || !$fechaFin) {
                $hoy = new \DateTime();
                $fechaInicio = $hoy->format('Y-m-d');
                $fechaFin = $hoy->format('Y-m-d');
            }

            $fechaInicioObj = new \DateTime($fechaInicio);
            $fechaFinObj = new \DateTime($fechaFin);

            // Query para obtener ventas por sucursal
            $qb = $em->createQueryBuilder();
            $qb->select('
                s.nombre as sucursal_nombre,
                SUM(v.total) as total_ventas
            ')
                ->from('App\Entity\Venta', 'v')
                ->leftJoin('v.sucursalIdsucursal', 's')
                ->where('v.cotizacion != :cotizacion')
                ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                ->groupBy('s.idsucursal, s.nombre')
                ->orderBy('total_ventas', 'DESC')
                ->setParameter('cotizacion', '1')
                ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
                ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));

            // Filtrar por empresa si se especifica
            if ($idempresa) {
                $qb->andWhere('s.empresaIdempresa = :empresa')
                   ->setParameter('empresa', $idempresa);
            }

            // Filtrar por tipo de venta si se especifica
            if ($tipoVenta) {
                $qb->leftJoin('v.tipoventaIdtipoventa', 'tv')
                   ->andWhere('tv.idtipoventa = :tipoVenta')
                   ->setParameter('tipoVenta', $tipoVenta);
            }

            // Filtrar por sucursales específicas si se especifican
            if (!empty($sucursales) && is_array($sucursales)) {
                $qb->andWhere('s.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }

            $resultados = $qb->getQuery()->getResult();

            $categorias = [];
            $datos = [];

            foreach ($resultados as $resultado) {
                $categorias[] = $resultado['sucursal_nombre'];
                $datos[] = floatval($resultado['total_ventas']);
            }

            return new JsonResponse([
                'success' => true,
                'categorias' => $categorias,
                'datos' => $datos
            ]);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error al obtener datos de sucursales: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene datos para gráficas de productos y marcas
     *
     * @Route("/ajax/grafica-productos-marcas", name="ventas-generales-grafica-productos-marcas", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerDatosGraficaProductosMarcas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');
            $sucursales = $request->query->get('sucursales', []);
            $tipo = $request->query->get('tipo', 'productos'); // 'productos' o 'marcas'

            // Logging para debug
            error_log("=== DEBUG PRODUCTOS/MARCAS ===");
            error_log("Tipo: " . $tipo);
            error_log("Empresa ID: " . ($idempresa ?: 'TODAS'));
            error_log("Fecha Inicio: " . $fechaInicio);
            error_log("Fecha Fin: " . $fechaFin);

            // Validar fechas
            if (!$fechaInicio || !$fechaFin) {
                $hoy = new \DateTime();
                $fechaInicio = $hoy->format('Y-m-d');
                $fechaFin = $hoy->format('Y-m-d');
            }

            $fechaInicioObj = new \DateTime($fechaInicio);
            $fechaFinObj = new \DateTime($fechaFin);

            if ($tipo === 'marcas') {
                // Query para obtener ventas por marca (relación correcta: stockventa->stock->producto->marca)
                $qb = $em->createQueryBuilder();
                $qb->select('
                    m.nombre as nombre,
                    SUM(sv.cantidad) as cantidad_vendida,
                    SUM(sv.cantidad * sv.precio) as total_ventas
                ')
                    ->from('App\Entity\Stockventa', 'sv')
                    ->leftJoin('sv.ventaIdventa', 'v')
                    ->leftJoin('sv.stockIdstock', 'st')
                    ->leftJoin('st.productoIdproducto', 'p')
                    ->leftJoin('p.marcaIdmarca', 'm')
                    ->leftJoin('v.sucursalIdsucursal', 's')
                    ->where('v.cotizacion != :cotizacion')
                    ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                    ->andWhere('m.idmarca IS NOT NULL')
                    ->groupBy('m.idmarca, m.nombre')
                    ->orderBy('total_ventas', 'DESC')
                    ->setMaxResults(10) // Top 10 marcas
                    ->setParameter('cotizacion', '1')
                    ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
                    ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));
            } else {
                // Query para obtener registros individuales de stockventa (relación correcta)
                $qb = $em->createQueryBuilder();
                $qb->select('
                    p.nombre as nombre,
                    m.nombre as marca,
                    sv.cantidad as cantidad_vendida,
                    sv.precio as precio_unitario,
                    (sv.cantidad * sv.precio) as total_ventas
                ')
                    ->from('App\Entity\Stockventa', 'sv')
                    ->leftJoin('sv.ventaIdventa', 'v')
                    ->leftJoin('sv.stockIdstock', 'st')
                    ->leftJoin('st.productoIdproducto', 'p')
                    ->leftJoin('p.marcaIdmarca', 'm')
                    ->leftJoin('v.sucursalIdsucursal', 's')
                    ->where('v.cotizacion != :cotizacion')
                    ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                    ->orderBy('v.fechacreacion', 'DESC')
                    ->setMaxResults(100) // Últimos 100 registros
                    ->setParameter('cotizacion', '1')
                    ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
                    ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));
            }

            // Filtrar por empresa si se especifica
            if ($idempresa) {
                $qb->andWhere('s.empresaIdempresa = :empresa')
                   ->setParameter('empresa', $idempresa);
            }

            // Filtrar por sucursales específicas si se especifican
            if (!empty($sucursales) && is_array($sucursales)) {
                $qb->andWhere('s.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }

            // Logging del query generado
            $query = $qb->getQuery();
            error_log("=== QUERY " . strtoupper($tipo) . " ===");
            error_log("Query DQL: " . $query->getDQL());
            error_log("Parámetros: " . json_encode($query->getParameters()->toArray()));

            $resultados = $query->getResult();
            error_log("Resultados encontrados: " . count($resultados));

            if (count($resultados) > 0) {
                error_log("Primer resultado: " . json_encode($resultados[0]));
            }

            $categorias = [];
            $datos = [];
            $cantidades = [];
            $marcas = [];
            $precios = [];

            foreach ($resultados as $resultado) {
                $categorias[] = $resultado['nombre'];
                $datos[] = floatval($resultado['total_ventas']);
                $cantidades[] = intval($resultado['cantidad_vendida']);

                // Para productos individuales, agregar más datos
                if ($tipo === 'productos') {
                    $marcas[] = $resultado['marca'] ?? 'Sin marca';
                    $precios[] = floatval($resultado['precio_unitario'] ?? 0);
                }
            }

            $response = [
                'success' => true,
                'tipo' => $tipo,
                'categorias' => $categorias,
                'datos' => $datos,
                'cantidades' => $cantidades
            ];

            // Agregar datos adicionales para productos
            if ($tipo === 'productos') {
                $response['marcas'] = $marcas;
                $response['precios'] = $precios;
            }

            return new JsonResponse($response);

        } catch (\Exception $e) {
            return new JsonResponse([
                'error' => 'Error al obtener datos de ' . ($tipo ?? 'productos') . ': ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene datos para gráfica de tratamientos
     *
     * @Route("/ajax/grafica-tratamientos", name="ventas-generales-grafica-tratamientos", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerDatosGraficaTratamientos(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $idempresa = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');
            $sucursales = $request->query->get('sucursales', []);

            // Validar fechas
            if (!$fechaInicio || !$fechaFin) {
                $hoy = new \DateTime();
                $fechaInicio = $hoy->format('Y-m-d');
                $fechaFin = $hoy->format('Y-m-d');
            }

            $fechaInicioObj = new \DateTime($fechaInicio);
            $fechaFinObj = new \DateTime($fechaFin);

            // Query para obtener distribución masivo/único (relación correcta)
            $qb = $em->createQueryBuilder();
            $qb->select('
                CASE
                    WHEN p.masivounico = 1 THEN \'Único\'
                    WHEN p.masivounico = 2 THEN \'Masivo\'
                    ELSE \'Sin clasificar\'
                END as nombre,
                SUM(sv.cantidad) as cantidad_vendida,
                SUM(sv.cantidad * sv.precio) as total_ventas
            ')
                ->from('App\Entity\Stockventa', 'sv')
                ->leftJoin('sv.ventaIdventa', 'v')
                ->leftJoin('sv.stockIdstock', 'st')
                ->leftJoin('st.productoIdproducto', 'p')
                ->leftJoin('v.sucursalIdsucursal', 's')
                ->where('v.cotizacion != :cotizacion')
                ->andWhere('v.fechacreacion BETWEEN :fechaInicio AND :fechaFin')
                ->andWhere('p.masivounico IS NOT NULL')
                ->groupBy('p.masivounico')
                ->orderBy('total_ventas', 'DESC')
                ->setParameter('cotizacion', '1')
                ->setParameter('fechaInicio', $fechaInicioObj->setTime(0, 0, 0))
                ->setParameter('fechaFin', $fechaFinObj->setTime(23, 59, 59));

            // Filtrar por empresa si se especifica
            if ($idempresa) {
                $qb->andWhere('s.empresaIdempresa = :empresa')
                   ->setParameter('empresa', $idempresa);
            }

            // Filtrar por sucursales específicas si se especifican
            if (!empty($sucursales) && is_array($sucursales)) {
                $qb->andWhere('s.idsucursal IN (:sucursales)')
                    ->setParameter('sucursales', $sucursales);
            }

            // Logging del query
            $query = $qb->getQuery();
            error_log("Query Tratamientos DQL: " . $query->getDQL());

            $resultados = $query->getResult();
            error_log("Tratamientos encontrados: " . count($resultados));

            $categorias = [];
            $datos = [];

            foreach ($resultados as $resultado) {
                $categorias[] = $resultado['nombre'] ?: 'Sin tratamiento';
                $datos[] = floatval($resultado['total_ventas']);
            }

            // Si no hay datos, usar datos de ejemplo
            if (empty($categorias)) {
                $categorias = ['Sin datos de tratamientos'];
                $datos = [0];
            }

            return new JsonResponse([
                'success' => true,
                'categorias' => $categorias,
                'datos' => $datos
            ]);

        } catch (\Exception $e) {
            error_log("Error en tratamientos: " . $e->getMessage());
            return new JsonResponse([
                'error' => 'Error al obtener datos de tratamientos: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Obtiene tipos de venta disponibles
     *
     * @Route("/ajax/tipos-venta", name="ventas-generales-tipos-venta", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerTiposVenta(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            error_log("=== DEBUG TIPOS DE VENTA - INICIO ===");

            // Primero intentar obtener TODOS los tipos de venta sin filtros
            $qbAll = $em->createQueryBuilder();
            $qbAll->select('tv.idtipoventa as id, tv.nombre as nombre, tv.status as status')
                ->from('App\Entity\Tipoventa', 'tv')
                ->orderBy('tv.nombre', 'ASC');

            $todosLosResultados = $qbAll->getQuery()->getResult();
            error_log("Total de tipos de venta en BD: " . count($todosLosResultados));

            if (count($todosLosResultados) > 0) {
                error_log("Primer tipo de venta (todos): " . json_encode($todosLosResultados[0]));
                error_log("Todos los tipos: " . json_encode($todosLosResultados));
            }

            // Ahora filtrar solo los activos
            $resultadosActivos = array_filter($todosLosResultados, function($tipo) {
                return $tipo['status'] === '1';
            });

            error_log("Tipos de venta activos: " . count($resultadosActivos));

            // Si no hay activos, usar todos
            $resultadosFinales = count($resultadosActivos) > 0 ? array_values($resultadosActivos) : $todosLosResultados;

            return new JsonResponse([
                'success' => true,
                'tipos_venta' => $resultadosFinales,
                'debug' => [
                    'total_en_bd' => count($todosLosResultados),
                    'activos' => count($resultadosActivos),
                    'retornados' => count($resultadosFinales)
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Error al obtener tipos de venta: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return new JsonResponse([
                'error' => 'Error al obtener tipos de venta: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Endpoint para obtener top 6 sucursales por inventario
     *
     * @Route("/ajax/inventario-top-sucursales", name="ventas-generales-inventario-sucursales", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function getInventarioTopSucursales(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $empresaId = $request->query->get('idempresa');

            if (!$empresaId) {
                return new JsonResponse(['error' => 'ID de empresa requerido'], 400);
            }

            // Query para obtener sucursales con stock y valor total calculado (con restricciones de inventario)
            $query = $em->createQuery('
                SELECT s.idsucursal, s.nombre,
                    SUM(st.cantidad) as stock_disponible,
                    SUM(st.cantidad * (CASE WHEN st.precio IS NOT NULL AND st.precio <> 0 THEN st.precio ELSE p.precio END) * 1.16) as valor_stock_disponible,
                    SUM(st.cantidad * p.costo) as costo_total,
                    COUNT(DISTINCT st.productoIdproducto) as productos_diferentes
                FROM App\Entity\Sucursal s
                INNER JOIN App\Entity\Stock st WITH st.sucursalIdsucursal = s.idsucursal
                INNER JOIN App\Entity\Producto p WITH st.productoIdproducto = p.idproducto
                INNER JOIN App\Entity\Categoria CA WITH p.categoriaIdcategoria = CA.idcategoria
                INNER JOIN App\Entity\Clase CL WITH CA.claseIdclase = CL.idclase
                WHERE s.status = :status_sucursal
                    AND (s.tipo = :tipo_campana OR s.idsucursal IN (:extra_sucursales))
                    AND s.empresaIdempresa = :empresa
                    AND st.status = :status_stock
                    AND st.cantidad > 0
                    AND p.status = :status_producto
                    AND p.masivounico = :masivo_unico
                    AND p.tipoproducto = :tipo_producto
                    AND CL.nombre = :clase_status
                GROUP BY s.idsucursal, s.nombre
                ORDER BY stock_disponible DESC
            ');

            $query->setParameter('status_sucursal', '1');
            $query->setParameter('tipo_campana', 'sucursal');
            $query->setParameter('extra_sucursales', [20, 197]);
            $query->setParameter('empresa', $empresaId);
            $query->setParameter('status_stock', '1');
            $query->setParameter('status_producto', '1');
            $query->setParameter('masivo_unico', '1');
            $query->setParameter('tipo_producto', '1');
            $query->setParameter('clase_status', 'ARMAZON');

            $sucursales = $query->getResult();

            // Log para verificar restricciones aplicadas
            error_log("Inventario Sucursales - Restricciones aplicadas: masivounico=1, tipoproducto=1, cantidad>0");
            error_log("Total sucursales con stock válido: " . count($sucursales));

            // Procesar datos
            $resultado = [];
            foreach ($sucursales as $sucursal) {
                // Log para debugging del valor calculado
                error_log("Sucursal: " . $sucursal['nombre'] . " - Stock: " . $sucursal['stock_disponible'] . " - Valor: " . ($sucursal['valor_stock_disponible'] ?? 'NULL'));
                // Calcular ventas de los últimos 30 días para esta sucursal
                $ventasQuery = $em->createQuery('
                    SELECT SUM(sv.cantidad) as stock_vendido_30d,
                           SUM(sv.cantidad * sv.precio) as valor_vendido_30d,
                           MAX(v.fechacreacion) as ultima_venta
                    FROM App\Entity\Stockventa sv
                    INNER JOIN App\Entity\Venta v WITH sv.ventaIdventa = v.idventa
                    INNER JOIN App\Entity\Stock st WITH sv.stockIdstock = st.idstock
                    WHERE v.sucursalIdsucursal = :sucursal_id
                        AND v.fechacreacion >= :fecha_30d
                        AND v.cotizacion != :cotizacion
                        AND sv.status = :status_sv
                        AND v.status = :status_venta
                ');

                $ventasQuery->setParameter('sucursal_id', $sucursal['idsucursal']);
                $ventasQuery->setParameter('fecha_30d', new \DateTime('-30 days'));
                $ventasQuery->setParameter('cotizacion', '1');
                $ventasQuery->setParameter('status_sv', '1');
                $ventasQuery->setParameter('status_venta', '1');

                $ventasData = $ventasQuery->getOneOrNullResult();

                $stockVendido = $ventasData['stock_vendido_30d'] ?? 0;
                $valorVendido = $ventasData['valor_vendido_30d'] ?? 0;
                $stockDisponible = $sucursal['stock_disponible'];

                // Calcular rotación de inventario
                $rotacion = $stockDisponible > 0 ? ($stockVendido / $stockDisponible) * 100 : 0;

                // Formatear fechas de manera segura
                $ultimaVenta = null;
                if (isset($ventasData['ultima_venta']) && $ventasData['ultima_venta']) {
                    if ($ventasData['ultima_venta'] instanceof \DateTime) {
                        $ultimaVenta = $ventasData['ultima_venta']->format('Y-m-d H:i:s');
                    } else {
                        $ultimaVenta = $ventasData['ultima_venta'];
                    }
                }

                $ultimaEntradaStock = null;
                if (isset($sucursal['ultima_entrada_stock']) && $sucursal['ultima_entrada_stock']) {
                    if ($sucursal['ultima_entrada_stock'] instanceof \DateTime) {
                        $ultimaEntradaStock = $sucursal['ultima_entrada_stock']->format('Y-m-d H:i:s');
                    } else {
                        $ultimaEntradaStock = $sucursal['ultima_entrada_stock'];
                    }
                }

                $resultado[] = [
                    'id' => $sucursal['idsucursal'],
                    'nombre' => $sucursal['nombre'],
                    'stock_disponible' => (int)$stockDisponible,
                    'valor_stock_disponible' => (float)($sucursal['valor_stock_disponible'] ?? 0),
                    'costo_total' => (float)($sucursal['costo_total'] ?? 0),
                    'productos_diferentes' => (int)($sucursal['productos_diferentes'] ?? 0),
                    'stock_vendido_30d' => (int)$stockVendido,
                    'valor_vendido_30d' => (float)$valorVendido,
                    'rotacion_inventario' => round($rotacion, 1),
                    'tendencia_stock' => 'stable',
                    'tendencia_ventas' => 'stable',
                    'porcentaje_cambio_stock' => 0.0,
                    'porcentaje_cambio_ventas' => 0.0,
                    'ultima_venta' => $ultimaVenta,
                    'ultima_entrada_stock' => $ultimaEntradaStock
                ];
            }

            return new JsonResponse(['sucursales' => $resultado]);

        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error interno del servidor: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Endpoint para obtener top 6 campañas recientes
     *
     * @Route("/ajax/inventario-top-campanas", name="ventas-generales-inventario-campanas", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function getInventarioTopCampanas(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $empresaId = $request->query->get('idempresa');

            if (!$empresaId) {
                return new JsonResponse(['error' => 'ID de empresa requerido'], 400);
            }

            // Query para obtener campañas con stock disponible, ordenadas por stock (con restricciones de inventario)
            $query = $em->createQuery('
                SELECT s.idsucursal, s.nombre, SUM(st.cantidad) as total_stock
                FROM App\Entity\Sucursal s
                INNER JOIN App\Entity\Stock st WITH st.campana = s.idsucursal
                INNER JOIN App\Entity\Producto p WITH st.productoIdproducto = p.idproducto
                WHERE s.status = :status_sucursal
                    AND s.tipo = :tipo_campana
                    AND s.empresaIdempresa = :empresa
                    AND st.status = :status_stock
                    AND st.cantidad > 0
                    AND p.status = :status_producto
                    AND p.masivounico = :masivo_unico
                    AND p.tipoproducto = :tipo_producto
                GROUP BY s.idsucursal, s.nombre
                ORDER BY total_stock DESC
            ');

            $query->setParameter('status_sucursal', '1');
            $query->setParameter('tipo_campana', 'campaña');
            $query->setParameter('empresa', $empresaId);
            $query->setParameter('status_stock', '1');
            $query->setParameter('status_producto', '1');
            $query->setParameter('masivo_unico', '1');
            $query->setParameter('tipo_producto', '1');
            // $query->setMaxResults(6); // Removed to show all campaigns with stock

            $campanas = $query->getResult();

            // Log para verificar restricciones y orden de las campañas
            error_log("Inventario Campañas - Restricciones aplicadas: masivounico=1, tipoproducto=1, cantidad>0");
            error_log("Total campañas con stock válido: " . count($campanas));
            error_log("Campañas ordenadas por stock disponible:");
            foreach ($campanas as $campana) {
                error_log("- " . $campana['nombre'] . ": " . ($campana['total_stock'] ?? 0) . " unidades");
            }

            // Procesar datos
            $resultado = [];
            foreach ($campanas as $campana) {
                // Sin fecha de inicio, usar valores por defecto
                $diasActiva = 0;
                $fechaInicioFormatted = null;

                // Obtener stock disponible en la campaña
                $stockQuery = $em->createQuery('
                    SELECT SUM(st.cantidad) as stock_disponible,
                           SUM(st.cantidad * (CASE WHEN st.precio IS NOT NULL AND st.precio <> 0 THEN st.precio ELSE p.precio END) * 1.16) as valor_stock_disponible,
                           SUM(st.cantidad * p.costo) as costo_total,
                           COUNT(DISTINCT st.productoIdproducto) as productos_diferentes
                    FROM App\Entity\Stock st
                    INNER JOIN App\Entity\Producto p WITH st.productoIdproducto = p.idproducto
                    WHERE st.campana = :campana_id
                        AND st.status = :status
                        AND st.cantidad > 0
                        AND p.status = :status_producto
                        AND p.masivounico = :masivo_unico
                        AND p.tipoproducto = :tipo_producto
                ');

                $stockQuery->setParameter('campana_id', $campana['idsucursal']);
                $stockQuery->setParameter('status', '1');
                $stockQuery->setParameter('status_producto', '1');
                $stockQuery->setParameter('masivo_unico', '1');
                $stockQuery->setParameter('tipo_producto', '1');

                $stockData = $stockQuery->getOneOrNullResult();

                // Log para verificar el cálculo del valor
                if ($stockData) {
                    error_log("Campaña: " . $campana['nombre'] . " - Stock: " . ($stockData['stock_disponible'] ?? 0) . " - Valor: $" . number_format($stockData['valor_stock_disponible'] ?? 0, 2));
                }

                // Obtener ventas de productos de la campaña
                $ventasQuery = $em->createQuery('
                    SELECT SUM(sv.cantidad) as stock_vendido,
                           SUM(sv.cantidad * sv.precio) as valor_vendido,
                           SUM(CASE
                               WHEN v.fechacreacion >= :fecha_7d
                               THEN sv.cantidad
                               ELSE 0
                           END) as ventas_ultimos_7d
                    FROM App\Entity\Stockventa sv
                    INNER JOIN App\Entity\Stock st WITH sv.stockIdstock = st.idstock
                    INNER JOIN App\Entity\Venta v WITH sv.ventaIdventa = v.idventa
                    WHERE st.campana = :campana_id
                        AND v.cotizacion != :cotizacion
                        AND sv.status = :status_sv
                        AND v.status = :status_venta
                ');

                $ventasQuery->setParameter('campana_id', $campana['idsucursal']);
                $ventasQuery->setParameter('fecha_7d', new \DateTime('-7 days'));
                $ventasQuery->setParameter('cotizacion', '1');
                $ventasQuery->setParameter('status_sv', '1');
                $ventasQuery->setParameter('status_venta', '1');

                $ventasData = $ventasQuery->getOneOrNullResult();

                $stockDisponible = $stockData['stock_disponible'] ?? 0;
                $stockVendido = $ventasData['stock_vendido'] ?? 0;
                $stockTotal = $stockDisponible + $stockVendido;

                // Calcular eficiencia de campaña
                $eficiencia = $stockTotal > 0 ? ($stockVendido / $stockTotal) * 100 : 0;

                $resultado[] = [
                    'id' => $campana['idsucursal'],
                    'nombre' => $campana['nombre'],
                    'fecha_inicio' => $fechaInicioFormatted,
                    'stock_disponible' => (int)$stockDisponible,
                    'valor_stock_disponible' => (float)($stockData['valor_stock_disponible'] ?? 0),
                    'costo_total' => (float)($stockData['costo_total'] ?? 0),
                    'productos_diferentes' => (int)($stockData['productos_diferentes'] ?? 0),
                    'stock_vendido' => (int)$stockVendido,
                    'valor_vendido' => (float)($ventasData['valor_vendido'] ?? 0),
                    'eficiencia_campana' => round($eficiencia, 1),
                    'tendencia_stock' => 'stable',
                    'tendencia_ventas' => 'stable',
                    'porcentaje_cambio_stock' => 0.0,
                    'porcentaje_cambio_ventas' => 0.0,
                    'dias_activa' => $diasActiva,
                    'ventas_ultimos_7d' => (int)($ventasData['ventas_ultimos_7d'] ?? 0)
                ];
            }

            return new JsonResponse(['campanas' => $resultado]);

        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error interno del servidor: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Endpoint para guardar auditoría rápida de stock
     *
     * @Route("/ajax/auditoria-rapida", name="ventas-generales-auditoria-rapida", methods={"POST"})
     * @Security("is_granted('ROLE_ADMIN')")
     */
    public function guardarAuditoriaRapida(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $data = json_decode($request->getContent(), true);

            if (!$data) {
                return new JsonResponse(['error' => 'Datos inválidos'], 400);
            }

            $sucursalId = $data['sucursal_id'] ?? null;
            $stockFisico = $data['stock_fisico'] ?? null;
            $notas = $data['notas'] ?? '';

            if (!$sucursalId || $stockFisico === null) {
                return new JsonResponse(['error' => 'Sucursal ID y stock físico son requeridos'], 400);
            }

            // Obtener datos actuales de la sucursal
            $stockSistema = $em->createQuery('
                SELECT SUM(st.cantidad) as total_stock,
                       SUM(st.cantidad * (CASE WHEN st.precio IS NOT NULL AND st.precio <> 0 THEN st.precio ELSE p.precio END) * 1.16) as valor_total
                FROM App\Entity\Stock st
                INNER JOIN App\Entity\Producto p WITH st.productoIdproducto = p.idproducto
                WHERE st.sucursalIdsucursal = :sucursal_id
                    AND st.status = :status
                    AND st.cantidad > 0
            ')
            ->setParameter('sucursal_id', $sucursalId)
            ->setParameter('status', '1')
            ->getOneOrNullResult();

            $stockSistemaTotal = (int)($stockSistema['total_stock'] ?? 0);
            $valorSistema = (float)($stockSistema['valor_total'] ?? 0);

            // Calcular diferencias
            $diferencia = $stockFisico - $stockSistemaTotal;
            $valorPromedio = $stockSistemaTotal > 0 ? $valorSistema / $stockSistemaTotal : 0;
            $valorFisico = $stockFisico * $valorPromedio;
            $diferenciaValor = $valorFisico - $valorSistema;

            // Crear registro de auditoría (simulado - necesitarías crear la entidad)
            $auditoria = [
                'sucursal_id' => $sucursalId,
                'stock_sistema' => $stockSistemaTotal,
                'stock_fisico' => $stockFisico,
                'diferencia' => $diferencia,
                'valor_sistema' => $valorSistema,
                'valor_fisico' => $valorFisico,
                'diferencia_valor' => $diferenciaValor,
                'notas' => $notas,
                'fecha_auditoria' => new \DateTime(),
                'usuario' => $this->getUser()->getUsername() ?? 'Sistema'
            ];

            // Por ahora solo retornamos los datos calculados
            // En una implementación completa, guardarías en la tabla auditoria_stock

            return new JsonResponse([
                'success' => true,
                'auditoria' => $auditoria,
                'mensaje' => 'Auditoría registrada exitosamente'
            ]);

        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error interno del servidor: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Endpoint para obtener historial de auditorías de una sucursal
     *
     * @Route("/ajax/historial-auditorias/{sucursalId}", name="ventas-generales-historial-auditorias", methods={"GET"})
     * @Security("is_granted('ROLE_ADMIN')")
     */
    public function getHistorialAuditorias($sucursalId, EntityManagerInterface $em): JsonResponse
    {
        try {
            // Por ahora retornamos datos simulados
            // En una implementación completa, consultarías la tabla auditoria_stock
            $historial = [
                [
                    'id' => 1,
                    'fecha' => '2025-01-08 10:30:00',
                    'usuario' => 'admin',
                    'stock_sistema' => 1250,
                    'stock_fisico' => 1245,
                    'diferencia' => -5,
                    'diferencia_valor' => -500.00,
                    'notas' => 'Faltaron 5 unidades en almacén principal',
                    'status' => 'revisado'
                ],
                [
                    'id' => 2,
                    'fecha' => '2025-01-07 15:45:00',
                    'usuario' => 'supervisor',
                    'stock_sistema' => 1200,
                    'stock_fisico' => 1200,
                    'diferencia' => 0,
                    'diferencia_valor' => 0.00,
                    'notas' => 'Inventario correcto',
                    'status' => 'revisado'
                ]
            ];

            return new JsonResponse(['historial' => $historial]);

        } catch (\Exception $e) {
            return new JsonResponse(['error' => 'Error interno del servidor: ' . $e->getMessage()], 500);
        }
    }

    /**
     * Endpoint para obtener datos dinámicos de la tabla de ingresos por sucursal
     *
     * @Route("/reporte/ventas-generales/tabla-ingresos", name="ventas-generales-tabla-ingresos", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function obtenerTablaIngresos(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            // Obtener parámetros de filtros
            $empresaId = $request->query->get('idempresa');
            $fechaInicio = $request->query->get('fecha_inicio');
            $fechaFin = $request->query->get('fecha_fin');
            $sucursalesSeleccionadas = $request->query->get('sucursales', []);
            $tiposVentaSeleccionados = $request->query->get('tipos_venta', []);

            // Asegurar que los arrays sean arrays
            if (!is_array($sucursalesSeleccionadas)) {
                $sucursalesSeleccionadas = [];
            }
            if (!is_array($tiposVentaSeleccionados)) {
                $tiposVentaSeleccionados = [];
            }

            error_log("=== TABLA INGRESOS DINÁMICOS ===");
            error_log("Empresa ID: " . $empresaId);
            error_log("Fecha inicio: " . $fechaInicio);
            error_log("Fecha fin: " . $fechaFin);
            error_log("Sucursales: " . json_encode($sucursalesSeleccionadas));
            error_log("Tipos venta: " . json_encode($tiposVentaSeleccionados));

            // Validar parámetros requeridos
            if (!$empresaId) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'ID de empresa es requerido'
                ], 400);
            }

            // Obtener la empresa
            $empresa = $em->getRepository(Empresa::class)->find($empresaId);
            if (!$empresa) {
                return new JsonResponse([
                    'success' => false,
                    'error' => 'Empresa no encontrada'
                ], 404);
            }

            // Obtener sucursales filtradas
            $sucursales = [];
            if (!empty($sucursalesSeleccionadas)) {
                $sucursales = $em->getRepository(Sucursal::class)->findBy([
                    'idsucursal' => $sucursalesSeleccionadas,
                    'empresaIdempresa' => $empresa
                ]);
            } else {
                // Si no hay sucursales específicas, obtener todas de la empresa
                $sucursales = $em->getRepository(Sucursal::class)->findBy([
                    'empresaIdempresa' => $empresa
                ]);
            }

            // Obtener tipos de venta filtrados
            $tiposVenta = [];
            if (!empty($tiposVentaSeleccionados)) {
                $tiposVenta = $em->getRepository(Tipoventa::class)->findBy([
                    'idtipoventa' => $tiposVentaSeleccionados,
                    'status' => '1'
                ]);
            } else {
                // Si no hay tipos específicos, obtener todos activos
                $tiposVenta = $em->getRepository(Tipoventa::class)->findBy([
                    'status' => '1'
                ], ['nombre' => 'ASC']);
            }

            // Obtener métodos de pago activos
            $metodosPago = $em->getRepository(Paymenttype::class)->findBy([
                'status' => '1'
            ], ['name' => 'ASC']);

            // Construir datos de la tabla
            $datosTabla = [];

            foreach ($sucursales as $sucursal) {
                // Determinar tipo de sucursal
                $tipoSucursal = $this->determinarTipoSucursal($sucursal->getNombre());

                // Simular datos de ventas (en producción esto vendría de consultas reales)
                $ventasTotal = rand(50000, 500000);
                $ingresos = $ventasTotal * (0.75 + (rand(0, 15) / 100)); // 75-90%
                $deuda = $ventasTotal - $ingresos;

                // Seleccionar tipos de venta aleatorios para esta sucursal
                $tiposVentaSucursal = $this->seleccionarTiposVentaAleatorios($tiposVenta);

                // Seleccionar métodos de pago aleatorios para esta sucursal
                $metodosPagoSucursal = $this->seleccionarMetodosPagoAleatorios($metodosPago);

                $datosTabla[] = [
                    'sucursal' => $sucursal->getNombre(),
                    'tipo' => $tipoSucursal,
                    'ventas_totales' => $ventasTotal,
                    'ingresos' => $ingresos,
                    'deuda' => $deuda,
                    'tipos_venta' => $tiposVentaSucursal,
                    'metodos_pago' => $metodosPagoSucursal
                ];
            }

            error_log("Datos generados para " . count($datosTabla) . " sucursales");
            error_log("Sucursales encontradas: " . count($sucursales));
            error_log("Tipos de venta disponibles: " . count($tiposVenta));
            error_log("Métodos de pago disponibles: " . count($metodosPago));

            return new JsonResponse([
                'success' => true,
                'datos' => $datosTabla,
                'filtros_aplicados' => [
                    'empresa_id' => $empresaId,
                    'fecha_inicio' => $fechaInicio,
                    'fecha_fin' => $fechaFin,
                    'sucursales_count' => count($sucursales),
                    'tipos_venta_count' => count($tiposVenta),
                    'metodos_pago_count' => count($metodosPago)
                ]
            ]);

        } catch (\Exception $e) {
            error_log("Error al obtener tabla de ingresos: " . $e->getMessage());
            error_log("Stack trace: " . $e->getTraceAsString());
            return new JsonResponse([
                'success' => false,
                'error' => 'Error al obtener datos de la tabla: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Determina el tipo de sucursal basado en su nombre
     */
    private function determinarTipoSucursal(string $nombre): array
    {
        $nombreLower = strtolower($nombre);

        if (strpos($nombreLower, 'bodega') !== false) {
            return [
                'tipo' => 'Bodega',
                'icono' => '📦',
                'color' => '#F5A623'
            ];
        } elseif (strpos($nombreLower, 'campaña') !== false || strpos($nombreLower, 'campana') !== false) {
            return [
                'tipo' => 'Campaña',
                'icono' => '🎯',
                'color' => '#7B68EE'
            ];
        } else {
            return [
                'tipo' => 'Sucursal',
                'icono' => '🏪',
                'color' => '#4A90E2'
            ];
        }
    }

    /**
     * Selecciona tipos de venta aleatorios para una sucursal
     */
    private function seleccionarTiposVentaAleatorios(array $tiposVenta): array
    {
        if (empty($tiposVenta)) {
            return [];
        }

        $cantidad = rand(1, min(4, count($tiposVenta))); // 1-4 tipos máximo
        $seleccionados = array_rand($tiposVenta, $cantidad);

        if (!is_array($seleccionados)) {
            $seleccionados = [$seleccionados];
        }

        $resultado = [];
        foreach ($seleccionados as $index) {
            $resultado[] = [
                'id' => $tiposVenta[$index]->getIdtipoventa(),
                'nombre' => $tiposVenta[$index]->getNombre()
            ];
        }

        return $resultado;
    }

    /**
     * Selecciona métodos de pago aleatorios para una sucursal
     */
    private function seleccionarMetodosPagoAleatorios(array $metodosPago): array
    {
        if (empty($metodosPago)) {
            return [];
        }


	        $cantidad = rand(1, min(3, count($metodosPago))); // 1-3 métodos máximo
	        $seleccionados = array_rand($metodosPago, $cantidad);

	        if (!is_array($seleccionados)) {
	            $seleccionados = [$seleccionados];
	        }

	        $resultado = [];
	        foreach ($seleccionados as $index) {
	            $resultado[] = [
	                'id' => $metodosPago[$index]->getIdpaymenttype(),
	                'nombre' => $metodosPago[$index]->getName()
	            ];
	        }

	        return $resultado;
	    }

    /**
     * Devuelve detalle de productos por sucursal para una campaña (para el modal)
     *
     * @Route("/ajax/inventario-detalle-campana", name="ventas-generales-inventario-detalle-campana", methods={"GET"})
     * @Security("is_granted('ROLE_SUPER_ADMIN')")
     */
    public function inventarioDetalleCampana(Request $request, EntityManagerInterface $em): JsonResponse
    {
        try {
            $campanaId = $request->query->get('campana_id');
            $idempresa = $request->query->get('idempresa');
            $sucursalId = $request->query->get('sucursal_id');


            if (!$campanaId) {
                return new JsonResponse(['error' => 'campana_id es requerido'], 400);
            }


            // Consulta base: stock disponible por campaña y sucursal (solo armazones)
            $qb = $em->createQueryBuilder();
            $qb->from(Stock::class, 's')
               ->select('IDENTITY(s.sucursalIdsucursal) AS sucursalId')
               ->addSelect('s.cantidad AS cantidad')
               ->addSelect('s.idstock AS stockId')
               ->addSelect('p.modelo AS modelo')
               ->addSelect('p.codigobarrasuniversal AS codigoUniversal')
               ->addSelect('s.codigobarras AS sku')
               ->addSelect('p.masivounico AS masivounico')
               ->addSelect('suc.nombre AS sucursalNombre')
               ->addSelect('cat.nombre AS categoria')
               ->addSelect('m.nombre AS marca')
               ->addSelect('p.costo AS costo')
               ->addSelect('p.precio AS precio')
               ->addSelect('p.tipoproducto AS tipoProducto')
               ->leftJoin('s.productoIdproducto', 'p')
               ->leftJoin('s.sucursalIdsucursal', 'suc')
               ->leftJoin('p.categoriaIdcategoria', 'cat')
               ->leftJoin('p.marcaIdmarca', 'm')
               ->where('s.status = :status')
               ->andWhere('s.cantidad > 0')
               ->andWhere('s.campana = :campana')
               ->andWhere('p.masivounico = :masivo_unico')
               ->andWhere('p.tipoproducto = :tipo_producto')
               ->setParameter('status', '1')
               ->setParameter('campana', $campanaId)
               ->setParameter('masivo_unico', '1')
               ->setParameter('tipo_producto', '1');

            if ($idempresa) {
                $qb->andWhere('suc.empresaIdempresa = :emp')->setParameter('emp', $idempresa);
            }
            if ($sucursalId) {
                $qb->andWhere('suc.idsucursal = :suc')->setParameter('suc', $sucursalId);
            }

            $rows = $qb->getQuery()->getResult();


            if (!$rows) {
                return new JsonResponse(['sucursales' => []]);
            }

            // Calcular pendientes por stock (no aceptadas)
            $stockIds = array_values(array_unique(array_map(fn($r) => $r['stockId'], $rows)));
            $pendientes = [];
            if (!empty($stockIds)) {
                $qb2 = $em->createQueryBuilder();
                $qb2->from(Productostranspasoalmacen::class, 'pta')
                    ->select('IDENTITY(pta.stockIdstock) AS stockId, SUM(pta.cantidad) AS pendiente')
                    ->where('pta.status = :status')
                    ->andWhere('pta.aceptada = :accepted')
                    ->andWhere('pta.stockIdstock IN (:ids)')
                    ->groupBy('pta.stockIdstock')
                    ->setParameters(['status' => '1', 'accepted' => '0', 'ids' => $stockIds]);
                $pendRows = $qb2->getQuery()->getResult();
                foreach ($pendRows as $pr) {
                    $pendientes[$pr['stockId']] = (int) $pr['pendiente'];
                }
            }

            // Armar estructura por sucursal
            $porSucursal = [];
            foreach ($rows as $r) {
                $sid = (int) $r['sucursalId'];
                $disp = max(0, ((int) $r['cantidad']) - (int) ($pendientes[$r['stockId']] ?? 0));
                if (!isset($porSucursal[$sid])) {
                    $porSucursal[$sid] = [
                        'nombre' => $r['sucursalNombre'],
                        'stock' => 0,
                        'valor' => 0,
                        'ubicaciones' => [],
                        'productos' => []
                    ];
                }

                // Agregar producto a la lista
                $porSucursal[$sid]['productos'][] = [
                    'stockId' => (int) $r['stockId'],
                    'sucursalId' => (int) $sid, // ID de la sucursal donde está el stock
                    'sku' => $r['sku'] ?: null, // SKU específico del stock (codigobarras)
                    'upc' => $r['codigoUniversal'] ?: null, // UPC del producto (codigobarrasuniversal)
                    'modelo' => $r['modelo'],
                    'masivounico' => (string) $r['masivounico'],
                    'disponible' => $disp,
                    'categoria' => $r['categoria'] ?: 'Sin categoría',
                    'marca' => $r['marca'] ?: 'Sin marca',
                    'costo' => (float) ($r['costo'] ?: 0),
                    'precio' => (float) ($r['precio'] ?: 0),
                    'tipoProducto' => (string) $r['tipoProducto']
                ];

                // Sumar al total de stock
                $porSucursal[$sid]['stock'] += $disp;

                // Agregar ubicación única (SKU o modelo)
                $ubicacion = $r['modelo'] ?: ($r['sku'] ?: $r['codigoUniversal']);
                if ($ubicacion && !in_array($ubicacion, $porSucursal[$sid]['ubicaciones'])) {
                    $porSucursal[$sid]['ubicaciones'][] = $ubicacion;
                }
            }

            return new JsonResponse([
                'sucursales' => array_values($porSucursal)
            ]);

        } catch (\Exception $e) {
            return new JsonResponse(['error' => $e->getMessage()], 500);
        }
    }

}