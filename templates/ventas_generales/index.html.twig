{% extends 'admin/layout.html.twig' %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Ventas Generales{% endblock %}

{% block stylesheets %}
{{ parent() }}
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Select2 CSS (solo si no está incluido en el layout) -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.bootstrap5.min.css" rel="stylesheet" />

<!-- Dashboard Oficial CSS -->
<link href="{{ asset('css/puntodeventa/dashboard-oficial.css') }}" rel="stylesheet" />

<!-- Ventas Generales CSS -->
<link href="{{ asset('css/ventas_generales/styles.css') }}" rel="stylesheet" />
{% endblock %}

{% block content %}

<!-- Executive Dashboard -->
<div class="executive-dashboard ventas-generales-container dashboard-oficial-container" data-theme="light">

<!-- Overlay de carga inicial (dentro del contenido) -->
<div id="initial-loading-overlay" style="
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
">
  <div style="text-align: center;">
    <!-- GIF de carga -->
    <div style="margin-bottom: 2rem;">
      <div style="
        width: 120px;
        height: 120px;
        border: 4px solid var(--do-gray-200);
        border-top: 4px solid var(--do-primary-blue);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      "></div>
    </div>

    <!-- Título -->
    <h2 style="color: var(--do-gray-800); font-size: 1.5rem; font-weight: 600; margin: 0 0 1rem 0;">
      <i class="fas fa-chart-line" style="color: var(--do-primary-blue); margin-right: 0.5rem;"></i>
      Configurando Dashboard de Ventas
    </h2>

    <!-- Mensaje dinámico -->
    <p id="loading-message" style="color: var(--do-gray-600); font-size: 1rem; margin: 0 0 2rem 0; min-height: 1.5rem;">
      Iniciando sistema...
    </p>

    <!-- Barra de progreso -->
    <div style="width: 300px; height: 6px; background: var(--do-gray-200); border-radius: 3px; overflow: hidden; margin: 0 auto;">
      <div id="loading-progress" style="
        width: 0%;
        height: 100%;
        background: linear-gradient(90deg, var(--do-primary-blue), var(--do-success-green));
        border-radius: 3px;
        transition: width 0.5s ease;
      "></div>
    </div>

    <!-- Pasos de carga -->
    <div style="margin-top: 2rem; text-align: left; max-width: 400px;">
      <div id="step-empresa" style="display: flex; align-items: center; margin-bottom: 0.5rem; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Configurando empresa...
      </div>
      <div id="step-fechas" style="display: flex; align-items: center; margin-bottom: 0.5rem; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Estableciendo fechas...
      </div>
      <div id="step-sucursales" style="display: flex; align-items: center; margin-bottom: 0.5rem; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Cargando sucursales...
      </div>
      <div id="step-tipos" style="display: flex; align-items: center; margin-bottom: 0.5rem; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Configurando tipos de venta...
      </div>
      <div id="step-inventario" style="display: flex; align-items: center; margin-bottom: 0.5rem; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Cargando inventario...
      </div>
      <div id="step-graficas" style="display: flex; align-items: center; color: var(--do-gray-500); font-size: 0.875rem;">
        <i class="fas fa-circle" style="margin-right: 0.5rem; font-size: 0.5rem;"></i>
        Generando gráficas...
      </div>
    </div>
  </div>
</div>

  <!-- Header con controles -->
  <div style="position: relative; margin-bottom: 1rem;">



  <!-- Wizard de Configuración de Reporte -->
  <section class="filtros-wizard" style="margin-bottom: 2rem;">
    <div style="background: var(--do-white); border-radius: 12px; padding: 2rem; box-shadow: var(--do-shadow-sm); border: 1px solid var(--do-gray-200);">
      <h3 style="font-size: 1.25rem; font-weight: 700; color: var(--do-gray-900); margin: 0 0 2rem 0; display: flex; align-items: center; gap: 0.75rem;">
        <i class="fas fa-cogs" style="color: var(--do-primary-blue);"></i>
        Configuración de Reporte
      </h3>

      <!-- Paso 1: Empresa -->
      <div class="filtro-step" data-step="1" style="margin-bottom: 1.5rem; padding: 1.5rem; border: 2px solid var(--vg-success-soft); border-radius: 12px; transition: all 0.3s ease;">
        <div class="step-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
          <div class="step-number" style="width: 32px; height: 32px; border-radius: 50%; background: var(--vg-success-soft); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">1</div>
          <div style="flex: 1;">
            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: var(--do-gray-900);">Seleccionar Empresa</h4>
            <p style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);">Elija la empresa para generar el reporte</p>
          </div>
          <span class="step-status" style="padding: 0.25rem 0.75rem; background: var(--vg-success-soft); color: white; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">✅ COMPLETADO</span>
        </div>
        <div class="step-content">
          <select name="empresa" id="idempresa" disabled style="width: 100%; padding: 1rem; border: 2px solid var(--vg-success-soft); border-radius: 8px; font-size: 1rem; background: var(--do-green-50); color: var(--do-gray-700); cursor: not-allowed; opacity: 0.8;" onchange="onEmpresaChange();">
            {% for empresa in empresas %}
              {% if empresa.idempresa == 1 %}
                <option value="{{ empresa.idempresa }}" selected>🔒 {{ empresa.nombre }} (Empresa Fija)</option>
              {% else %}
                <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
              {% endif %}
            {% endfor %}
          </select>
        </div>
      </div>

      <!-- Paso 2: Período -->
      <div class="filtro-step" data-step="2" data-disabled="true" style="margin-bottom: 1.5rem; padding: 1.5rem; border: 2px solid var(--do-gray-200); border-radius: 12px; opacity: 0.5; transition: all 0.3s ease;">
        <div class="step-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
          <div class="step-number" style="width: 32px; height: 32px; border-radius: 50%; background: var(--do-gray-300); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">2</div>
          <div style="flex: 1;">
            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: var(--do-gray-900);">Período de Análisis</h4>
            <p style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);">Seleccione el rango de fechas para el reporte</p>
          </div>
          <span class="step-status" style="padding: 0.25rem 0.75rem; background: var(--do-warning-amber); color: white; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">⚠️ REQUERIDO</span>
        </div>
        <div class="step-content">
          <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 1rem; align-items: center; margin-bottom: 1rem;">
            <div>
              <label for="fecha-inicio" style="display: block; font-size: 1rem; font-weight: 500; color: var(--do-gray-700); margin-bottom: 0.5rem;">
                Fecha Inicio:
              </label>
              <input type="date" id="fecha-inicio" style="width: 100%; padding: 1rem; border: 2px solid var(--do-gray-300); border-radius: 8px; font-size: 1rem; background: var(--do-white); color: var(--do-gray-900);" onchange="onFechaChange();">
            </div>
            <div style="text-align: center; padding-top: 1.5rem;">
              <i class="fas fa-arrow-right" style="color: var(--do-gray-400);"></i>
            </div>
            <div>
              <label for="fecha-fin" style="display: block; font-size: 1rem; font-weight: 500; color: var(--do-gray-700); margin-bottom: 0.5rem;">
                Fecha Fin:
              </label>
              <input type="date" id="fecha-fin" style="width: 100%; padding: 1rem; border: 2px solid var(--do-gray-300); border-radius: 8px; font-size: 1rem; background: var(--do-white); color: var(--do-gray-900);" onchange="onFechaChange();">
            </div>
          </div>
          <div class="date-presets" style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
            <button type="button" onclick="setPresetFecha('hoy')" style="padding: 0.75rem 1rem; background: var(--do-gray-100); border: 1px solid var(--do-gray-300); border-radius: 6px; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--do-gray-200)'" onmouseout="this.style.background='var(--do-gray-100)'">Hoy</button>
            <button type="button" onclick="setPresetFecha('semana')" style="padding: 0.75rem 1rem; background: var(--do-gray-100); border: 1px solid var(--do-gray-300); border-radius: 6px; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--do-gray-200)'" onmouseout="this.style.background='var(--do-gray-100)'">Esta Semana</button>
            <button type="button" onclick="setPresetFecha('mes')" style="padding: 0.75rem 1rem; background: var(--do-gray-100); border: 1px solid var(--do-gray-300); border-radius: 6px; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--do-gray-200)'" onmouseout="this.style.background='var(--do-gray-100)'">Este Mes</button>
            <button type="button" onclick="setPresetFecha('año')" style="padding: 0.75rem 1rem; background: var(--do-gray-100); border: 1px solid var(--do-gray-300); border-radius: 6px; font-size: 0.875rem; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='var(--do-gray-200)'" onmouseout="this.style.background='var(--do-gray-100)'">Este Año</button>
          </div>
        </div>
      </div>

      <!-- Paso 3: Tipo de Venta -->
      <div class="filtro-step" data-step="3" data-disabled="true" style="margin-bottom: 1.5rem; padding: 1.5rem; border: 2px solid var(--do-gray-200); border-radius: 12px; opacity: 0.5; transition: all 0.3s ease;">
        <div class="step-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
          <div class="step-number" style="width: 32px; height: 32px; border-radius: 50%; background: var(--do-gray-300); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">3</div>
          <div style="flex: 1;">
            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: var(--do-gray-900);">Tipo de Venta</h4>
            <p style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);">Seleccione los tipos de venta a analizar</p>
          </div>
          <span class="step-status" style="padding: 0.25rem 0.75rem; background: var(--do-success-green); color: white; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">OPCIONAL</span>
        </div>
        <div class="step-content">
          <div class="filter-summary" style="display: flex; align-items: center; justify-content: space-between; padding: 1rem; background: var(--do-gray-50); border-radius: 8px; border: 1px solid var(--do-gray-200);">
            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
              <input type="checkbox" id="todos-tipos-venta" checked onchange="toggleTodosTiposVenta()" style="width: 18px; height: 18px;">
              <span style="font-weight: 500; color: var(--do-gray-900); font-size: 1rem;">Todos los tipos de venta</span>
              <span id="tipos-venta-count" style="color: var(--do-gray-600); font-size: 1rem; font-weight: 500;">(Cargando...)</span>
            </label>
            <button type="button" class="toggle-details" onclick="toggleTiposVentaDetails()" style="padding: 0.75rem 1rem; background: var(--vg-sucursal-color); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease;">
              <span>Ver detalles</span>
              <i class="fas fa-chevron-down" id="tipos-venta-chevron"></i>
            </button>
          </div>
          <div class="filter-details" id="tipos-venta-details" style="display: none; margin-top: 1rem; padding: 1rem; background: var(--do-white); border-radius: 8px; border: 1px solid var(--do-gray-200);">
            <div id="tipos-venta-list">
            </div>
          </div>
        </div>
      </div>

      <!-- Paso 4: Sucursales -->
      <div class="filtro-step" data-step="4" data-disabled="true" style="margin-bottom: 2rem; padding: 1.5rem; border: 2px solid var(--do-gray-200); border-radius: 12px; opacity: 0.5; transition: all 0.3s ease;">
        <div class="step-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
          <div class="step-number" style="width: 32px; height: 32px; border-radius: 50%; background: var(--do-gray-300); color: white; display: flex; align-items: center; justify-content: center; font-weight: 600; font-size: 0.875rem;">4</div>
          <div style="flex: 1;">
            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: var(--do-gray-900);">Sucursales</h4>
            <p style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);">Seleccione las sucursales a incluir (todas por defecto)</p>
          </div>
          <span class="step-status" style="padding: 0.25rem 0.75rem; background: var(--do-success-green); color: white; border-radius: 16px; font-size: 0.75rem; font-weight: 500;">OPCIONAL</span>
        </div>
        <div class="step-content">
          <div class="filter-summary" style="display: flex; align-items: center; justify-content: space-between; padding: 1rem; background: var(--do-gray-50); border-radius: 8px; border: 1px solid var(--do-gray-200);">
            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
              <input type="checkbox" id="todas-sucursales" checked onchange="toggleTodasSucursales()" style="width: 18px; height: 18px;">
              <span style="font-weight: 500; color: var(--do-gray-900); font-size: 1rem;">Todas las sucursales</span>
              <span id="sucursales-count" style="color: var(--do-gray-600); font-size: 1rem; font-weight: 500;">(Cargando...)</span>
            </label>
            <button type="button" class="toggle-details" onclick="toggleSucursalesDetails()" style="padding: 0.75rem 1rem; background: var(--vg-sucursal-color); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; display: flex; align-items: center; gap: 0.5rem; transition: all 0.2s ease;">
              <span>Ver detalles</span>
              <i class="fas fa-chevron-down" id="sucursales-chevron"></i>
            </button>
          </div>
          <div class="filter-details" id="sucursales-details" style="display: none; margin-top: 1rem; padding: 1rem; background: var(--do-white); border-radius: 8px; border: 1px solid var(--do-gray-200);">
            <div id="sucursales-list">
              <!-- Las sucursales se cargarán aquí dinámicamente -->
            </div>
          </div>
        </div>
      </div>

      <!-- Resumen y Botón de Generación -->
      <div class="wizard-summary" style="padding: 1.5rem; background: linear-gradient(135deg, var(--do-gray-50) 0%, var(--do-white) 100%); border-radius: 12px; border: 2px solid var(--do-gray-200);">
        <div class="selection-preview" style="margin-bottom: 1rem;">
          <h4 style="margin: 0 0 0.5rem 0; font-size: 1rem; font-weight: 600; color: var(--do-gray-900);">📋 Resumen de Configuración</h4>
          <p id="summary-text" style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);">Seleccione una empresa para comenzar</p>
        </div>
        <button id="btn-generar-reporte" onclick="generarReporteCompleto()" disabled style="width: 100%; padding: 1rem 1.5rem; background: var(--do-gray-400); color: white; border: none; border-radius: 8px; font-size: 1rem; font-weight: 600; cursor: not-allowed; transition: all 0.3s ease; display: flex; align-items: center; justify-content: center; gap: 0.75rem;">
          <i class="fas fa-chart-bar"></i>
          <span>Seleccione una empresa</span>
        </button>
      </div>
    </div>
  </section>

  <!-- Métricas Principales -->
  <section class="metrics-section" id="metrics-section" style="display: none; margin-bottom: 2rem;">
    <div class="metrics-grid">
      <div class="metric-card success">
        <div class="metric-header">
          <div class="metric-icon success">
            <i class="fas fa-chart-line"></i>
          </div>
          <div class="metric-trend positive" id="ventas-trend">
            <i class="fas fa-arrow-up"></i> +0%
          </div>
        </div>
        <div class="metric-value" id="ventas-total">$0.00</div>
        <div class="metric-label">Ventas del Período</div>
        <div class="metric-description" id="ventas-description">Total de ventas del período seleccionado</div>
      </div>

      <div class="metric-card primary">
        <div class="metric-header">
          <div class="metric-icon primary">
            <i class="fas fa-money-bill-wave"></i>
          </div>
          <div class="metric-trend positive" id="pagos-trend">
            <i class="fas fa-arrow-up"></i> +0%
          </div>
        </div>
        <div class="metric-value" id="pagos-total">$0.00</div>
        <div class="metric-label">Pagos Recibidos</div>
        <div class="metric-description" id="pagos-description">Total de pagos cobrados en el período</div>
      </div>

      <div class="metric-card warning">
        <div class="metric-header">
          <div class="metric-icon warning">
            <i class="fas fa-clock"></i>
          </div>
          <div class="metric-trend neutral" id="deuda-trend">
            <i class="fas fa-minus"></i> 0%
          </div>
        </div>
        <div class="metric-value" id="por-cobrar-total">$0.00</div>
        <div class="metric-label">Por Cobrar</div>
        <div class="metric-description" id="deuda-description">Pendiente de cobro</div>
      </div>
    </div>
  </section>


  <!-- Modal de Auditoría Rápida -->
  <div id="modal-auditoria" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; justify-content: center; align-items: center;">
    <div style="background: white; border-radius: 12px; padding: 2rem; max-width: 500px; width: 90%; max-height: 90vh; overflow-y: auto; box-shadow: 0 20px 60px rgba(0,0,0,0.3);">

      <!-- Header del Modal -->
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1.5rem; padding-bottom: 1rem; border-bottom: 2px solid var(--do-gray-200);">
        <div style="display: flex; align-items: center; gap: 0.75rem;">
          <i class="fas fa-clipboard-check" style="color: var(--do-warning-amber); font-size: 1.5rem;"></i>
          <div>
            <h3 style="margin: 0; font-size: 1.25rem; font-weight: 700; color: var(--do-gray-900);">Auditoría Rápida</h3>
            <p id="modal-sucursal-nombre" style="margin: 0; font-size: 0.875rem; color: var(--do-gray-600);"></p>
          </div>
        </div>
        <button onclick="cerrarModalAuditoria()" style="background: none; border: none; font-size: 1.5rem; color: var(--do-gray-400); cursor: pointer; padding: 0.25rem;" onmouseover="this.style.color='var(--do-gray-600)'" onmouseout="this.style.color='var(--do-gray-400)'">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- Contenido del Modal -->
      <div id="modal-auditoria-contenido">

        <!-- Stock del Sistema -->
        <div style="background: var(--do-blue-50); border: 1px solid var(--blue-200); border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
          <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <i class="fas fa-desktop" style="color: var(--do-primary-blue); font-size: 1rem;"></i>
            <span style="font-weight: 600; color: var(--do-primary-blue);">Stock en Sistema</span>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Unidades</div>
              <div id="stock-sistema-unidades" style="font-size: 1.25rem; font-weight: 700; color: var(--do-primary-blue);">0</div>
            </div>
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Valor Total</div>
              <div id="stock-sistema-valor" style="font-size: 1.25rem; font-weight: 700; color: var(--do-primary-blue);">$0</div>
            </div>
          </div>
        </div>

        <!-- Stock Físico (Input) -->
        <div style="background: var(--do-green-50); border: 1px solid var(--green-200); border-radius: 8px; padding: 1rem; margin-bottom: 1rem;">
          <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <i class="fas fa-boxes" style="color: var(--vg-success-soft); font-size: 1rem;"></i>
            <span style="font-weight: 600; color: var(--vg-success-soft);">Stock Físico Real</span>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Unidades Contadas</div>
              <input type="number" id="stock-fisico-input" placeholder="Ingrese cantidad..."
                     style="width: 100%; padding: 0.75rem; border: 2px solid var(--green-300); border-radius: 6px; font-size: 1rem; font-weight: 600;"
                     oninput="calcularDiferencias()">
            </div>
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Valor Estimado</div>
              <div id="stock-fisico-valor" style="font-size: 1.25rem; font-weight: 700; color: var(--vg-success-soft);">$0</div>
            </div>
          </div>
        </div>

        <!-- Diferencias (Calculadas automáticamente) -->
        <div id="diferencias-container" style="background: var(--do-gray-50); border: 1px solid var(--do-gray-200); border-radius: 8px; padding: 1rem; margin-bottom: 1rem; display: none;">
          <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
            <i class="fas fa-calculator" style="color: var(--do-gray-600); font-size: 1rem;"></i>
            <span style="font-weight: 600; color: var(--do-gray-600);">Diferencias Calculadas</span>
          </div>
          <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Unidades</div>
              <div id="diferencia-unidades" style="font-size: 1.25rem; font-weight: 700;">0</div>
            </div>
            <div>
              <div style="font-size: 0.75rem; color: var(--do-gray-600); margin-bottom: 0.25rem;">Valor</div>
              <div id="diferencia-valor" style="font-size: 1.25rem; font-weight: 700;">$0</div>
            </div>
          </div>
        </div>

        <!-- Notas -->
        <div style="margin-bottom: 1.5rem;">
          <label style="display: block; font-weight: 600; color: var(--do-gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-sticky-note" style="margin-right: 0.5rem;"></i>
            Notas de la Auditoría
          </label>
          <textarea id="notas-auditoria" placeholder="Observaciones, discrepancias encontradas, etc..."
                    style="width: 100%; height: 80px; padding: 0.75rem; border: 1px solid var(--do-gray-300); border-radius: 6px; resize: vertical; font-family: inherit;"></textarea>
        </div>

        <!-- Información del Auditor -->
        <div style="background: var(--do-gray-50); border-radius: 6px; padding: 0.75rem; margin-bottom: 1.5rem; font-size: 0.875rem; color: var(--do-gray-600);">
          <div style="display: flex; justify-content: space-between;">
            <span><i class="fas fa-user" style="margin-right: 0.5rem;"></i>Auditor: <strong>{{ app.user.username ?? 'Sistema' }}</strong></span>
            <span><i class="fas fa-calendar" style="margin-right: 0.5rem;"></i>Fecha: <strong id="fecha-auditoria"></strong></span>
          </div>
        </div>

        <!-- Botones de Acción -->
        <div style="display: flex; gap: 1rem; justify-content: flex-end;">
          <button onclick="cerrarModalAuditoria()"
                  style="padding: 0.75rem 1.5rem; background: var(--do-gray-200); color: var(--do-gray-700); border: none; border-radius: 6px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;"
                  onmouseover="this.style.background='var(--do-gray-300)'"
                  onmouseout="this.style.background='var(--do-gray-200)'">
            Cancelar
          </button>
          <button onclick="guardarAuditoria()"
                  style="padding: 0.75rem 1.5rem; background: var(--vg-success-soft); color: white; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; transition: all 0.2s ease;"
                  onmouseover="this.style.background='#059669'"
                  onmouseout="this.style.background='var(--vg-success-soft)'">
            <i class="fas fa-save" style="margin-right: 0.5rem;"></i>
            Guardar Auditoría
          </button>
        </div>

      </div>

      <!-- Loading State -->
      <div id="modal-auditoria-loading" style="display: none; text-align: center; padding: 2rem;">
        <i class="fas fa-spinner fa-spin" style="font-size: 2rem; color: var(--do-primary-blue); margin-bottom: 1rem;"></i>
        <p style="margin: 0; color: var(--do-gray-600);">Guardando auditoría...</p>
      </div>

    </div>
  </div>

  <!-- Análisis de Ingresos Diarios -->
  <section class="detailed-analysis">
    <details class="analysis-section" open style="background: var(--do-white); border-radius: 12px; box-shadow: var(--do-shadow-sm); border: 1px solid var(--do-gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--do-gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-pie" style="color: var(--do-primary-blue);"></i>
        Análisis de Ingresos Diarios
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--do-gray-200);">
        <!-- Charts Grid -->
        <div class="chart-grid">

          <!-- Ingresos por Sucursal -->
          <div class="chart-container" style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-store" style="color: var(--do-success-green);"></i>
              Ingresos por Sucursal
            </h4>
            <div id="graficaSucursal" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Tipos de Pago -->
          <div class="chart-container" style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-credit-card" style="color: var(--do-primary-blue);"></i>
              Tipos de Pago
            </h4>
            <div id="graficaTipoPago" style="height: 300px; min-height: 300px;"></div>
          </div>

          <!-- Deuda por Sucursal -->
          <div class="chart-container" style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-exclamation-triangle" style="color: var(--do-warning-amber);"></i>
              Deuda por Sucursal
            </h4>
            <div id="deudaTotal" style="height: 300px; min-height: 300px; display: flex; align-items: center; justify-content: center; color: var(--do-gray-500);">
              <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem;"></i>
                <p>Cargando datos...</p>
              </div>
            </div>
          </div>

        </div>


      </div>
    </details>
  </section>

  <!-- Inventario Dashboard Section -->
  <section class="inventory-section" id="inventory-section" style="margin-bottom: 2rem;">
    <div style="background: var(--do-white); border-radius: 12px; box-shadow: var(--do-shadow-sm); border: 1px solid var(--do-gray-200); padding: 1.5rem;">
      <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 1.5rem;">
        <h3 style="font-size: 1.5rem; font-weight: 700; color: var(--do-gray-900); margin: 0; display: flex; align-items: center; gap: 0.75rem;">
          <i class="fas fa-boxes" style="color: var(--do-warning-amber); font-size: 1.5rem;"></i>
          Inventario
        </h3>
        <div style="display: flex; align-items: center; gap: 1rem;">
          <span id="inventory-last-update" style="font-size: 1rem; color: var(--do-gray-600); font-weight: 500;">
            Última actualización: --:--
          </span>
          <button id="refresh-inventory" onclick="refreshInventoryData()" style="padding: 0.5rem; background: var(--do-primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.background='#0056b3'" onmouseout="this.style.background='var(--do-primary-blue)'">
            <i class="fas fa-sync-alt"></i>
          </button>
        </div>
      </div>

      <!-- Dashboard Grid -->
      <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 1.5rem;">

        <!-- Top 6 Sucursales -->
        <div class="inventory-card">
          <h4 style="font-size: 1.25rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-store" style="color: var(--vg-sucursal-color); font-size: 1.25rem;"></i>
            Top Sucursales por Stock
          </h4>
          <div id="top-sucursales-container" style="max-height: 300px; overflow-y: auto; overflow-x: hidden; scroll-behavior: smooth;">
            <div class="loading-state" style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
              <i class="fas fa-spinner fa-spin"></i>
              <p style="margin: 0.5rem 0 0 0;">Cargando datos...</p>
            </div>
          </div>
        </div>

        <!-- Top 6 Campañas -->
        <div class="inventory-card">
          <h4 style="font-size: 1.25rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-bullhorn" style="color: var(--vg-campaña-color); font-size: 1.25rem;"></i>
            Campañas con Stock disponible
          </h4>
          <div id="top-campanas-container" style="max-height: 300px; overflow-y: auto; overflow-x: hidden; scroll-behavior: smooth;">
            <div class="loading-state" style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
              <i class="fas fa-spinner fa-spin"></i>
              <p style="margin: 0.5rem 0 0 0;">Cargando datos...</p>
            </div>
          </div>
        </div>

        <!-- Gráfica de Distribución de Stock -->
        <div class="inventory-card">
          <h4 style="font-size: 1.25rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 0.5rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-chart-bar" style="color: var(--do-primary-blue); font-size: 1.25rem;"></i>
            Distribución de Stock por Sucursal
          </h4>
          <div id="total-inventario-global" style="font-size: 0.9rem; color: var(--do-gray-700); margin-bottom: 0.75rem; font-weight: 600;">
            Total global: <span id="total-inventario-global-num">--</span> unidades
          </div>
          <div id="grafica-stock-sucursales" style="height: 300px; min-height: 300px;">
            <div class="loading-state" style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
              <i class="fas fa-spinner fa-spin"></i>
              <p style="margin: 0.5rem 0 0 0;">Cargando gráfica...</p>
            </div>
          </div>
        </div>

      </div>
    </div>
  </section>


  <!-- Análisis Anual Section -->
  <section class="advanced-analysis">
    <details class="analysis-section" open style="background: var(--do-white); border-radius: 12px; box-shadow: var(--do-shadow-sm); border: 1px solid var(--do-gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--do-gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-chart-bar" style="color: var(--do-success-green);"></i>
        Análisis Anual
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--do-gray-200);">
        <!-- Year Selector -->
        <div style="margin-bottom: 1.5rem;">
          <label for="year-select" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--do-gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Seleccionar Año:
          </label>
          <select id="year-select" name="year" style="padding: 0.75rem; border: 1px solid var(--do-gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--do-white); color: var(--do-gray-900); min-width: 150px;">
            <option value="2025" selected>2025</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarVentasAnuales()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--do-success-green); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-sync-alt"></i> Actualizar
          </button>
        </div>

        <!-- Annual Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 1.5rem;">

          <!-- Ventas Mensuales -->
          <div style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--do-success-green);"></i>
              Ventas Mensuales
            </h4>
            <div id="sumaMontos" style="height: 350px; min-height: 350px;"></div>
          </div>

          <!-- Pagos Anuales por Sucursal -->
          <div style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200);">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-pie" style="color: var(--do-primary-blue);"></i>
              Ingresos por Sucursal
            </h4>
            <div id="sumaPagos" style="height: 350px; min-height: 350px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>



  <!-- Facturación Section -->
  <section class="billing-analysis">
    <details class="analysis-section" open style="background: var(--do-white); border-radius: 12px; box-shadow: var(--do-shadow-sm); border: 1px solid var(--do-gray-200); margin-bottom: 2rem;">
      <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--do-gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
        <i class="fas fa-file-invoice-dollar" style="color: var(--do-info);"></i>
        🧾 Análisis de Facturación
        <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
      </summary>

      <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--do-gray-200);">
        <!-- Year Selector for Billing -->
        <div style="margin-bottom: 1.5rem;">
          <label for="year-select-facturacion" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--do-gray-700); margin-bottom: 0.5rem;">
            <i class="fas fa-calendar"></i> Año para Facturación:

          </label>
          <select id="year-select-facturacion" name="year" style="padding: 0.75rem; border: 1px solid var(--do-gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--do-white); color: var(--do-gray-900); min-width: 150px;">
            <option value="2025" selected>2025</option>
            <option value="2024">2024</option>
            <option value="2023">2023</option>
            <option value="2022">2022</option>
          </select>
          <button onclick="buscarDatosFacturacion()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--do-info); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
            <i class="fas fa-sync-alt"></i> Actualizar
          </button>
        </div>

        <!-- Billing Charts Grid -->
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">



          <!-- Facturación Mensual -->
          <div style="background: var(--do-gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--do-gray-200); grid-column: 1 / -1;">
            <h4 style="font-size: 1rem; font-weight: 600; color: var(--do-gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
              <i class="fas fa-chart-line" style="color: var(--do-primary-blue);"></i>
              Evolución Mensual de Facturación
            </h4>
            <div id="Sumaim" style="height: 350px; min-height: 350px;"></div>
          </div>

        </div>
      </div>
    </details>
  </section>





</div>

{% endblock %}

{% block javascripts %}
{{ parent() }}
<!-- Select2 JS (solo si no está incluido en el layout) -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<!-- ApexCharts JS -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<!-- Highcharts JS para gráficas de pastel -->
<script src="https://code.highcharts.com/highcharts.js"></script>
<!-- DataTables JS (solo básico, sin botones de exportación) -->
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>

<script>
// ===== FUNCIONES GLOBALES (DEFINIDAS ANTES DEL DOM READY) =====

// Función para obtener empresa fija (siempre retorna 1 - OPTIMO ÓPTICAS)
function getEmpresaId() {
    const empresaId = '1';
    console.log('🏢 getEmpresaId() llamada, retornando:', empresaId);
    return empresaId;
}

// Función para obtener nombre de empresa fija
function getEmpresaNombre() {
    return $('#idempresa option:selected').text() || 'Empresa Fija';
}

function onEmpresaChange() {
    const empresaId = getEmpresaId();
    const empresaNombre = getEmpresaNombre();

    console.log('🏢 Empresa seleccionada:', empresaId, '-', empresaNombre);
    console.log('🔍 Elemento #sucursales-list existe:', $('#sucursales-list').length > 0);

    if (empresaId && empresaId !== '') {
        // Marcar paso 1 como completado
        completarPaso(1);

        // Habilitar paso 2
        habilitarPaso(2);

        console.log('🚀 Iniciando carga de sucursales...');

        // Cargar sucursales para la empresa fija
        cargarSucursales();

        // Habilitar paso 3
        habilitarPaso(3);

        // Deshabilitar botón de generar reporte hasta que se seleccionen sucursales
        deshabilitarBotonGenerar();

        // Actualizar resumen
        actualizarResumen();

        // Cargar métricas del día actual para la empresa seleccionada
        cargarMetricasDelDia();

    } else {
        console.log('🔄 Reseteando wizard...');
        // Resetear wizard
        resetearWizard();

        // Volver a cargar métricas iniciales (todas las empresas)
        cargarMetricasIniciales();
    }
}

function onFechaChange() {
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();

    console.log('📅 Fechas seleccionadas:', fechaInicio, 'a', fechaFin);

    if (fechaInicio && fechaFin) {
        // Validar que fecha inicio <= fecha fin
        if (new Date(fechaInicio) <= new Date(fechaFin)) {
            completarPaso(2);
            habilitarPaso(3); // Habilitar paso de tipo de venta
            actualizarResumen();

            // Los tipos de venta ya están cargados desde el controlador
            console.log('✅ Tipos de venta ya disponibles en el selector');

            // Actualizar métricas si hay empresa seleccionada
            const empresaId = getEmpresaId();
            if (empresaId) {
                actualizarMetricasPorFiltros();
            }
        } else {
            Swal.fire({
              icon: 'warning',
              title: 'Fechas inválidas',
              text: 'La fecha de inicio no puede ser mayor que la fecha fin',
              confirmButtonColor: '#3085d6'
            });
            $('#fecha-fin').val('');
        }
    }
}

function onTipoVentaChange() {
    // Esta función ahora se llama cuando cambian los checkboxes de tipos de venta
    const tiposSeleccionados = $('input[name="tipoventa"]:checked').length;
    const totalTipos = $('input[name="tipoventa"]').length;

    console.log('Tipos de venta seleccionados:', tiposSeleccionados, 'de', totalTipos);

    // Siempre completar el paso 3 ya que es opcional
    completarPaso(3);
    habilitarPaso(4); // Habilitar paso de sucursales
    actualizarResumen();

    // Actualizar métricas si hay empresa seleccionada
    const empresaId = getEmpresaId();
    if (empresaId) {
        actualizarMetricasPorFiltros();
    }
}

// Inicialización de componentes
$(document).ready(function() {
  console.log('🚀 Inicializando VentasGeneralesController...');

  // Verificar que las librerías estén cargadas
  if (!verificarLibrerias()) {
    console.error('❌ Error: Librerías requeridas no están cargadas');
    return;
  }

  // Inicializar Select2 (verificar que no esté ya inicializado)
  if (!$('#idempresa').hasClass('select2-hidden-accessible')) {
    $('#idempresa').select2({
      theme: 'bootstrap-5',
      placeholder: 'Seleccione una empresa',
      allowClear: true
    });
    console.log('✅ Select2 inicializado correctamente');
  } else {
    console.log('⚠️ Select2 ya estaba inicializado, saltando inicialización');
  }

  // Event listeners para el wizard eliminados - se usan funciones específicas

  // Inicializar DataTable
  $('#tabla-ventas').DataTable({
    language: {
      url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json'
    },
    responsive: true
  });

  // Actualizar fecha y hora
  actualizarFechaHora();
  setInterval(actualizarFechaHora, 1000);

  // Establecer fechas por defecto (HOY)
  const hoy = new Date();

  $('#fecha-inicio').val(formatDate(hoy));
  $('#fecha-fin').val(formatDate(hoy));

  // Marcar el preset "hoy" como activo
  setTimeout(() => {
    $('.preset-btn').removeClass('active');
    $('[onclick="setPresetFecha(\'hoy\')"]').addClass('active');

    // Completar paso 2 (fechas) automáticamente
    completarPaso(2);
    habilitarPaso(3);

    // Actualizar progreso de loading
    updateLoadingProgress('fechas', 'Fechas establecidas para hoy');
  }, 200);

  console.log('📅 Fechas por defecto establecidas a HOY:', formatDate(hoy));

  // Cargar métricas del día actual al inicializar (TODAS las empresas)
  cargarMetricasIniciales();

  // Inicializar contador de tipos de venta
  setTimeout(() => {
    const totalTiposVenta = {{ tipos_venta|length }};
    if (totalTiposVenta > 0) {
      $('#tipos-venta-count').text(`(${totalTiposVenta} tipos)`).css('color', 'var(--vg-success-soft)');
    } else {
      $('#tipos-venta-count').text('(Sin tipos de venta)').css('color', 'var(--vg-error-soft)');
    }
  }, 100);

  // Manejar redimensionamiento de ventana para gráficas responsivas
  let resizeTimeout;
  $(window).on('resize', function() {
    clearTimeout(resizeTimeout);
    resizeTimeout = setTimeout(function() {
      console.log('🔄 Redimensionando gráficas...');

      // Lista de IDs de gráficas a redimensionar
      const graficasIds = [
        'graficaSucursal',
        'graficaTipoPago',
        'graficaDeuda',
        'graficaMarcas',
        'ventasMensuales'
      ];

      // Redimensionar todas las gráficas de ApexCharts
      if (window.ApexCharts) {
        graficasIds.forEach(id => {
          try {
            window.ApexCharts.exec(id, 'updateOptions', {
              chart: {
                width: '100%',
                height: 300
              }
            });
          } catch (e) {
            // Gráfica no existe o no está inicializada
            console.log(`⚠️ No se pudo redimensionar gráfica: ${id}`);
          }
        });

        console.log('✅ Gráficas redimensionadas');
      }
    }, 300);
  });

  // Event listeners para cambios en filtros del wizard
  $(document).on('change', 'input[name="sucursal"]', function() {
    const tipo = $(this).data('tipo');
    if (tipo) {
      updateTipoCounter(tipo);
    }
    actualizarContadorSucursales();
  });

  // INICIALIZACIÓN: Auto-seleccionar empresa 1
  setTimeout(() => {
    const empresasDisponibles = $('#idempresa option').length;
    console.log('🏢 Empresas disponibles:', empresasDisponibles);

    // Verificar que la empresa 1 esté seleccionada
    const empresaSeleccionada = getEmpresaId();
    if (empresaSeleccionada === '1') {
      // console.log('✅ Empresa 1 pre-seleccionada correctamente');

      // Actualizar progreso de loading
      updateLoadingProgress('empresa', 'Empresa configurada correctamente');

      // Marcar paso 1 como completado inmediatamente
      // console.log('🔄 Marcando paso 1 como completado...');
      completarPaso(1);
      // console.log('🔄 Habilitando paso 2...');
      habilitarPaso(2);

      // console.log('🚀 Iniciando configuración automática completa...');

      // Disparar el evento de cambio para cargar datos automáticamente
      console.log('🔄 Disparando carga automática de datos...');

      // Llamar directamente a onEmpresaChange ya que el select está disabled
      onEmpresaChange();

      // Cargar tipos de venta inmediatamente después
      setTimeout(() => {
        console.log('🏷️ Cargando tipos de venta inmediatamente...');
        cargarTiposVenta();
      }, 1500);

      // Auto-configurar tipos de venta después de cargar sucursales
      setTimeout(() => {
        console.log('🏷️ Auto-configurando tipos de venta...');

        // Forzar la carga de tipos de venta directamente
        cargarTiposVenta();

        // También abrir la sección si existe
        const tiposVentaDetails = document.querySelector('details[data-section="tipos-venta"]');
        if (tiposVentaDetails && !tiposVentaDetails.open) {
          tiposVentaDetails.open = true;
          toggleTiposVentaDetails();
        }
      }, 3000); // Esperar más tiempo para que se carguen las sucursales

        // Verificación final de configuración completa
        setTimeout(() => {
          verificarConfiguracionCompleta();

          // Recargar dashboard de inventario después de configuración completa
          console.log('🔄 Recargando dashboard de inventario con configuración completa...');
          loadInventoryData();

          // Cargar todas las gráficas automáticamente
          console.log('📊 Cargando todas las gráficas automáticamente...');
          cargarGraficasSecuencialmente();
        }, 5000);
    } else {
      console.warn('⚠️ Empresa 1 no encontrada, mostrando estado inicial');
      mostrarEstadoInicial();
    }
  }, 500);

  // Inicializar dashboard de inventario
  console.log('🏭 Inicializando Dashboard de Inventario...');
  initInventoryDashboard();
});

// Función para actualizar fecha y hora
function actualizarFechaHora() {
  const ahora = new Date();
  const opciones = { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' };

  $('#current-date').text(ahora.toLocaleDateString('es-ES', opciones));
  $('#current-time').text(ahora.toLocaleTimeString('es-ES'));
}

// Función para formatear fecha en YYYY-MM-DD
function formatDate(date) {
  const d = new Date(date);
  let month = '' + (d.getMonth() + 1);
  let day = '' + d.getDate();
  const year = d.getFullYear();

  if (month.length < 2) month = '0' + month;
  if (day.length < 2) day = '0' + day;

  return [year, month, day].join('-');
}

// Función para cambiar tema
function toggleTheme() {
  const dashboard = document.querySelector('.executive-dashboard');
  const currentTheme = dashboard.getAttribute('data-theme');
  const newTheme = currentTheme === 'light' ? 'dark' : 'light';

  dashboard.setAttribute('data-theme', newTheme);
  document.getElementById('theme-icon').textContent = newTheme === 'light' ? '🌙' : '☀️';
}

// Función para mostrar/ocultar panel de sucursales
function toggleSucursalesPanel() {
  const panel = document.getElementById('sucursales-panel');
  panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Función para mostrar/ocultar panel de clases
function toggleClasesPanel() {
  const panel = document.getElementById('clases-panel');
  panel.style.display = panel.style.display === 'none' ? 'block' : 'none';
}

// Función duplicada eliminada - se usa la versión mejorada más abajo

// Función para buscar ingresos diarios
function buscarIngresosDiarios() {
  const idempresa = getEmpresaId();
  const fechaInicio = $('#fecha-inicio').val();
  const fechaFin = $('#fecha-fin').val();

  // Validar datos
  if (idempresa === '-1') {
    Swal.fire({
      icon: 'warning',
      title: 'Empresa requerida',
      text: 'Por favor seleccione una empresa',
      confirmButtonColor: '#3085d6'
    });
    return;
  }

  if (!fechaInicio || !fechaFin) {
    Swal.fire({
      icon: 'warning',
      title: 'Fechas requeridas',
      text: 'Por favor seleccione fechas de inicio y fin',
      confirmButtonColor: '#3085d6'
    });
    return;
  }

  // Obtener sucursales seleccionadas
  const sucursales = [];
  $('input[name="sucursal"]:checked').each(function() {
    sucursales.push($(this).val());
  });

  // Obtener clases seleccionadas
  const clases = [];
  $('input[name="clase"]:checked').each(function() {
    clases.push($(this).val());
  });

  // Realizar petición AJAX
  $.ajax({
    url: '{{ path("ventas-generales-generar-reporte") }}',
    type: 'POST',
    data: {
      idempresa: idempresa,
      sucursales: sucursales,
      clases: clases,
      fecha_inicio: fechaInicio,
      fecha_fin: fechaFin
    },
    success: function(response) {
      if (response.success) {
        // Actualizar métricas usando los datos del reporte
        const fechaInicio = $('#fecha-inicio').val();
        const fechaFin = $('#fecha-fin').val();

        // Convertir strings a números para el formateo correcto
        const datosMetricas = {
            ventas_total: parseFloat(response.datos.ventas_total.replace(/,/g, '') || 0),
            pagos_total: parseFloat(response.datos.pagos_total.replace(/,/g, '') || 0),
            por_cobrar_total: parseFloat(response.datos.por_cobrar_total.replace(/,/g, '') || 0),
            total_ventas: response.datos.total_ventas || 0,
            promedio_venta: parseFloat(response.datos.promedio_venta || 0),
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin
        };

        actualizarMetricas(datosMetricas);

        // Aquí se actualizarían los gráficos y tablas con los datos recibidos
        actualizarGraficos(response.datos);
        actualizarTablaVentas(response.datos);
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error al generar reporte',
          text: 'Error al generar el reporte: ' + (response.mensaje || 'Error desconocido'),
          confirmButtonColor: '#d33'
        });
      }
    },
    error: function() {
      Swal.fire({
        icon: 'error',
        title: 'Error de conexión',
        text: 'No se pudo conectar con el servidor. Verifique su conexión a internet.',
        confirmButtonColor: '#d33'
      });
    }
  });
}

// Función para actualizar gráficos
function actualizarGraficos(datos) {
  // Ejemplo de implementación - En una aplicación real, estos datos vendrían del servidor
  const datosSucursales = {
    categorias: ['Sucursal A', 'Sucursal B', 'Sucursal C', 'Sucursal D'],
    valores: [12500, 8700, 7300, 5200]
  };

  const datosClases = {
    categorias: ['Armazones', 'Lentes', 'Accesorios', 'Otros'],
    valores: [15000, 9800, 4500, 3200]
  };

  // Gráfico de ingresos por sucursal
  new ApexCharts(document.querySelector('#chart-ingresos-sucursal'), {
    series: [{
      name: 'Ingresos',
      data: datosSucursales.valores
    }],
    chart: {
      type: 'bar',
      height: 300,
      toolbar: {
        show: false
      }
    },
    plotOptions: {
      bar: {
        horizontal: false,
        columnWidth: '55%',
        borderRadius: 4
      }
    },
    dataLabels: {
      enabled: false
    },
    xaxis: {
      categories: datosSucursales.categorias
    },
    colors: ['#3B82F6'],
    theme: {
      mode: 'light'
    }
  }).render();

  // Gráfico de ingresos por clase
  new ApexCharts(document.querySelector('#chart-ingresos-clase'), {
    series: datosClases.valores,
    chart: {
      type: 'donut',
      height: 300
    },
    labels: datosClases.categorias,
    colors: ['#3B82F6', '#10B981', '#F59E0B', '#6366F1'],
    theme: {
      mode: 'light'
    },
    legend: {
      position: 'bottom'
    }
  }).render();
}

// Función para actualizar tabla de ventas
function actualizarTablaVentas(datos) {
  // Limpiar tabla
  const tabla = $('#tabla-ventas').DataTable();
  tabla.clear();

  // Ejemplo de implementación - En una aplicación real, estos datos vendrían del servidor
  const datosEjemplo = [
    [1001, '2025-07-30', 'Sucursal A', 'Armazones', 'Armazón Modelo X', 2, '$1,200.00', '$2,400.00'],
    [1002, '2025-07-30', 'Sucursal B', 'Lentes', 'Lente Progresivo', 1, '$3,500.00', '$3,500.00'],
    [1003, '2025-07-31', 'Sucursal A', 'Accesorios', 'Estuche Premium', 5, '$250.00', '$1,250.00'],
    [1004, '2025-07-31', 'Sucursal C', 'Armazones', 'Armazón Modelo Y', 1, '$1,800.00', '$1,800.00'],
    [1005, '2025-08-01', 'Sucursal D', 'Lentes', 'Lente Bifocal', 2, '$2,200.00', '$4,400.00']
  ];

  // Agregar datos a la tabla
  tabla.rows.add(datosEjemplo).draw();
}

// ===== FUNCIONES PRINCIPALES DE CARGA =====

// Función para actualizar contador de sucursales (versión wizard con tipos)
function actualizarContadorSucursales() {
    const checkboxes = $('input[name="sucursal"]:checked');
    const contador = $('#sucursales-count');

    if (contador.length > 0) {
        const total = $('input[name="sucursal"]').length;
        const seleccionadas = checkboxes.length;

        // Contar por tipos
        const tiposCounts = {};
        checkboxes.each(function() {
            const tipo = $(this).data('tipo');
            if (tipo) {
                tiposCounts[tipo] = (tiposCounts[tipo] || 0) + 1;
            }
        });

        // Crear texto descriptivo
        let textoContador = '';
        if (seleccionadas === 0) {
            textoContador = '(Ninguna seleccionada)';
            contador.css('color', 'var(--vg-error-soft)');
            $('#todas-sucursales').prop('checked', false);
        } else if (seleccionadas === total) {
            textoContador = `(${total} ubicaciones)`;
            contador.css('color', 'var(--vg-success-soft)');
            $('#todas-sucursales').prop('checked', true);
        } else {
            // Mostrar desglose por tipos
            const tiposTexto = Object.entries(tiposCounts).map(([tipo, count]) => {
                const icon = tipo === 'sucursal' ? '🏪' : tipo === 'bodega' ? '📦' : tipo === 'campaña' ? '🎯' : '📍';
                return `${icon}${count}`;
            }).join(' ');

            textoContador = `(${seleccionadas}/${total}: ${tiposTexto})`;
            contador.css('color', 'var(--vg-sucursal-color)');
            $('#todas-sucursales').prop('checked', false);
        }

        contador.text(textoContador);

        console.log(`📊 Contador actualizado: ${seleccionadas}/${total} ubicaciones`);
        console.log('📊 Por tipos:', tiposCounts);

        // Completar paso 4 si hay sucursales seleccionadas
        if (seleccionadas > 0) {
            completarPaso(4);
        }

        // Actualizar resumen automáticamente
        actualizarResumen();
    }
}

// Función para actualizar todas las gráficas
function actualizarTodasLasGraficas() {
    console.log('Actualizando todas las gráficas del dashboard...');
    mostrarBarraProgreso();
    cargarGraficasSecuencialmente();
}

// Función para cargar gráficas secuencialmente
function cargarGraficasSecuencialmente() {
    // Prevenir múltiples cargas simultáneas
    if (graficasCargandose) {
        console.log('⚠️ Las gráficas ya se están cargando, saltando...');
        return;
    }

    graficasCargandose = true;
    console.log('🔄 Iniciando carga secuencial de gráficas...');

    try {
        // Usar empresa fija
        const empresaSeleccionada = getEmpresaId();
        if (!empresaSeleccionada) {
            console.warn('⚠️ Empresa fija no disponible, no se pueden cargar gráficas');
            return;
        }

        console.log('🏢 Cargando gráficas para empresa:', empresaSeleccionada);

        // Cargar gráficas de ingresos diarios
        setTimeout(() => {
            cargarGraficasIngresosDiarios();
        }, 200);

        // Cargar gráficas anuales
        setTimeout(() => {
            cargarGraficasVentasAnuales();
        }, 800);



        // Cargar gráficas de facturación
        setTimeout(() => {
            cargarGraficasFacturacion();
        }, 2000);

        // Ocultar barra de progreso al final
        setTimeout(() => {
            ocultarBarraProgreso();
            graficasCargandose = false; // Reset de la bandera
            console.log('✅ Carga secuencial completada');
        }, 2500);

    } catch (error) {
        console.error('❌ Error en carga secuencial:', error);
        ocultarBarraProgreso();
        graficasCargandose = false; // Reset de la bandera en caso de error
    }
}

// Función para buscar ventas anuales
function buscarVentasAnuales() {
    console.log('Buscando ventas anuales...');

    const year = document.getElementById('year-select').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (sucursales.length === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Sucursales requeridas',
          text: 'Por favor seleccione al menos una sucursal',
          confirmButtonColor: '#3085d6'
        });
        return;
    }

    cargarGraficasVentasAnuales();
    mostrarMensajeExito('Ventas anuales actualizadas correctamente');
}

// Función para buscar datos de facturación
function buscarDatosFacturacion() {
    console.log('🔄 Iniciando búsqueda de datos de facturación...');

    const year = document.getElementById('year-select-facturacion').value || new Date().getFullYear();
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    if (!sucursales) {
        Swal.fire({
          icon: 'warning',
          title: 'Sucursales requeridas',
          text: 'Por favor seleccione al menos una sucursal',
          confirmButtonColor: '#3085d6'
        });
        return;
    }

    cargarGraficasFacturacion();
    mostrarMensajeExito('Datos de facturación actualizados correctamente');
}

// Función para resetear rango de fechas
// Función resetRangoFechaDias eliminada (era parte de configuración avanzada)

// ===== FUNCIONES DE CARGA DE GRÁFICAS =====

// Cargar gráficas de ingresos diarios
function cargarGraficasIngresosDiarios() {
    console.log('📊 Cargando gráficas de ingresos diarios...');

    try {
        // Gráfica de ingresos por sucursal
        setTimeout(() => cargarGraficaSucursal(), 100);

        // Gráfica de tipos de pago
        setTimeout(() => cargarGraficaTipoPago(), 300);

        // Gráfica de deuda por sucursal
        setTimeout(() => cargarGraficaDeuda(), 500);

        // Tabla de ingresos diarios
        setTimeout(() => cargarTablaIngresosDiarios(), 700);

        console.log('✅ Gráficas de ingresos diarios iniciadas');

    } catch (error) {
        console.error('❌ Error al cargar gráficas de ingresos diarios:', error);
    }
}

// Cargar gráficas de ventas anuales
function cargarGraficasVentasAnuales() {
    console.log('Cargando gráficas de ventas anuales...');

    // Gráfica de ventas mensuales
    cargarGraficaVentasMensuales();

    // Gráfica de ingresos por sucursal
    cargarGraficaPagosAnuales();
}

// Funciones de productos y marcas eliminadas - ya no se usan

// Cargar gráficas de facturación
function cargarGraficasFacturacion() {
    console.log('Cargando gráficas de facturación...');

    // Solo gráfica de facturación mensual (las otras fueron eliminadas)
    cargarGraficaFacturacionMensual();
}

// ===== FUNCIONES ESPECÍFICAS DE GRÁFICAS =====

function cargarGraficaSucursal() {
    console.log('📊 Cargando gráfica de sucursales...');

    try {
        if (!verificarLibrerias() || !verificarElemento('graficaSucursal')) return;

        mostrarCargando('graficaSucursal', 'Cargando ingresos por sucursal...');

        // Simular delay de carga real
        setTimeout(() => {
            try {
                // Obtener filtros actuales
                const empresaId = getEmpresaId();
                const fechaInicio = $('#fecha-inicio').val();
                const fechaFin = $('#fecha-fin').val();
                const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
                const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

                // Llamar al endpoint real con filtros
                $.ajax({
                    url: '{{ path("ventas-generales-grafica-sucursales") }}',
                    method: 'GET',
                    data: {
                        idempresa: empresaId || null,
                        fecha_inicio: fechaInicio,
                        fecha_fin: fechaFin,
                        sucursales: sucursalesSeleccionadas,
                        tipos_venta: tiposVentaSeleccionados
                    },
                    success: function(response) {
                        if (response.success) {
                            const categorias = response.categorias;
                            const datos = response.datos;

                            // Continuar con la creación de la gráfica
                            crearGraficaSucursales(categorias, datos);
                        } else {
                            console.error('Error al obtener datos de sucursales');
                            // Usar datos de fallback
                            crearGraficaSucursales(['Sin datos'], [0]);
                        }
                    },
                    error: function() {
                        console.error('Error de conexión al obtener datos de sucursales');
                        // Usar datos de fallback
                        crearGraficaSucursales(['Error de conexión'], [0]);
                    }
                });
            } catch (error) {
                console.error('❌ Error al cargar datos de gráfica de sucursales:', error);
                // Usar datos de fallback en caso de error
                crearGraficaSucursales(['Error'], [0]);
            }

            function crearGraficaSucursales(categorias, datos) {
                try {
                    const options = {
                        chart: {
                            type: 'bar',
                            height: 300,
                            width: '100%',
                            toolbar: { show: true },
                            id: 'graficaSucursal',
                            animations: {
                                enabled: true,
                                easing: 'easeinout',
                                speed: 800
                            }
                        },
                        series: [{
                            name: 'Ventas',
                            data: datos
                        }],
                        xaxis: {
                            categories: categorias,
                            labels: {
                                rotate: -45,
                                maxHeight: 120
                            }
                        },
                        colors: ['#10B981'],
                        dataLabels: {
                            enabled: true,
                            formatter: function (val) {
                                return '$' + val.toLocaleString();
                            }
                        },
                        tooltip: {
                            y: {
                                formatter: function (val) {
                                    return '$' + val.toLocaleString();
                                }
                            }
                        },
                        responsive: [{
                            breakpoint: 768,
                            options: {
                                chart: {
                                    height: 250
                                },
                                xaxis: {
                                    labels: {
                                        rotate: -90
                                    }
                                }
                            }
                        }]
                    };

                    // Limpiar contenedor antes de crear nueva gráfica
                    document.getElementById('graficaSucursal').innerHTML = '';

                    const chart = new ApexCharts(document.querySelector("#graficaSucursal"), options);
                    chart.render();

                    console.log('✅ Gráfica de sucursales cargada exitosamente');

                } catch (error) {
                    console.error('❌ Error al renderizar gráfica de sucursales:', error);
                    mostrarError('graficaSucursal', 'Error al cargar gráfica de sucursales');
                }
            } // Cierre de función crearGraficaSucursales
        }, 500);

    } catch (error) {
        console.error('❌ Error en cargarGraficaSucursal:', error);
        mostrarError('graficaSucursal', 'Error al cargar gráfica de sucursales');
    }
}

function cargarGraficaTipoPago() {
    console.log('💳 Cargando gráfica de tipos de pago...');

    try {
        if (!verificarLibrerias() || !verificarElemento('graficaTipoPago')) return;

        mostrarCargando('graficaTipoPago', 'Cargando tipos de pago...');

        setTimeout(() => {
            try {
                // Obtener filtros actuales
                const empresaId = getEmpresaId();
                const fechaInicio = $('#fecha-inicio').val();
                const fechaFin = $('#fecha-fin').val();
                const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
                const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

                // Llamar endpoint con paymenttype agregado (detalle diario)
                $.ajax({
                    url: '/cliente-api/get-ingreso-diario-details',
                    method: 'GET',
                    data: {
                        todayDate: fechaFin,
                        startDate: fechaInicio,
                        endDate: fechaFin,
                        sucursales: sucursalesSeleccionadas.join(','),
                        category: null,
                        class: null
                    },
                    success: function(response) {
                        const data = Array.isArray(response?.dataPerPaymentType) ? response.dataPerPaymentType : [];

                        if (data.length === 0) {
                            mostrarGraficaVacia('graficaTipoPago', 'No hay datos de tipos de pago');
                            return;
                        }

                        const labels = data.map(d => d.paymentType || 'Otro');
                        const valores = data.map(d => parseFloat(d.cobrado || 0));

                        const options = {
                            chart: { type: 'bar', height: 300, id: 'graficaTipoPago' },
                            series: [{ name: 'Cobrado', data: valores }],
                            xaxis: { categories: labels, labels: { rotate: -25 } },
                            colors: ['#3B82F6'],
                            dataLabels: { enabled: false },
                            tooltip: { y: { formatter: (val) => '$' + (parseFloat(val || 0)).toLocaleString() } },
                            legend: { show: false }
                        };

                        document.getElementById('graficaTipoPago').innerHTML = '';
                        const chart = new ApexCharts(document.querySelector('#graficaTipoPago'), options);
                        chart.render();

                        console.log('✅ Gráfica de tipos de pago cargada exitosamente');
                    },
                    error: function() {
                        console.error('❌ Error al cargar tipos de pago');
                        mostrarGraficaVacia('graficaTipoPago', 'Error al cargar datos');
                    }
                });

            } catch (error) {
                console.error('❌ Error al renderizar gráfica de tipos de pago:', error);
                mostrarError('graficaTipoPago', 'Error al cargar gráfica de tipos de pago');
            }
        }, 700);

    } catch (error) {
        console.error('❌ Error en cargarGraficaTipoPago:', error);
        mostrarError('graficaTipoPago', 'Error al cargar gráfica de tipos de pago');
    }
}

function cargarGraficaDeuda() {
    console.log('⚠️ Cargando gráfica de deuda...');

    try {
        if (!verificarLibrerias() || !verificarElemento('deudaTotal')) return;

        mostrarCargando('deudaTotal', 'Cargando deuda por sucursal...');

        setTimeout(() => {
            try {
                // Obtener datos de forma asíncrona
                obtenerDatosDeudaSucursales().then(datosDeuda => {
                    const options = {
                        chart: {
                            type: 'bar',
                            height: 300,
                            toolbar: { show: true },
                            id: 'graficaDeuda'
                        },
                        series: [{
                            name: 'Deuda',
                            data: datosDeuda
                        }],
                        colors: ['#F59E0B'],
                        dataLabels: {
                            enabled: true,
                            formatter: function (val) {
                                return '$' + val.toLocaleString();
                            }
                        },
                        tooltip: {
                            y: {
                                formatter: function (val) {
                                    return '$' + val.toLocaleString();
                                }
                            }
                        },
                        plotOptions: {
                            bar: {
                                horizontal: false,
                                columnWidth: '55%',
                            }
                        }
                    };

                    // Limpiar contenedor
                    document.getElementById('deudaTotal').innerHTML = '';

                    const chart = new ApexCharts(document.querySelector("#deudaTotal"), options);
                    chart.render();

                    console.log('✅ Gráfica de deuda cargada exitosamente');
                }).catch(error => {
                    console.error('❌ Error al obtener datos de deuda:', error);
                    mostrarError('deudaTotal', 'Error al cargar datos de deuda');
                });

            } catch (error) {
                console.error('❌ Error al renderizar gráfica de deuda:', error);
                mostrarError('deudaTotal', 'Error al cargar gráfica de deuda');
            }
        }, 900);

    } catch (error) {
        console.error('❌ Error en cargarGraficaDeuda:', error);
        mostrarError('deudaTotal', 'Error al cargar gráfica de deuda');
    }
}

function cargarGraficaVentasMensuales() {
    console.log('📈 Cargando gráfica de ventas mensuales (dinámico desde API)...');

    try {
        if (!verificarLibrerias() || !verificarElemento('sumaMontos')) return;

        mostrarCargando('sumaMontos', 'Cargando ventas mensuales...');

        const year = document.getElementById('year-select')?.value || new Date().getFullYear();
        const sucursales = (typeof obtenerSucursalesSeleccionadas === 'function')
            ? obtenerSucursalesSeleccionadas().join(',')
            : [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

        const nombresMeses = ['Ene','Feb','Mar','Abr','May','Jun','Jul','Ago','Sep','Oct','Nov','Dic'];
        const limiteMes = (parseInt(year) === new Date().getFullYear()) ? (new Date().getMonth() + 1) : 12; // YTD

        $.get(`/cliente-api/get-ingreso-anual-overview?year=${year}&sucursales=${sucursales}`)
            .done(function (response) {
                try {
                    const data = Array.isArray(response?.dataPerMonth) ? response.dataPerMonth : [];

                    // Mapear mes -> monto (backend devuelve {ventaMonth, cobrado})
                    const mapa = new Map();
                    data.forEach(item => {
                        const mes = parseInt(item.ventaMonth);
                        const valor = parseFloat(item.cobrado || 0);
                        if (!isNaN(mes) && mes >= 1 && mes <= 12) {
                            mapa.set(mes, (mapa.get(mes) || 0) + (isNaN(valor) ? 0 : valor));
                        }
                    });

                    const categorias = nombresMeses.slice(0, limiteMes);
                    const serie = Array.from({ length: limiteMes }, (_, i) => {
                        const mes = i + 1;
                        return mapa.get(mes) || 0;
                    });

                    const options = {
                        chart: {
                            type: 'bar',
                            height: 350,
                            toolbar: { show: true }
                        },
                        series: [{ name: 'Ventas', data: serie }],
                        xaxis: { categories: categorias },
                        colors: ['#10B981'],
                        stroke: { curve: 'smooth', width: 3 },
                        markers: { size: 4 },
                        tooltip: {
                            y: { formatter: val => '$' + (parseFloat(val || 0)).toLocaleString() }
                        },
                        yaxis: {
                            labels: {
                                formatter: val => '$' + (parseFloat(val || 0)).toLocaleString()
                            }
                        }
                    };

                    document.getElementById('sumaMontos').innerHTML = '';
                    const chart = new ApexCharts(document.querySelector('#sumaMontos'), options);
                    chart.render();

                    console.log('✅ Gráfica de ventas mensuales cargada desde API');
                } catch (error) {
                    console.error('❌ Error procesando datos de ventas mensuales:', error);
                    mostrarError('sumaMontos', 'Error al procesar datos de ventas mensuales');
                }
            })
            .fail(function (error) {
                console.error('❌ Error al consultar ventas mensuales:', error);
                mostrarError('sumaMontos', 'No se pudieron obtener las ventas del año');
            });

    } catch (error) {
        console.error('❌ Error en cargarGraficaVentasMensuales:', error);
        mostrarError('sumaMontos', 'Error al cargar gráfica de ventas mensuales');
    }
}

function cargarGraficaPagosAnuales() {
    console.log('📊 Cargando gráfica de ingresos por sucursal (anual por año y sucursales)...');

    try {
        if (!verificarLibrerias() || !verificarElemento('sumaPagos')) return;

        mostrarCargando('sumaPagos', 'Cargando ingresos por sucursal...');

        const year = document.getElementById('year-select')?.value || new Date().getFullYear();
        const sucursales = (typeof obtenerSucursalesSeleccionadas === 'function')
            ? obtenerSucursalesSeleccionadas().join(',')
            : [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

        $.get(`/cliente-api/get-ingreso-anual-overview?year=${year}&sucursales=${sucursales}`)
            .done(function(response) {
                const data = Array.isArray(response?.dataPerBranchPerMonth) ? response.dataPerBranchPerMonth : [];
                if (data.length === 0) {
                    mostrarGraficaVacia('sumaPagos', 'No hay datos para el período seleccionado');
                    return;
                }

                // Construir categorías = sucursales (TOP 10) y series = meses
                const nombresMeses = ['Ene','Feb','Mar','Abr','May','Jun','Jul','Ago','Sep','Oct','Nov','Dic'];
                const yearSel = parseInt(year);
                const limiteMes = (yearSel === new Date().getFullYear()) ? (new Date().getMonth() + 1) : 12;

                // Totales por sucursal para ordenar TOP 10
                const totalesSucursal = new Map();
                data.forEach(row => {
                    const s = row.sucursal || 'Sucursal';
                    const v = parseFloat(row.cobrado || 0);
                    totalesSucursal.set(s, (totalesSucursal.get(s) || 0) + (isNaN(v) ? 0 : v));
                });

                let sucursalesList = Array.from(totalesSucursal.keys());
                sucursalesList.sort((a,b) => (totalesSucursal.get(b) - totalesSucursal.get(a)));
                sucursalesList = sucursalesList.slice(0, 10);

                // Inicializar matriz mes -> sucursal -> valor
                const matrizMesSucursal = Array.from({length: limiteMes}, () => Array(sucursalesList.length).fill(0));

                data.forEach(row => {
                    const s = row.sucursal || 'Sucursal';
                    const m = parseInt(row.ventaMonth);
                    const v = parseFloat(row.cobrado || 0);
                    if (!isNaN(m) && m>=1 && m<=limiteMes) {
                        const idx = sucursalesList.indexOf(s);
                        if (idx !== -1) {
                            matrizMesSucursal[m-1][idx] += isNaN(v) ? 0 : v;
                        }
                    }
                });

                const categorias = sucursalesList; // eje X: sucursales
                const series = Array.from({length: limiteMes}, (_, i) => ({
                    name: nombresMeses[i],
                    data: matrizMesSucursal[i]
                }));

                const options = {
                    chart: { type: 'bar', height: 420, stacked: true, id: 'graficaPagosAnuales' },
                    series: series, // cada serie es un mes
                    xaxis: { categories: categorias, labels: { rotate: -25 } },
                    colors: ['#3B82F6','#10B981','#F59E0B','#EF4444','#8B5CF6','#F97316','#06B6D4','#84CC16','#A855F7','#22C55E','#F59E0B','#EF4444'],
                    dataLabels: { enabled: false },
                    plotOptions: { bar: { borderRadius: 3, columnWidth: '60%' } },
                    yaxis: { labels: { formatter: (val) => '$' + (parseFloat(val || 0)).toLocaleString() } },
                    tooltip: {
                        shared: true,
                        intersect: false,
                        y: { formatter: (val) => '$' + (parseFloat(val || 0)).toLocaleString() }
                    },
                    legend: { position: 'bottom' }
                };

                document.getElementById('sumaPagos').innerHTML = '';
                const chart = new ApexCharts(document.querySelector('#sumaPagos'), options);
                chart.render();

                console.log('✅ Gráfica de ingresos por sucursal (anual) cargada');
            })
            .fail(function(err) {
                console.error('❌ Error al cargar ingresos por sucursal:', err);
                mostrarGraficaVacia('sumaPagos', 'Error al cargar datos');
            });
    } catch (e) {
        console.error('❌ Error en cargarGraficaPagosAnuales:', e);
        mostrarGraficaVacia('sumaPagos', 'Error al cargar datos');
    }
}

function cargarGraficaRecuentoMarcas() {
    console.log('📊 Cargando gráfica de marcas...');

    // Obtener filtros actuales
    const empresaId = $('#idempresa').val();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
    const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

    // Llamar al endpoint real con filtros
    $.ajax({
        url: '{{ path("ventas-generales-grafica-productos-marcas") }}',
        method: 'GET',
        data: {
            idempresa: empresaId || null,
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin,
            sucursales: sucursalesSeleccionadas,
            tipos_venta: tiposVentaSeleccionados,
            tipo: 'marcas'
        },
        success: function(response) {
            if (response.success && response.categorias.length > 0) {
                const options = {
                    chart: {
                        type: 'bar',
                        height: 350,
                        id: 'graficaMarcas'
                    },
                    series: [{
                        name: 'Ventas por Marca',
                        data: response.datos
                    }],
                    xaxis: {
                        categories: response.categorias
                    },
                    colors: ['#F59E0B'],
                    dataLabels: {
                        enabled: true,
                        formatter: function (val) {
                            return '$' + val.toLocaleString();
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return '$' + val.toLocaleString();
                            }
                        }
                    }
                };

                // Limpiar contenedor
                document.getElementById('recuentoMarcas').innerHTML = '';

                const chart = new ApexCharts(document.querySelector("#recuentoMarcas"), options);
                chart.render();

                console.log('✅ Gráfica de marcas cargada con datos reales');
            } else {
                console.log('⚠️ No hay datos de marcas para mostrar');
                mostrarGraficaVacia('recuentoMarcas', 'No hay datos de marcas para el período seleccionado');
            }
        },
        error: function() {
            console.error('❌ Error al cargar datos de marcas');
            mostrarGraficaVacia('recuentoMarcas', 'Error al cargar datos de marcas');
        }
    });
}

function cargarGraficaTratamientos() {
    console.log('📊 Cargando gráfica de tratamientos...');

    // Obtener filtros actuales
    const empresaId = $('#idempresa').val();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
    const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

    // Llamar al endpoint real con filtros
    $.ajax({
        url: '{{ path("ventas-generales-grafica-tratamientos") }}',
        method: 'GET',
        data: {
            idempresa: empresaId || null,
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin,
            sucursales: sucursalesSeleccionadas,
            tipos_venta: tiposVentaSeleccionados
        },
        success: function(response) {
            if (response.success && response.categorias.length > 0) {
                const options = {
                    chart: {
                        type: 'pie',
                        height: 350,
                        id: 'graficaTratamientos'
                    },
                    series: response.datos,
                    labels: response.categorias,
                    colors: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#F97316', '#06B6D4'],
                    dataLabels: {
                        enabled: true,
                        formatter: function (val) {
                            return val.toFixed(1) + '%';
                        }
                    },
                    tooltip: {
                        y: {
                            formatter: function (val) {
                                return '$' + val.toLocaleString();
                            }
                        }
                    },
                    legend: {
                        position: 'bottom',
                        horizontalAlign: 'center'
                    }
                };

                // Limpiar contenedor
                document.getElementById('tratamientoGrafica').innerHTML = '';

                const chart = new ApexCharts(document.querySelector("#tratamientoGrafica"), options);
                chart.render();

                console.log('✅ Gráfica de tratamientos cargada con datos reales');
            } else {
                console.log('⚠️ No hay datos de tratamientos para mostrar');
                mostrarGraficaVacia('tratamientoGrafica', 'No hay datos de tratamientos para el período seleccionado');
            }
        },
        error: function() {
            console.error('❌ Error al cargar datos de tratamientos');
            mostrarGraficaVacia('tratamientoGrafica', 'Error al cargar datos de tratamientos');
        }
    });
}

// Funciones de facturación por estado y total facturado eliminadas

function cargarGraficaFacturacionMensual() {
    const options = {
        chart: { type: 'area', height: 350 },
        series: [{
            name: 'Facturación',
            data: [80000, 95000, 110000, 125000, 140000, 135000, 150000, 145000, 160000, 155000, 170000, 165000]
        }],
        xaxis: {
            categories: ['Ene', 'Feb', 'Mar', 'Abr', 'May', 'Jun', 'Jul', 'Ago', 'Sep', 'Oct', 'Nov', 'Dic']
        },
        colors: ['#3B82F6'],
        fill: { type: 'gradient' }
    };

    const chart = new ApexCharts(document.querySelector("#Sumaim"), options);
    chart.render();
}

// ===== FUNCIONES DE TABLAS =====

function cargarTablaIngresosDiarios() {
    console.log('📊 Iniciando carga de tabla de ingresos diarios...');

    // Verificar y destruir tabla existente
    if ($.fn.DataTable.isDataTable('#tablaIngresosDiarios')) {
        console.log('🔄 Destruyendo tabla existente...');
        $('#tablaIngresosDiarios').DataTable().destroy();
        $('#tablaIngresosDiarios').empty();
    }

    // Obtener filtros actuales
    const empresaId = $('#idempresa').val();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
    const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

    // Llamar al nuevo endpoint dinámico de tabla de ingresos
    $.ajax({
        url: '{{ path("ventas-generales-tabla-ingresos") }}',
        method: 'GET',
        data: {
            idempresa: empresaId || null,
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin,
            sucursales: sucursalesSeleccionadas,
            tipos_venta: tiposVentaSeleccionados
        },
        success: function(response) {
            console.log('✅ Respuesta del endpoint de tabla de ingresos:', response);
            const datosTabla = [];

            if (response.success && response.datos && response.datos.length > 0) {
                response.datos.forEach(fila => {
                    // Formatear tipo de sucursal con icono y color
                    const tipoHtml = `<span style="color: ${fila.tipo.color}; font-weight: 600;">${fila.tipo.icono} ${fila.tipo.tipo}</span>`;

                    // Formatear montos
                    const ventasHtml = '$' + Math.round(fila.ventas_totales).toLocaleString('es-MX');
                    const ingresosHtml = `<span style="color: #50C878; font-weight: 600;">$${Math.round(fila.ingresos).toLocaleString('es-MX')}</span>`;
                    const deudaHtml = fila.deuda > 0 ?
                        `<span style="color: #FF6B6B; font-weight: 600;">$${Math.round(fila.deuda).toLocaleString('es-MX')}</span>` :
                        '<span style="color: #50C878;">$0</span>';

                    // Formatear tipos de venta como badges
                    const tiposVentaHtml = fila.tipos_venta.length > 0 ?
                        `<div style="display: flex; gap: 4px; flex-wrap: wrap;">${fila.tipos_venta.map(tipo =>
                            `<span class="badge-tipo-venta">${tipo.nombre}</span>`
                        ).join('')}</div>` :
                        '<span style="color: #999;">Sin tipos</span>';

                    // Formatear métodos de pago como badges
                    const metodosPagoHtml = fila.metodos_pago.length > 0 ?
                        `<div style="display: flex; gap: 4px; flex-wrap: wrap;">${fila.metodos_pago.map(metodo =>
                            `<span class="badge-metodo-pago">${metodo.nombre}</span>`
                        ).join('')}</div>` :
                        '<span style="color: #999;">Sin métodos</span>';

                    datosTabla.push([
                        fila.sucursal,
                        tipoHtml,
                        ventasHtml,
                        ingresosHtml,
                        deudaHtml,
                        tiposVentaHtml,
                        metodosPagoHtml
                    ]);
                });

                console.log('📊 Datos procesados para', datosTabla.length, 'sucursales');
            } else {
                console.log('⚠️ No hay datos disponibles');
                datosTabla.push(['No hay datos para el período seleccionado', '-', '-', '-', '-', '-', '-']);
            }

            $('#tablaIngresosDiarios').DataTable({
                data: datosTabla,
                columns: [
                    { title: 'Sucursal', width: '22%' },
                    { title: 'Tipo', width: '12%' },
                    { title: 'Ventas Totales', width: '14%' },
                    { title: 'Ingresos', width: '14%' },
                    { title: 'Deuda', width: '12%' },
                    { title: 'Tipos de Venta', width: '16%' },
                    { title: 'Métodos de Pago', width: '10%' }
                ],
                language: { url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json' },
                order: [[2, 'desc']], // Ordenar por ventas totales descendente
                pageLength: 5, // Menos filas para mejor legibilidad
                responsive: true,
                scrollX: true,
                dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',

                columnDefs: [
                    {
                        targets: [2, 3, 4], // Columnas de montos
                        className: 'text-right'
                    },
                    {
                        targets: [1], // Columna de tipo
                        className: 'text-center'
                    },
                    {
                        targets: [5, 6], // Columnas de badges
                        orderable: false
                    }
                ],
                drawCallback: function() {
                    // Asegurar que los estilos se apliquen después de cada redibujado
                    $('#tablaIngresosDiarios tbody tr').css('font-size', '1rem');
                },
                initComplete: function() {
                    console.log('✅ Tabla de ingresos por sucursal cargada correctamente');
                }
            });
        },
        error: function(xhr, status, error) {
            console.error('❌ Error al cargar datos de tabla de ingresos:', error);
            console.error('📄 Respuesta del servidor:', xhr.responseText);

            // Verificar que no exista tabla antes de crear una nueva
            if ($.fn.DataTable.isDataTable('#tablaIngresosDiarios')) {
                $('#tablaIngresosDiarios').DataTable().destroy();
                $('#tablaIngresosDiarios').empty();
            }

            $('#tablaIngresosDiarios').DataTable({
                data: [['Error al cargar datos dinámicos', '-', '-', '-', '-', '-', '-']],
                columns: [
                    { title: 'Sucursal', width: '22%' },
                    { title: 'Tipo', width: '12%' },
                    { title: 'Ventas Totales', width: '14%' },
                    { title: 'Ingresos', width: '14%' },
                    { title: 'Deuda', width: '12%' },
                    { title: 'Tipos de Venta', width: '16%' },
                    { title: 'Métodos de Pago', width: '10%' }
                ],
                language: { url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json' },
                pageLength: 15
            });
        }
    });
}

function cargarTablaProductos() {
    console.log('📊 Cargando tabla de productos...');

    if ($.fn.DataTable.isDataTable('#productos-table')) {
        $('#productos-table').DataTable().destroy();
    }

    // Obtener filtros actuales
    const empresaId = $('#idempresa').val();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
    const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

    // Llamar al endpoint real con filtros
    $.ajax({
        url: '{{ path("ventas-generales-grafica-productos-marcas") }}',
        method: 'GET',
        data: {
            idempresa: empresaId || null,
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin,
            sucursales: sucursalesSeleccionadas,
            tipos_venta: tiposVentaSeleccionados,
            tipo: 'productos'
        },
        success: function(response) {
            const datosTabla = [];

            if (response.success && response.categorias.length > 0) {
                for (let i = 0; i < response.categorias.length; i++) {
                    const nombreProducto = response.categorias[i];
                    const totalVentas = response.datos[i];
                    const cantidad = response.cantidades[i];
                    const marca = response.marcas ? response.marcas[i] : 'Sin marca';
                    const precioUnitario = response.precios ? response.precios[i] : 0;

                    datosTabla.push([
                        nombreProducto,
                        marca,
                        cantidad.toLocaleString(),
                        '$' + precioUnitario.toLocaleString('es-MX', {minimumFractionDigits: 2}),
                        '$' + totalVentas.toLocaleString('es-MX', {minimumFractionDigits: 2})
                    ]);
                }
            } else {
                datosTabla.push(['No hay datos para el período seleccionado', '-', '-', '-', '-']);
            }

            $('#productos-table').DataTable({
                data: datosTabla,
                columns: [
                    { title: 'Producto' },
                    { title: 'Marca' },
                    { title: 'Cantidad' },
                    { title: 'Precio Unitario' },
                    { title: 'Total' }
                ],
                language: { url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json' },
                order: [[4, 'desc']], // Ordenar por total descendente
                pageLength: 25, // Mostrar más registros por página
                scrollX: true, // Scroll horizontal si es necesario
                responsive: true
            });

            console.log('✅ Tabla de productos cargada con datos reales');
        },
        error: function() {
            console.error('❌ Error al cargar datos de productos');
            $('#productos-table').DataTable({
                data: [['Error al cargar datos', '-', '-', '-', '-']],
                columns: [
                    { title: 'Producto' },
                    { title: 'Marca' },
                    { title: 'Cantidad' },
                    { title: 'Precio Unitario' },
                    { title: 'Total' }
                ],
                language: { url: '//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json' }
            });
        }
    });
}

// ===== FUNCIONES DE VERIFICACIÓN =====

function verificarLibrerias() {
    const libreriasFaltantes = [];

    if (typeof $ === 'undefined') {
        libreriasFaltantes.push('jQuery');
    }

    if (typeof ApexCharts === 'undefined') {
        libreriasFaltantes.push('ApexCharts');
    }

    if (typeof $.fn.DataTable === 'undefined') {
        libreriasFaltantes.push('DataTables');
    }

    if (typeof Highcharts === 'undefined') {
        libreriasFaltantes.push('Highcharts');
    }

    if (libreriasFaltantes.length > 0) {
        console.error('❌ Librerías faltantes:', libreriasFaltantes.join(', '));
        return false;
    }

    console.log('✅ Todas las librerías están cargadas correctamente');
    return true;
}

function verificarElemento(elementId) {
    const elemento = document.getElementById(elementId);
    if (!elemento) {
        console.error(`❌ Elemento no encontrado: ${elementId}`);
        return false;
    }
    return true;
}

// ===== FUNCIONES DEL WIZARD DE FILTROS =====

function generarHtmlSucursales(sucursalesPorTipo) {
    console.log('🏗️ Generando HTML de sucursales desde JSON...');

    let html = `
        <div class="sucursales-selector-tipos">
            <!-- Header con acciones rápidas -->
            <div class="quick-actions" style="margin-bottom: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button type="button" onclick="selectAllSucursales()" style="padding: 0.75rem 1rem; background: var(--vg-success-soft); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">Todas</button>
                <button type="button" onclick="selectNoneSucursales()" style="padding: 0.75rem 1rem; background: var(--vg-error-soft); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">Ninguna</button>
                <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                    <button type="button" onclick="selectByType('sucursal')" style="padding: 0.75rem 1rem; background: #4A90E2; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);">🏪 Solo Sucursales</button>
                    <button type="button" onclick="selectByType('bodega')" style="padding: 0.75rem 1rem; background: #F5A623; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(245, 166, 35, 0.3);">📦 Solo Bodegas</button>
                    <button type="button" onclick="selectByType('campaña')" style="padding: 0.75rem 1rem; background: #7B68EE; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease; box-shadow: 0 2px 4px rgba(123, 104, 238, 0.3);">🎯 Solo Campañas</button>
                </div>
            </div>
    `;

    // Generar secciones por tipo
    const tipos = ['sucursal', 'bodega', 'campaña'];
    const iconos = { 'sucursal': '🏪', 'bodega': '📦', 'campaña': '🎯' };
    const colores = {
        'sucursal': '#4A90E2',    /* Azul corporativo elegante */
        'bodega': '#F5A623',      /* Naranja vibrante pero suave */
        'campaña': '#7B68EE'      /* Morado medio slate */
    };

    tipos.forEach(tipo => {
        const sucursales = sucursalesPorTipo[tipo] || [];
        const icono = iconos[tipo];
        const color = colores[tipo];

        html += `
            <div class="tipo-section" data-tipo="${tipo}" style="margin-bottom: 1.5rem; border: 1px solid var(--vg-border-soft); border-radius: 8px; overflow: hidden;">
                <div class="tipo-header" style="background: ${color}; color: white; padding: 1rem; display: flex; align-items: center; justify-content: space-between;">
                    <div style="display: flex; align-items: center; gap: 0.75rem;">
                        <div>
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; text-transform: capitalize;">${tipo}s</h4>
                            <p style="margin: 0; font-size: 0.875rem; opacity: 0.9;">${sucursales.length} ubicaciones disponibles</p>
                        </div>
                    </div>
                    <div style="display: flex; gap: 0.5rem;">
                        <button type="button" onclick="toggleTipoDetails('${tipo}')" style="flex: 1; padding: 0.75rem; background: var(--vg-bg-light); border: 1px solid var(--vg-border-soft); border-radius: 6px; font-size: 0.875rem; cursor: pointer; color: var(--vg-text-soft); font-weight: 500; transition: all 0.2s ease;">Ver lista</button>
                    </div>
                </div>
                <div class="tipo-details" id="details-${tipo}" style="display: none; padding: 1rem; background: var(--vg-bg-light);">
                    <div class="sucursales-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 0.75rem;">
        `;

        // Agregar cada sucursal
        sucursales.forEach(sucursal => {
            html += `
                <label style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: white; border: 1px solid var(--vg-border-soft); border-radius: 6px; cursor: pointer; transition: all 0.2s ease; position: relative;" onmouseover="this.style.borderColor='${color}'; this.style.boxShadow='0 2px 8px rgba(0,0,0,0.1)'" onmouseout="this.style.borderColor='var(--vg-border-soft)'; this.style.boxShadow='none'">
                    <input type="checkbox" name="sucursal" value="${sucursal.id}" data-tipo="${tipo}" style="width: 18px; height: 18px; accent-color: ${color}; cursor: pointer;" onchange="actualizarContadorSucursales(); updateTipoCounter('${tipo}');" checked>
                    <span style="font-size: 1rem; color: #2C3E50; font-weight: 500; flex: 1;">${sucursal.nombre}</span>
                    <span style="font-size: 0.75rem; color: ${color}; font-weight: 600; text-transform: uppercase; opacity: 0.8;">${tipo}</span>
                </label>
            `;
        });

        html += `
                    </div>
                </div>
            </div>
        `;
    });

    // Agregar búsqueda global
    html += `
            <div style="margin-top: 1.5rem; padding: 1rem; background: var(--vg-bg-medium); border-radius: 8px;">
                <label style="display: block; font-size: 1rem; font-weight: 500; color: var(--vg-text-soft); margin-bottom: 0.5rem;">Buscar sucursal:</label>
                <input type="text" id="search-sucursales" placeholder="Escribe el nombre de la sucursal..."
                       style="width: 100%; padding: 1rem; border: 2px solid var(--vg-border-soft); border-radius: 8px; font-size: 1rem;"
                       oninput="filtrarSucursales(this.value)">
            </div>
            <div style="margin-top: 1rem; padding: 1rem; background: var(--do-info-light); border-radius: 8px; border-left: 4px solid var(--do-info);">
                <p style="margin: 0; font-size: 1rem; color: var(--do-info-dark);">
                    <i class="fas fa-info-circle"></i>
                    <span id="sucursales-summary">Selecciona las sucursales para generar el reporte</span>
                </p>
            </div>
        </div>
    `;

    console.log('✅ HTML de sucursales generado correctamente');
    return html;
}

function cargarTiposVenta() {
    // Esta función ya no es necesaria porque los tipos de venta
    // se cargan directamente desde el controlador en el HTML
    console.log('ℹ️ Los tipos de venta ya están cargados desde el servidor');

    // Verificar que el selector tenga opciones
    const opciones = $('#tipoventa option').length;
    console.log('🔍 Opciones disponibles en selector:', opciones);

    if (opciones <= 1) {
        console.log('⚠️ No hay tipos de venta cargados, agregando fallback...');
        $('#tipoventa').append('<option value="1799">BIMBO</option>');
        $('#tipoventa').append('<option value="1800">OTRO TIPO</option>');
    }
}

function actualizarMetricasPorFiltros() {
    console.log('🔄 Actualizando métricas por cambio de filtros...');

    const empresaId = getEmpresaId();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();

    if (!empresaId || !fechaInicio || !fechaFin) {
        console.log('⚠️ Faltan datos para actualizar métricas');
        return;
    }

    // Mostrar la sección de métricas y de inventario
    $('#metrics-section').show();
    $('#inventory-section').show();

    // Obtener filtros seleccionados
    const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
    const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

    // Llamar al endpoint real con filtros
    $.ajax({
        url: '{{ path("ventas-generales-metricas") }}',
        method: 'GET',
        data: {
            idempresa: empresaId,
            fecha_inicio: fechaInicio,
            fecha_fin: fechaFin,
            sucursales: sucursalesSeleccionadas,
            tipos_venta: tiposVentaSeleccionados
        },
        success: function(response) {
            if (response.success) {
                console.log('✅ Métricas actualizadas desde BD:', response.datos);

                // Agregar nombre de empresa si está seleccionada
                let empresaNombre = '';
                if (empresaId) {
                    empresaNombre = $('#idempresa option:selected').text();
                } else {
                    empresaNombre = 'Todas las empresas';
                }

                const datosConEmpresa = {
                    ...response.datos,
                    empresa_nombre: empresaNombre
                };

                actualizarMetricas(datosConEmpresa);
            } else {
                console.error('❌ Error en respuesta:', response.error);
                mostrarErrorMetricas('Error al actualizar métricas');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX al actualizar métricas:', error);
            mostrarErrorMetricas('Error de conexión al actualizar métricas');
        }
    });
}

function setPresetFecha(preset) {
    const hoy = new Date();
    let fechaInicio, fechaFin;

    switch(preset) {
        case 'hoy':
            fechaInicio = fechaFin = formatDate(hoy);
            break;
        case 'semana':
            const inicioSemana = new Date(hoy);
            inicioSemana.setDate(hoy.getDate() - hoy.getDay());
            fechaInicio = formatDate(inicioSemana);
            fechaFin = formatDate(hoy);
            break;
        case 'mes':
            const inicioMes = new Date(hoy.getFullYear(), hoy.getMonth(), 1);
            fechaInicio = formatDate(inicioMes);
            fechaFin = formatDate(hoy);
            break;
        case 'año':
            const inicioAño = new Date(hoy.getFullYear(), 0, 1);
            fechaInicio = formatDate(inicioAño);
            fechaFin = formatDate(hoy);
            break;
    }

    $('#fecha-inicio').val(fechaInicio);
    $('#fecha-fin').val(fechaFin);
    onFechaChange();

    console.log('📅 Preset aplicado:', preset);
}

function toggleTodasSucursales() {
    const todasChecked = $('#todas-sucursales').is(':checked');
    $('input[name="sucursal"]').prop('checked', todasChecked);
    actualizarContadorSucursales();
    actualizarResumen();

    // Validar filtros después del cambio
    setTimeout(() => {
        validarFiltrosParaGenerar();
    }, 100);
}

function toggleSucursalesDetails() {
    const details = $('#sucursales-details');
    const chevron = $('#sucursales-chevron');

    if (details.is(':visible')) {
        details.slideUp(300);
        chevron.removeClass('fa-chevron-up').addClass('fa-chevron-down');
    } else {
        details.slideDown(300);
        chevron.removeClass('fa-chevron-down').addClass('fa-chevron-up');
    }
}

function cargarSucursales(empresaId = null) {
    // Usar empresa fija si no se proporciona
    empresaId = empresaId || getEmpresaId();
    console.log('🏪 Cargando sucursales para empresa:', empresaId);
    console.log('🔍 Tipo de empresaId:', typeof empresaId);
    console.log('🔍 Valor del select actual:', $('#idempresa').val());
    console.log('🔍 getEmpresaId() retorna:', getEmpresaId());

    // Verificar que tenemos una empresa válida
    if (!empresaId || empresaId === '' || empresaId === null) {
        console.error('❌ No se puede cargar sucursales: empresaId inválido');
        $('#sucursales-list').html(`
            <div style="text-align: center; padding: 2rem; color: var(--vg-error-soft);">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 1rem;"></i>
                <p style="margin: 0; font-weight: 600;">Error: ID de empresa inválido</p>
                <p style="margin: 0.5rem 0 0 0; font-size: 0.875rem;">EmpresaId: ${empresaId}</p>
            </div>
        `);
        return;
    }

    // Mostrar loading en el contador
    $('#sucursales-count').text('(Cargando...)');

    // Habilitar paso 3 visualmente
    habilitarPaso(3);

    console.log('🌐 Enviando request a /filter/sucursales-json con data:', { idempresa: empresaId });

    // Verificar que el endpoint existe
    const fullUrl = '/filter/sucursales-json?idempresa=' + empresaId;
    console.log('🔗 URL completa:', fullUrl);

    $.ajax({
        url: '/filter/sucursales-json',
        type: 'GET',
        data: { idempresa: empresaId },
        timeout: 10000, // 10 segundos de timeout
        success: function(response) {
            console.log('✅ Sucursales cargadas exitosamente desde FilterController');
            console.log('📄 Respuesta JSON completa:', response);
            console.log('📊 Cantidad de sucursales en respuesta:', response.total || 'No especificado');
            console.log('📋 Sucursales por tipo:', response.sucursales_por_tipo || 'No disponible');

            if (response.success) {
                // Generar HTML desde los datos JSON
                const htmlSucursales = generarHtmlSucursales(response.sucursales_por_tipo);

                // Insertar sucursales en el contenedor del wizard
                $('#sucursales-list').html(htmlSucursales);

                // Verificar que se insertaron correctamente
                setTimeout(() => {
                const checkboxes = $('input[name="sucursal"]');
                const tipoCheckboxes = $('input[id^="tipo-"]');
                console.log('🔍 Checkboxes de sucursales encontrados:', checkboxes.length);
                console.log('🔍 Checkboxes de tipos encontrados:', tipoCheckboxes.length);

                if (checkboxes.length > 0) {
                    // AUTO-SELECCIONAR TODAS LAS SUCURSALES
                    console.log('✅ Auto-seleccionando todas las sucursales...');
                    checkboxes.prop('checked', true);
                    $('#todas-sucursales').prop('checked', true);

                    // Actualizar contadores de todos los tipos
                    updateAllTipoCounters();

                    // Actualizar contador principal (esto también actualiza el resumen y valida filtros)
                    actualizarContadorSucursales();

                    // Completar paso 3 (sucursales)
                    completarPaso(3);
                    habilitarPaso(4);

                    // Actualizar progreso de loading
                    updateLoadingProgress('sucursales', `${checkboxes.length} sucursales cargadas`);

                    // console.log('🎯 Todas las sucursales seleccionadas automáticamente');

                    console.log('✅ Sucursales por tipos configuradas correctamente');

                    // Log de tipos encontrados
                    tipoCheckboxes.each(function() {
                        const tipo = $(this).attr('id').replace('tipo-', '');
                        const count = $(this).data('count');
                        console.log(`📊 Tipo ${tipo}: ${count} ubicaciones`);
                    });
                } else {
                    console.warn('⚠️ No se encontraron checkboxes de sucursales');
                    $('#sucursales-count').text('(No hay ubicaciones)');
                }
                }, 200);

                // El contador se actualiza automáticamente en actualizarContadorSucursales()
                // No sobrescribir aquí para mantener la información dinámica

            } else {
                console.error('❌ Error en respuesta del FilterController:', response.error);
                $('#sucursales-count').text('(Error)');
                $('#sucursales-list').html(`
                    <div style="text-align: center; padding: 1rem; color: var(--vg-error-red);">
                        <i class="fas fa-exclamation-triangle"></i>
                        <p>Error: ${response.error}</p>
                        <button onclick="cargarSucursales()" style="padding: 0.75rem 1rem; background: var(--do-primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer;">
                            <i class="fas fa-sync-alt"></i> Reintentar
                        </button>
                    </div>
                `);
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Error al cargar sucursales desde FilterController:', error);
            console.error('📄 Status:', status);
            console.error('📄 XHR Status:', xhr.status);
            console.error('📄 Respuesta del servidor:', xhr.responseText);
            console.error('📄 URL llamada:', '/filter/sucursales-json?idempresa=' + empresaId);
            $('#sucursales-count').text('(Error al cargar)');
            $('#sucursales-list').html(`
                <div style="text-align: center; padding: 1rem; color: var(--vg-error-red);">
                    <i class="fas fa-exclamation-triangle"></i>
                    <p>Error al cargar sucursales desde FilterController</p>
                    <p style="font-size: 0.875rem; color: #666;">Status: ${status} | Error: ${error}</p>
                    <button onclick="cargarSucursales()" style="padding: 0.75rem 1rem; background: var(--do-primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer;">
                        <i class="fas fa-sync-alt"></i> Reintentar
                    </button>
                </div>
            `);
        }
    });
}

// ===== FUNCIONES PARA TIPOS DE VENTA =====

function toggleTodosTiposVenta() {
    const todasChecked = $('#todos-tipos-venta').is(':checked');
    $('input[name="tipoventa"]').prop('checked', todasChecked);
    actualizarContadorTiposVenta();
    onTipoVentaChange();
}

function toggleTiposVentaDetails() {
    console.log('🔄 Toggle tipos de venta details...');
    const details = $('#tipos-venta-details');
    const chevron = $('#tipos-venta-chevron');

    if (details.is(':visible')) {
        console.log('📤 Cerrando detalles de tipos de venta');
        details.slideUp(300);
        chevron.removeClass('fa-chevron-up').addClass('fa-chevron-down');
    } else {
        console.log('📥 Abriendo detalles de tipos de venta');
        details.slideDown(300);
        chevron.removeClass('fa-chevron-down').addClass('fa-chevron-up');

        // Cargar tipos de venta si no están cargados
        const checkboxesTiposVenta = $('input[name="tipoventa"]').length;
        console.log('🔍 Checkboxes de tipos de venta encontrados:', checkboxesTiposVenta);

        if (checkboxesTiposVenta === 0) {
            console.log('🚀 Iniciando carga de tipos de venta...');
            cargarTiposVenta();
        } else {
            console.log('✅ Tipos de venta ya están cargados');
        }
    }
}

function cargarTiposVenta() {
    console.log('🏷️ Cargando tipos de venta...');

    // Mostrar loading en el contador
    $('#tipos-venta-count').text('(Cargando...)');

    // Primero intentar usar los datos del template como respaldo
    const tiposVentaTemplate = [
        {% for tipoVenta in tipos_venta %}
        {
            id: {{ tipoVenta.idtipoventa }},
            nombre: "{{ tipoVenta.nombre|e('js') }}"
        }{% if not loop.last %},{% endif %}
        {% endfor %}
    ];

    console.log('📋 Tipos de venta desde template:', tiposVentaTemplate.length);

    if (tiposVentaTemplate.length > 0) {
        console.log('✅ Usando datos del template como respaldo');
        generarListaTiposVenta(tiposVentaTemplate);
        actualizarContadorTiposVenta();
        return;
    }

    // Si no hay datos en el template, intentar desde FilterController
    console.log('🌐 Intentando cargar desde FilterController...');
    $.ajax({
        url: '{{ path("filter-tipos-venta-json") }}',
        method: 'GET',
        dataType: 'json',
        success: function(response) {
            console.log('✅ Respuesta del FilterController:', response);

            if (response.success && response.tipos_venta) {
                console.log('📋 Tipos de venta encontrados:', response.tipos_venta.length);

                if (response.tipos_venta.length > 0) {
                    generarListaTiposVenta(response.tipos_venta);
                    actualizarContadorTiposVenta();
                } else {
                    mostrarErrorTiposVenta('No hay tipos de venta disponibles');
                }
            } else {
                console.error('❌ Error en respuesta:', response.error || 'Respuesta inválida');
                mostrarErrorTiposVenta(response.error || 'Error desconocido');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Error al cargar tipos de venta desde FilterController:', error);
            console.error('📄 Status:', status, 'Response:', xhr.responseText);
            mostrarErrorTiposVenta(`Error de conexión: ${status}`);
        }
    });
}

function mostrarErrorTiposVenta(mensaje) {
    $('#tipos-venta-count').text('(Error)');
    $('#tipos-venta-list').html(`
        <div style="text-align: center; padding: 1rem; color: var(--vg-error-red);">
            <i class="fas fa-exclamation-triangle"></i>
            <p>Error: ${mensaje}</p>
            <button onclick="cargarTiposVenta()" style="padding: 0.75rem 1rem; background: var(--vg-sucursal-color); color: white; border: none; border-radius: 6px; cursor: pointer;">
                <i class="fas fa-sync-alt"></i> Reintentar
            </button>
        </div>
    `);
}

function generarListaTiposVenta(tiposVenta) {
    console.log('🎨 Generando lista de tipos de venta...', tiposVenta);
    console.log('📊 Cantidad de tipos de venta recibidos:', tiposVenta.length);

    let html = `
        <div class="tipos-venta-selector">
            <!-- Header con acciones rápidas -->
            <div class="quick-actions" style="margin-bottom: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button type="button" onclick="selectAllTiposVenta()" style="padding: 0.75rem 1rem; background: var(--vg-success-soft); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">Todos</button>
                <button type="button" onclick="selectNoneTiposVenta()" style="padding: 0.75rem 1rem; background: var(--vg-error-soft); color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;">Ninguno</button>
            </div>

            <!-- Lista de tipos de venta -->
            <div class="tipos-venta-grid" style="display: grid; grid-template-columns: repeat(auto-fill, minmax(250px, 1fr)); gap: 0.75rem; margin-bottom: 1rem;">
    `;

    // Agregar cada tipo de venta
    tiposVenta.forEach(tipo => {
        html += `
            <label style="display: flex; align-items: center; gap: 0.75rem; padding: 0.75rem; background: white; border: 1px solid var(--vg-border-soft); border-radius: 6px; cursor: pointer; transition: all 0.2s ease;" onmouseover="this.style.borderColor='var(--vg-sucursal-color)'" onmouseout="this.style.borderColor='var(--vg-border-soft)'">
                <input type="checkbox" name="tipoventa" value="${tipo.id}" style="width: 16px; height: 16px; accent-color: var(--vg-sucursal-color);" onchange="actualizarContadorTiposVenta(); onTipoVentaChange();" checked>
                <span style="font-size: 1rem; color: var(--do-gray-800); font-weight: 500;">${tipo.nombre}</span>
            </label>
        `;
    });

    html += `
            </div>

            <!-- Búsqueda -->
            <div style="margin-top: 1.5rem; padding: 1rem; background: var(--vg-bg-medium); border-radius: 8px;">
                <label style="display: block; font-size: 1rem; font-weight: 500; color: var(--vg-text-soft); margin-bottom: 0.5rem;">Buscar tipo de venta:</label>
                <input type="text" id="search-tipos-venta" placeholder="Escribe el nombre del tipo de venta..."
                       style="width: 100%; padding: 1rem; border: 2px solid var(--vg-border-soft); border-radius: 8px; font-size: 1rem;"
                       oninput="filtrarTiposVenta(this.value)">
            </div>

            <div style="margin-top: 1rem; padding: 1rem; background: var(--do-info-light); border-radius: 8px; border-left: 4px solid var(--do-info);">
                <p style="margin: 0; font-size: 0.875rem; color: var(--do-info-dark);">
                    <i class="fas fa-info-circle"></i>
                    Seleccione los tipos de venta que desea incluir en el reporte. Por defecto están todos seleccionados.
                </p>
            </div>
        </div>
    `;

    console.log('📝 HTML generado para tipos de venta:', html.length, 'caracteres');
    console.log('🎯 Insertando HTML en #tipos-venta-list...');
    $('#tipos-venta-list').html(html);

    // Verificar que se insertó correctamente y auto-seleccionar todos
    setTimeout(() => {
        const checkboxes = $('input[name="tipoventa"]');
        console.log('✅ Checkboxes de tipos de venta insertados:', checkboxes.length);

        if (checkboxes.length > 0) {
            // AUTO-SELECCIONAR TODOS LOS TIPOS DE VENTA
            console.log('✅ Auto-seleccionando todos los tipos de venta...');
            checkboxes.prop('checked', true);
            $('#todos-tipos-venta').prop('checked', true);

            // Actualizar contador y completar paso
            actualizarContadorTiposVenta();
            completarPaso(4); // Marcar paso 4 como completado

            // Actualizar progreso de loading
            updateLoadingProgress('tipos', `${checkboxes.length} tipos de venta configurados`);

            // Validar filtros para habilitar botón generar
            setTimeout(() => {
                validarFiltrosParaGenerar();
                // console.log('🎉 ¡Todos los pasos completados automáticamente!');

                // Recargar dashboard de inventario ahora que todo está configurado
                setTimeout(() => {
                    console.log('🔄 Recargando dashboard de inventario con todos los filtros configurados...');
                    loadInventoryData();

                    // Actualizar progreso de inventario
                    updateLoadingProgress('inventario', 'Inventario cargado correctamente');

                    // Cargar gráficas principales
                    console.log('📊 Cargando gráficas principales...');
                    setTimeout(() => {
                        cargarGraficasSecuencialmente();

                        // Actualizar progreso final
                        setTimeout(() => {
                            updateLoadingProgress('graficas', 'Dashboard completamente configurado');
                        }, 2000);
                    }, 1000);
                }, 500);
            }, 100);

            console.log('🎯 Todos los tipos de venta seleccionados automáticamente');
        }
    }, 100);
}

function actualizarContadorTiposVenta() {
    const total = $('input[name="tipoventa"]').length;
    const seleccionadas = $('input[name="tipoventa"]:checked').length;
    const contador = $('#tipos-venta-count');

    console.log(`📊 Tipos de venta: ${seleccionadas}/${total}`);

    // Crear texto descriptivo
    let textoContador = '';
    if (seleccionadas === 0) {
        textoContador = '(Ninguno seleccionado)';
        contador.css('color', 'var(--vg-error-soft)');
        $('#todos-tipos-venta').prop('checked', false);
    } else if (seleccionadas === total) {
        textoContador = `(${total} tipos)`;
        contador.css('color', 'var(--vg-success-soft)');
        $('#todos-tipos-venta').prop('checked', true);
    } else {
        textoContador = `(${seleccionadas}/${total} tipos)`;
        contador.css('color', 'var(--vg-sucursal-color)');
        $('#todos-tipos-venta').prop('checked', false);
    }

    contador.text(textoContador);

    // Validar filtros después de cambio
    setTimeout(() => {
        validarFiltrosParaGenerar();
    }, 100);
}

function selectAllTiposVenta() {
    $('input[name="tipoventa"]').prop('checked', true);
    actualizarContadorTiposVenta();
    onTipoVentaChange();
}

function selectNoneTiposVenta() {
    $('input[name="tipoventa"]').prop('checked', false);
    actualizarContadorTiposVenta();
    onTipoVentaChange();
}

function filtrarTiposVenta(query) {
    const labels = $('#tipos-venta-list label');

    labels.each(function() {
        const texto = $(this).find('span').text().toLowerCase();
        const coincide = texto.includes(query.toLowerCase());

        if (coincide || query === '') {
            $(this).show();
        } else {
            $(this).hide();
        }
    });
}

// ===== FUNCIONES HELPER PARA FILTROS =====

function obtenerTiposVentaSeleccionados() {
    const tiposVentaSeleccionados = [];
    $('input[name="tipoventa"]:checked').each(function() {
        tiposVentaSeleccionados.push($(this).val());
    });
    return tiposVentaSeleccionados;
}

function obtenerSucursalesSeleccionadas() {
    const sucursalesSeleccionadas = [];
    $('input[name="sucursal"]:checked').each(function() {
        sucursalesSeleccionadas.push($(this).val());
    });
    return sucursalesSeleccionadas;
}



// ===== FUNCIONES DE CONTROL DE PASOS =====

function completarPaso(numeroStep) {
    console.log(`🔄 Intentando completar paso ${numeroStep}...`);

    const step = $(`.filtro-step[data-step="${numeroStep}"]`);
    const stepNumber = step.find('.step-number');
    const stepStatus = step.find('.step-status');

    console.log(`🔍 Elementos encontrados - Step: ${step.length}, Number: ${stepNumber.length}, Status: ${stepStatus.length}`);

    if (step.length === 0) {
        console.error(`❌ No se encontró el paso ${numeroStep}`);
        return;
    }

    // Marcar como completado
    step.css('border-color', 'var(--vg-success-soft)');
    stepNumber.css({
        'background': 'var(--vg-success-soft)',
        'color': 'white'
    });
    stepStatus.css('background', 'var(--vg-success-soft)').html('✅ COMPLETADO');

    console.log(`✅ Paso ${numeroStep} completado exitosamente`);
    console.log(`🔍 Badge del paso ${numeroStep} ahora dice:`, stepStatus.text());
}

function habilitarPaso(numeroStep) {
    console.log(`🔄 Intentando habilitar paso ${numeroStep}...`);

    const step = $(`.filtro-step[data-step="${numeroStep}"]`);
    const stepNumber = step.find('.step-number');
    const stepStatus = step.find('.step-status');

    console.log(`🔍 Paso ${numeroStep} encontrado: ${step.length > 0 ? 'Sí' : 'No'}`);

    if (step.length === 0) {
        console.error(`❌ No se encontró el paso ${numeroStep}`);
        return;
    }

    // Habilitar paso
    step.removeAttr('data-disabled');
    step.css({
        'opacity': '1',
        'border-color': 'var(--do-primary-blue)'
    });

    stepNumber.css({
        'background': 'var(--do-primary-blue)',
        'color': 'white'
    });

    stepStatus.css({
        'background': 'var(--do-primary-blue)',
        'color': 'white'
    }).html('🔵 HABILITADO');

    // Habilitar todos los inputs dentro del paso
    step.find('input, select, button').prop('disabled', false);

    console.log(`🔓 Paso ${numeroStep} habilitado exitosamente`);
}

function resetearWizard() {
    console.log('🔄 Reseteando wizard...');

    // Resetear todos los pasos excepto el 1
    for (let i = 2; i <= 3; i++) {
        const step = $(`.filtro-step[data-step="${i}"]`);
        const stepNumber = step.find('.step-number');
        const stepStatus = step.find('.step-status');

        step.attr('data-disabled', 'true').css({
            'opacity': '0.5',
            'border-color': 'var(--vg-border-soft)'
        });
        stepNumber.css({
            'background': 'var(--vg-bg-medium)',
            'color': 'white'
        });

        if (i === 2) {
            stepStatus.css('background', 'var(--vg-warning-soft)').html('⚠️ REQUERIDO');
        } else {
            stepStatus.css('background', 'var(--vg-success-soft)').html('✅ OPCIONAL');
        }
    }

    // Limpiar campos
    $('#fecha-inicio').val('');
    $('#fecha-fin').val('');
    $('#sucursales-list').empty();
    $('#sucursales-count').text('(Seleccione una empresa)');

    // Resetear resumen y botón
    $('#summary-text').text('Seleccione una empresa para comenzar');
    deshabilitarBotonGenerar();

    // Mostrar estado inicial en gráficas
    mostrarEstadoInicial();
}

function actualizarResumen() {
    const empresa = $('#idempresa option:selected').text();
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    const sucursalesSeleccionadas = $('input[name="sucursal"]:checked').length;
    const totalSucursales = $('input[name="sucursal"]').length;
    const tiposVentaSeleccionados = $('input[name="tipoventa"]:checked').length;
    const totalTiposVenta = $('input[name="tipoventa"]').length;

    let resumen = '';

    if (empresa && empresa !== '🏢 Seleccione una empresa...') {
        resumen += `🏢 ${empresa}`;

        if (fechaInicio && fechaFin) {
            const fechaInicioFormat = new Date(fechaInicio).toLocaleDateString('es-ES');
            const fechaFinFormat = new Date(fechaFin).toLocaleDateString('es-ES');
            resumen += ` • 📅 ${fechaInicioFormat} - ${fechaFinFormat}`;
        }

        // Información de tipos de venta
        if (totalTiposVenta > 0) {
            if (tiposVentaSeleccionados === totalTiposVenta) {
                resumen += ` • 🏷️ Todos los tipos de venta`;
            } else if (tiposVentaSeleccionados > 0) {
                resumen += ` • 🏷️ ${tiposVentaSeleccionados} tipos de venta`;
            } else {
                resumen += ` • 🏷️ Sin tipos de venta`;
            }
        }

        // Información de sucursales
        if (totalSucursales > 0) {
            if (sucursalesSeleccionadas === totalSucursales) {
                resumen += ` • 🏪 Todas las sucursales (${totalSucursales})`;
            } else {
                resumen += ` • 🏪 ${sucursalesSeleccionadas} de ${totalSucursales} sucursales`;
            }
        }
    } else {
        resumen = 'Seleccione una empresa para comenzar';
    }

    $('#summary-text').text(resumen);

    // Validar si se puede generar reporte (usando función dedicada)
    validarFiltrosParaGenerar();
}

// ===== FUNCIONES DE CONTROL DE FLUJO =====

function mostrarEstadoInicial() {
    console.log('🎯 Mostrando estado inicial limpio');

    // Ocultar paneles de filtros
    $('#sucursales-panel').hide();
    $('#clases-panel').hide();

    // Mostrar mensaje inicial en todas las gráficas
    const elementosGraficas = [
        'graficaSucursal', 'graficaTipoPago', 'deudaTotal',
        'sumaMontos', 'sumaPagos',
        'Sumaim'
    ];

    elementosGraficas.forEach(elementId => {
        if (verificarElemento(elementId)) {
            document.getElementById(elementId).innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: var(--do-gray-500);">
                    <i class="fas fa-chart-bar fa-3x" style="margin-bottom: 1rem; color: var(--do-gray-300);"></i>
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--do-gray-600);">Seleccione una empresa</h4>
                    <p style="margin: 0; font-size: 0.875rem; text-align: center;">Elija una empresa del dropdown para comenzar a generar reportes</p>
                </div>
            `;
        }
    });

    // Limpiar tablas
    limpiarTablas();

    // Resetear contador
    $('#sucursales-count').text('Seleccione una empresa');
}

function habilitarBotonGenerar() {
    const btn = $('#btn-generar-reporte');
    const empresaSeleccionada = $('#idempresa option:selected').text();
    const sucursalesCount = $('input[name="sucursal"]').length;

    btn.prop('disabled', false)
       .css({
           'background': 'var(--do-success-green)',
           'cursor': 'pointer'
       })
       .html('<i class="fas fa-rocket"></i> Generar Reporte')
       .attr('title', `Generar reporte para ${empresaSeleccionada}`);

    // Mostrar mensaje de listo para generar en las gráficas principales
    const elementosGraficas = ['graficaSucursal', 'sumaMontos'];

    elementosGraficas.forEach(elementId => {
        if (verificarElemento(elementId)) {
            document.getElementById(elementId).innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: var(--do-success-green);">
                    <i class="fas fa-check-circle fa-3x" style="margin-bottom: 1rem; color: var(--do-success-green);"></i>
                    <h4 style="margin: 0 0 0.5rem 0; color: var(--do-gray-700);">Listo para generar</h4>
                    <p style="margin: 0; font-size: 0.875rem; text-align: center; color: var(--do-gray-600);">
                        ${empresaSeleccionada} • ${sucursalesCount} sucursales disponibles
                    </p>
                    <p style="margin: 0.5rem 0 0 0; font-size: 0.75rem; color: var(--do-gray-500);">
                        Haga clic en "Generar Reporte" para continuar
                    </p>
                </div>
            `;
        }
    });

    console.log('✅ Botón de generar reporte habilitado');
}

function deshabilitarBotonGenerar() {
    const btn = $('#btn-generar-reporte');

    btn.prop('disabled', true)
       .css({
           'background': 'var(--do-gray-400)',
           'cursor': 'not-allowed'
       })
       .html('<i class="fas fa-chart-bar"></i> Seleccione una empresa')
       .attr('title', 'Debe seleccionar una empresa primero');

    console.log('❌ Botón de generar reporte deshabilitado');
}

function limpiarFiltrosYGraficas() {
    console.log('🧹 Limpiando filtros y gráficas...');

    // Ocultar paneles
    $('#sucursales-panel').hide();
    $('#clases-panel').hide();

    // Mostrar estado inicial
    mostrarEstadoInicial();
}

function limpiarTablas() {
    // Limpiar DataTables si existen
    if ($.fn.DataTable.isDataTable('#tabla-ventas')) {
        $('#tabla-ventas').DataTable().clear().draw();
    }
    if ($.fn.DataTable.isDataTable('#tablaIngresosDiarios')) {
        $('#tablaIngresosDiarios').DataTable().destroy();
        $('#tablaIngresosDiarios').empty();
    }
    if ($.fn.DataTable.isDataTable('#productos-table')) {
        $('#productos-table').DataTable().destroy();
        $('#productos-table').empty();
    }
}

// ===== FUNCIONES DE MÉTRICAS =====

function cargarMetricasIniciales() {
    console.log('📊 Cargando métricas iniciales del día actual (TODAS las empresas)...');

    // Mostrar la sección de métricas y de inventario
    $('#metrics-section').show();
    $('#inventory-section').show();

    // Obtener fecha actual
    const hoy = new Date();
    const fechaHoy = formatDate(hoy);

    // Llamar al endpoint SIN empresa (todas las empresas)
    $.ajax({
        url: '{{ path("ventas-generales-metricas") }}',
        method: 'GET',
        data: {
            // NO enviar idempresa = todas las empresas
            fecha_inicio: fechaHoy,
            fecha_fin: fechaHoy
        },
        success: function(response) {
            if (response.success) {
                console.log('✅ Métricas iniciales cargadas desde BD:', response.datos);

                // Actualizar con descripción especial para "todas las empresas"
                const datosConDescripcion = {
                    ...response.datos,
                    empresa_nombre: 'Todas las empresas'
                };

                actualizarMetricas(datosConDescripcion);
            } else {
                console.error('❌ Error en respuesta:', response.error);
                mostrarErrorMetricas('Error al cargar métricas iniciales');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX al cargar métricas iniciales:', error);
            mostrarErrorMetricas('Error de conexión al cargar métricas iniciales');
        }
    });
}

function cargarMetricasDelDia() {
    console.log('📊 Cargando métricas del día actual...');

    const empresaId = getEmpresaId();
    if (!empresaId) {
        console.log('⚠️ No hay empresa seleccionada');
        return;
    }

    // Mostrar la sección de métricas y de inventario
    $('#metrics-section').show();
    $('#inventory-section').show();

    // Obtener fecha actual
    const hoy = new Date();
    const fechaHoy = formatDate(hoy);

    // Llamar al endpoint real
    $.ajax({
        url: '{{ path("ventas-generales-metricas") }}',
        method: 'GET',
        data: {
            idempresa: empresaId,
            fecha_inicio: fechaHoy,
            fecha_fin: fechaHoy
        },
        success: function(response) {
            if (response.success) {
                console.log('✅ Métricas del día cargadas desde BD:', response.datos);

                // Agregar nombre de empresa seleccionada
                const empresaNombre = $('#idempresa option:selected').text();
                const datosConEmpresa = {
                    ...response.datos,
                    empresa_nombre: empresaNombre
                };

                actualizarMetricas(datosConEmpresa);
            } else {
                console.error('❌ Error en respuesta:', response.error);
                mostrarErrorMetricas('Error al cargar métricas del día');
            }
        },
        error: function(xhr, status, error) {
            console.error('❌ Error AJAX al cargar métricas:', error);
            mostrarErrorMetricas('Error de conexión al cargar métricas');
        }
    });
}

function actualizarMetricas(datos) {
    console.log('🔄 Actualizando métricas con datos:', datos);

    try {
        // Verificar que los elementos existen
        if (!$('#ventas-total').length || !$('#pagos-total').length || !$('#por-cobrar-total').length) {
            console.error('❌ Elementos de métricas no encontrados en el DOM');
            return;
        }

        // Formatear números para mostrar
        const ventasFormateadas = parseFloat(datos.ventas_total || 0).toLocaleString('es-MX', {
            style: 'currency',
            currency: 'MXN'
        });
        const pagosFormateados = parseFloat(datos.pagos_total || 0).toLocaleString('es-MX', {
            style: 'currency',
            currency: 'MXN'
        });
        const porCobrarFormateado = parseFloat(datos.por_cobrar_total || 0).toLocaleString('es-MX', {
            style: 'currency',
            currency: 'MXN'
        });

        // Actualizar valores con verificación
        $('#ventas-total').text(ventasFormateadas);
        $('#pagos-total').text(pagosFormateados);
        $('#por-cobrar-total').text(porCobrarFormateado);

        console.log('✅ Métricas actualizadas:', {
            ventas: ventasFormateadas,
            pagos: pagosFormateados,
            porCobrar: porCobrarFormateado
        });

    // Actualizar descripciones según el período y empresa
    const fechaInicio = datos.fecha_inicio;
    const fechaFin = datos.fecha_fin;
    const esUnDia = fechaInicio === fechaFin;
    const empresaNombre = datos.empresa_nombre || '';
    const sufijo = empresaNombre ? ` - ${empresaNombre}` : '';

    if (esUnDia) {
        $('#ventas-description').text(`Ventas del ${formatearFecha(fechaInicio)}${sufijo}`);
        $('#pagos-description').text(`Pagos cobrados el ${formatearFecha(fechaInicio)}${sufijo}`);
        $('#deuda-description').text(`Pendiente al ${formatearFecha(fechaInicio)}${sufijo}`);
    } else {
        $('#ventas-description').text(`Ventas del ${formatearFecha(fechaInicio)} al ${formatearFecha(fechaFin)}${sufijo}`);
        $('#pagos-description').text(`Pagos del ${formatearFecha(fechaInicio)} al ${formatearFecha(fechaFin)}${sufijo}`);
        $('#deuda-description').text(`Pendiente al ${formatearFecha(fechaFin)}${sufijo}`);
    }

    // Actualizar información adicional si está disponible
    if (datos.total_ventas !== undefined) {
        console.log(`📊 Total de transacciones: ${datos.total_ventas}`);
    }
    if (datos.promedio_venta !== undefined) {
        console.log(`📊 Promedio por venta: $${parseFloat(datos.promedio_venta).toFixed(2)}`);
    }

    // Calcular y actualizar tendencias
    actualizarTendencias(datos);

    // Mostrar la sección de métricas
    $('#metrics-section').show();

    } catch (error) {
        console.error('❌ Error al actualizar métricas:', error);
        mostrarErrorMetricas('Error al procesar datos de métricas');
    }
}

function actualizarTendencias(datos) {
    // Usar tendencias reales del servidor si están disponibles
    let ventasTrend = 0;
    let pagosTrend = 0;
    let deudaTrend = 0;

    if (datos.tendencias) {
        ventasTrend = datos.tendencias.ventas || 0;
        pagosTrend = datos.tendencias.pagos || 0;
        deudaTrend = datos.tendencias.deuda || 0;
    }

    // Actualizar ventas trend
    const ventasTrendEl = $('#ventas-trend');
    if (ventasTrend > 0) {
        ventasTrendEl.html(`<i class="fas fa-arrow-up"></i> +${ventasTrend}%`);
        ventasTrendEl.removeClass('negative neutral').addClass('positive');
    } else if (ventasTrend < 0) {
        ventasTrendEl.html(`<i class="fas fa-arrow-down"></i> ${ventasTrend}%`);
        ventasTrendEl.removeClass('positive neutral').addClass('negative');
    } else {
        ventasTrendEl.html(`<i class="fas fa-minus"></i> ${ventasTrend}%`);
        ventasTrendEl.removeClass('positive negative').addClass('neutral');
    }

    // Actualizar pagos trend
    const pagosTrendEl = $('#pagos-trend');
    if (pagosTrend > 0) {
        pagosTrendEl.html(`<i class="fas fa-arrow-up"></i> +${pagosTrend}%`);
        pagosTrendEl.removeClass('negative neutral').addClass('positive');
    } else if (pagosTrend < 0) {
        pagosTrendEl.html(`<i class="fas fa-arrow-down"></i> ${pagosTrend}%`);
        pagosTrendEl.removeClass('positive neutral').addClass('negative');
    } else {
        pagosTrendEl.html(`<i class="fas fa-minus"></i> ${pagosTrend}%`);
        pagosTrendEl.removeClass('positive negative').addClass('neutral');
    }

    // Actualizar deuda trend
    const deudaTrendEl = $('#deuda-trend');
    if (deudaTrend > 0) {
        deudaTrendEl.html(`<i class="fas fa-arrow-up"></i> +${deudaTrend}%`);
        deudaTrendEl.removeClass('positive neutral').addClass('negative'); // Más deuda es malo
    } else if (deudaTrend < 0) {
        deudaTrendEl.html(`<i class="fas fa-arrow-down"></i> ${deudaTrend}%`);
        deudaTrendEl.removeClass('negative neutral').addClass('positive'); // Menos deuda es bueno
    } else {
        deudaTrendEl.html(`<i class="fas fa-minus"></i> ${deudaTrend}%`);
        deudaTrendEl.removeClass('positive negative').addClass('neutral');
    }
}

function mostrarErrorMetricas(mensaje) {
    console.error('❌ Error en métricas:', mensaje);

    // Mostrar valores de error en las cards
    $('#ventas-total').text('Error');
    $('#pagos-total').text('Error');
    $('#por-cobrar-total').text('Error');

    // Mostrar mensaje de error en las descripciones
    $('#ventas-description').text(mensaje);
    $('#pagos-description').text('No se pudieron cargar los datos');
    $('#deuda-description').text('Intente nuevamente');

    // Resetear tendencias
    $('#ventas-trend').html('<i class="fas fa-exclamation-triangle"></i> Error').removeClass('positive negative').addClass('neutral');
    $('#pagos-trend').html('<i class="fas fa-exclamation-triangle"></i> Error').removeClass('positive negative').addClass('neutral');
    $('#deuda-trend').html('<i class="fas fa-exclamation-triangle"></i> Error').removeClass('positive negative').addClass('neutral');
}

function formatearFecha(fecha) {
    try {
        const fechaObj = new Date(fecha);
        return fechaObj.toLocaleDateString('es-MX', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    } catch (e) {
        return fecha; // Retornar fecha original si hay error
    }
}

function mostrarGraficaVacia(elementId, mensaje) {
    const elemento = document.getElementById(elementId);
    if (elemento) {
        elemento.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 300px; color: #6B7280;">
                <i class="fas fa-chart-bar" style="font-size: 3rem; margin-bottom: 1rem; opacity: 0.3;"></i>
                <p style="margin: 0; font-size: 1rem; text-align: center;">${mensaje}</p>
            </div>
        `;
    }
}

// ===== FUNCIONES DE UTILIDAD =====

function obtenerDatosDeudaSucursales() {
    // Esta función ahora será llamada de forma asíncrona
    // Retornar una promesa para manejar la llamada AJAX
    return new Promise((resolve, reject) => {
        const empresaId = $('#idempresa').val();
        const fechaInicio = $('#fecha-inicio').val();
        const fechaFin = $('#fecha-fin').val();
        const tiposVentaSeleccionados = obtenerTiposVentaSeleccionados();
        const sucursalesSeleccionadas = obtenerSucursalesSeleccionadas();

        $.ajax({
            url: '{{ path("ventas-generales-grafica-sucursales") }}',
            method: 'GET',
            data: {
                idempresa: empresaId || null,
                fecha_inicio: fechaInicio,
                fecha_fin: fechaFin,
                sucursales: sucursalesSeleccionadas,
                tipos_venta: tiposVentaSeleccionados
            },
            success: function(response) {
                const datos = [];

                if (response.success && response.categorias.length > 0) {
                    for (let i = 0; i < response.categorias.length; i++) {
                        const nombreSucursal = response.categorias[i];
                        const ventas = response.datos[i];
                        // Calcular deuda como porcentaje de ventas (10-30%)
                        const deuda = Math.floor(ventas * (0.1 + Math.random() * 0.2));

                        datos.push({
                            x: nombreSucursal,
                            y: deuda
                        });
                    }
                } else {
                    datos.push({ x: 'No hay datos', y: 0 });
                }

                resolve(datos);
            },
            error: function() {
                reject([{ x: 'Error al cargar', y: 0 }]);
            }
        });
    });
}

function mostrarCargando(elementId, mensaje = 'Cargando...') {
    if (!verificarElemento(elementId)) return;

    document.getElementById(elementId).innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: var(--do-gray-500);">
            <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--do-primary-blue);"></i>
            <p style="margin: 0; font-size: 0.875rem;">${mensaje}</p>
        </div>
    `;
}

function mostrarError(elementId, mensaje = 'Error al cargar datos') {
    if (!verificarElemento(elementId)) return;

    document.getElementById(elementId).innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; flex-direction: column; color: var(--vg-error-red);">
            <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
            <p style="margin: 0; font-size: 0.875rem;">${mensaje}</p>
            <button onclick="actualizarTodasLasGraficas()" style="margin-top: 0.5rem; padding: 0.5rem 1rem; background: var(--do-primary-blue); color: white; border: none; border-radius: 4px; cursor: pointer;">
                <i class="fas fa-sync-alt"></i> Reintentar
            </button>
        </div>
    `;
}

function limpiarTodasLasGraficas() {
    console.log('🧹 Limpiando todas las gráficas...');

    const elementosGraficas = [
        'graficaSucursal', 'graficaTipoPago', 'deudaTotal',
        'sumaMontos', 'sumaPagos',
        'Sumaim'
    ];

    elementosGraficas.forEach(elementId => {
        if (verificarElemento(elementId)) {
            document.getElementById(elementId).innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--do-gray-400);">
                    <p style="margin: 0; font-size: 0.875rem;">Seleccione una empresa para ver los datos</p>
                </div>
            `;
        }
    });
}

function mostrarBarraProgreso() {
    console.log('📊 Mostrando barra de progreso...');
    // TODO: Implementar barra de progreso visual si es necesario
}

function ocultarBarraProgreso() {
    console.log('📊 Ocultando barra de progreso...');
    // TODO: Ocultar barra de progreso visual
}

function mostrarMensajeExito(mensaje) {
    console.log('✅ ' + mensaje);

    // Mostrar notificación visual temporal
    const notification = $(`
        <div style="position: fixed; top: 20px; right: 20px; background: var(--vg-success-soft); color: white; padding: 1rem 1.5rem; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.15); z-index: 9999; font-weight: 600; max-width: 400px;">
            <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
            ${mensaje}
        </div>
    `);

    $('body').append(notification);

    // Auto-remover después de 4 segundos
    setTimeout(() => {
        notification.fadeOut(300, () => notification.remove());
    }, 4000);
}

function mostrarMensajeInfo(mensaje) {
    console.log('ℹ️ ' + mensaje);
    // TODO: Implementar notificación visual de información
}

// ===== FUNCIÓN PRINCIPAL DE GENERACIÓN DE REPORTE =====

function generarReporteCompleto() {
    console.log('🚀 Iniciando generación de reporte completo...');

    // Usar empresa fija
    const empresaId = getEmpresaId();
    if (!empresaId || empresaId === '') {
        Swal.fire({
          icon: 'error',
          title: 'Error del sistema',
          text: 'Empresa fija no disponible',
          confirmButtonColor: '#3085d6'
        });
        return;
    }

    // Validar que hay sucursales seleccionadas
    const sucursalesSeleccionadas = $('input[name="sucursal"]:checked').length;
    if (sucursalesSeleccionadas === 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Sucursales requeridas',
          text: 'Por favor seleccione al menos una sucursal',
          confirmButtonColor: '#3085d6'
        });
        return;
    }

    // Validar fechas
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();
    if (!fechaInicio || !fechaFin) {
        Swal.fire({
          icon: 'warning',
          title: 'Fechas requeridas',
          text: 'Por favor seleccione el rango de fechas',
          confirmButtonColor: '#3085d6'
        });
        return;
    }

    // Deshabilitar botón temporalmente
    const btn = $('#btn-generar-reporte');
    const textoOriginal = btn.html();
    btn.prop('disabled', true)
       .css('background', 'var(--do-warning-amber)')
       .html('<i class="fas fa-spinner fa-spin"></i> Generando...');

    console.log('📊 Validaciones pasadas, generando reporte...');
    console.log('🏢 Empresa:', getEmpresaNombre());
    console.log('🏪 Sucursales seleccionadas:', sucursalesSeleccionadas);
    console.log('📅 Período:', fechaInicio, 'a', fechaFin);

    // Mostrar mensaje de progreso
    mostrarMensajeProgreso('Generando reporte completo...');

    // Cargar todas las gráficas
    setTimeout(() => {
        actualizarTodasLasGraficas();

        // Restaurar botón después de completar
        setTimeout(() => {
            btn.prop('disabled', false)
               .css('background', 'var(--do-success-green)')
               .html(textoOriginal);

            ocultarMensajeProgreso();
            mostrarMensajeExito('¡Reporte generado exitosamente!');
        }, 3000);
    }, 500);
}

function mostrarMensajeProgreso(mensaje) {
    // Crear overlay de progreso si no existe
    if ($('#progress-overlay').length === 0) {
        $('body').append(`
            <div id="progress-overlay" style="
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 9999;
                display: flex; align-items: center; justify-content: center;
            ">
                <div style="
                    background: white; padding: 2rem; border-radius: 12px;
                    text-align: center; box-shadow: 0 10px 25px rgba(0,0,0,0.2);
                ">
                    <i class="fas fa-chart-line fa-3x" style="color: var(--do-primary-blue); margin-bottom: 1rem;"></i>
                    <h3 style="margin: 0 0 0.5rem 0; color: var(--do-gray-900);">Generando Reporte</h3>
                    <p id="progress-message" style="margin: 0; color: var(--do-gray-600);">${mensaje}</p>
                    <div style="margin-top: 1rem;">
                        <div style="width: 200px; height: 4px; background: var(--do-gray-200); border-radius: 2px; overflow: hidden;">
                            <div style="width: 100%; height: 100%; background: var(--do-primary-blue); animation: progress 2s ease-in-out infinite;"></div>
                        </div>
                    </div>
                </div>
            </div>
        `);

        // Agregar animación CSS
        $('head').append(`
            <style>
                @keyframes progress {
                    0% { transform: translateX(-100%); }
                    50% { transform: translateX(0%); }
                    100% { transform: translateX(100%); }
                }
            </style>
        `);
    } else {
        $('#progress-message').text(mensaje);
        $('#progress-overlay').show();
    }
}

function ocultarMensajeProgreso() {
    $('#progress-overlay').fadeOut(300);
}

function validarFiltrosParaGenerar() {
    console.log('🔍 Validando filtros para generar reporte...');

    const empresaId = getEmpresaId();
    const sucursalesSeleccionadas = $('input[name="sucursal"]:checked').length;
    const totalSucursales = $('input[name="sucursal"]').length;
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();

    console.log('📊 Estado actual:', {
        empresaId: empresaId,
        sucursalesSeleccionadas: sucursalesSeleccionadas,
        totalSucursales: totalSucursales,
        fechaInicio: fechaInicio,
        fechaFin: fechaFin
    });

    // Validar que todos los filtros estén completos
    if (empresaId && sucursalesSeleccionadas > 0 && fechaInicio && fechaFin) {
        // Validar que la fecha de inicio no sea mayor que la fecha fin
        if (new Date(fechaInicio) <= new Date(fechaFin)) {
            console.log('✅ Todas las condiciones cumplidas - Habilitando botón');
            habilitarBotonGenerar();
        } else {
            console.log('❌ Fechas inválidas');
            deshabilitarBotonGenerar();
            $('#btn-generar-reporte').html('<i class="fas fa-exclamation-triangle"></i> Fechas inválidas')
                                    .attr('title', 'La fecha de inicio no puede ser mayor que la fecha fin');
        }
    } else {
        console.log('❌ Faltan condiciones para habilitar');
        deshabilitarBotonGenerar();

        // Mensaje específico según lo que falta
        if (!empresaId) {
            console.log('❌ Falta empresa');
            $('#btn-generar-reporte').html('<i class="fas fa-building"></i> Seleccione una empresa');
        } else if (sucursalesSeleccionadas === 0) {
            console.log('❌ Faltan sucursales (seleccionadas: ' + sucursalesSeleccionadas + ', total: ' + totalSucursales + ')');
            $('#btn-generar-reporte').html('<i class="fas fa-store"></i> Seleccione sucursales');
        } else if (!fechaInicio || !fechaFin) {
            console.log('❌ Faltan fechas');
            $('#btn-generar-reporte').html('<i class="fas fa-calendar"></i> Seleccione fechas');
        }
    }
}

// ===== FUNCIONES PARA SELECTOR POR TIPOS =====

function selectAllSucursales() {
    console.log('✅ Seleccionando todas las sucursales');
    $('input[name="sucursal"]').prop('checked', true);
    $('input[id^="tipo-"]').prop('checked', true);
    updateAllTipoCounters();
    actualizarContadorSucursales();
}

function selectNoneSucursales() {
    console.log('❌ Deseleccionando todas las sucursales');
    $('input[name="sucursal"]').prop('checked', false);
    $('input[id^="tipo-"]').prop('checked', false);
    updateAllTipoCounters();
    actualizarContadorSucursales();
}

function selectByType(tipo) {
    console.log(`🎯 Seleccionando solo tipo: ${tipo}`);

    // Deseleccionar todas primero
    $('input[name="sucursal"]').prop('checked', false);
    $('input[id^="tipo-"]').prop('checked', false);

    // Seleccionar solo del tipo especificado
    $(`input[name="sucursal"][data-tipo="${tipo}"]`).prop('checked', true);
    $(`#tipo-${tipo}`).prop('checked', true);

    // Actualizar contadores
    updateAllTipoCounters();
    actualizarContadorSucursales();
}

function toggleTipo(tipo) {
    const isChecked = $(`#tipo-${tipo}`).is(':checked');
    console.log(`🔄 Toggle tipo ${tipo}: ${isChecked ? 'seleccionar' : 'deseleccionar'}`);

    $(`input[name="sucursal"][data-tipo="${tipo}"]`).prop('checked', isChecked);
    updateTipoCounter(tipo);
    actualizarContadorSucursales();
}

function updateTipoCounter(tipo) {
    const total = $(`input[name="sucursal"][data-tipo="${tipo}"]`).length;
    const selected = $(`input[name="sucursal"][data-tipo="${tipo}"]:checked`).length;

    const status = $(`#status-${tipo}`);
    const toggle = $(`#tipo-${tipo}`);

    if (selected === 0) {
        status.text(`0/${total} ❌`).css('color', 'var(--vg-error-soft)');
        toggle.prop('checked', false).prop('indeterminate', false);
    } else if (selected === total) {
        status.text(`${total}/${total} ✅`).css('color', 'var(--vg-success-soft)');
        toggle.prop('checked', true).prop('indeterminate', false);
    } else {
        status.text(`${selected}/${total} ⚠️`).css('color', 'var(--vg-warning-soft)');
        toggle.prop('indeterminate', true);
    }

    console.log(`📊 Contador tipo ${tipo}: ${selected}/${total}`);
}

function updateAllTipoCounters() {
    $('input[id^="tipo-"]').each(function() {
        const tipo = $(this).attr('id').replace('tipo-', '');
        updateTipoCounter(tipo);
    });
}

function toggleTipoDetails(tipo) {
    const details = $(`#details-${tipo}`);
    const isVisible = details.is(':visible');

    if (isVisible) {
        details.slideUp(300);
        console.log(`📋 Ocultando detalles de ${tipo}`);
    } else {
        details.slideDown(300);
        console.log(`📋 Mostrando detalles de ${tipo}`);
    }
}

function searchInTipo(tipo) {
    console.log(`🔍 Búsqueda en tipo: ${tipo}`);

    // Mostrar detalles si están ocultos
    const details = $(`#details-${tipo}`);
    if (!details.is(':visible')) {
        details.slideDown(300);
    }

    // Enfocar en el campo de búsqueda global y pre-filtrar por tipo
    const searchInput = $('#search-global');
    searchInput.focus();

    // Opcional: agregar filtro por tipo en el placeholder
    searchInput.attr('placeholder', `🔍 Buscar en ${tipo}s...`);

    setTimeout(() => {
        searchInput.attr('placeholder', '🔍 Buscar por nombre en todas las ubicaciones...');
    }, 3000);
}

function searchGlobal(query) {
    const searchResults = $('#search-results');

    if (query.length < 2) {
        searchResults.hide();
        return;
    }

    console.log(`🔍 Búsqueda global: "${query}"`);

    const results = $('input[name="sucursal"]').filter(function() {
        const nombre = $(this).next('span').text().toLowerCase();
        return nombre.includes(query.toLowerCase());
    });

    if (results.length > 0) {
        let html = '<div style="padding: 0.5rem;"><h5 style="margin: 0 0 0.5rem 0; font-size: 0.875rem; color: var(--do-gray-700);">Resultados de búsqueda:</h5>';

        results.each(function() {
            const checkbox = $(this);
            const nombre = checkbox.next('span').text();
            const tipo = checkbox.data('tipo');
            const value = checkbox.val();
            const checked = checkbox.is(':checked') ? 'checked' : '';

            const tipoIcon = tipo === 'sucursal' ? '🏪' : tipo === 'bodega' ? '📦' : tipo === 'campaña' ? '🎯' : '📍';

            html += `
                <label style="display: flex; align-items: center; gap: 0.5rem; padding: 0.5rem; cursor: pointer; border-radius: 4px;" onmouseover="this.style.background='var(--do-gray-50)'" onmouseout="this.style.background='transparent'">
                    <input type="checkbox" ${checked} onchange="syncSearchResult(this, '${value}', '${tipo}')" style="width: 16px; height: 16px;">
                    <span style="font-size: 0.875rem;">${tipoIcon} ${nombre} <small style="color: var(--do-gray-500);">(${tipo})</small></span>
                </label>
            `;
        });

        html += '</div>';
        searchResults.html(html).show();
    } else {
        searchResults.html('<div style="padding: 1rem; text-align: center; color: var(--do-gray-500);"><i class="fas fa-search"></i> No se encontraron resultados</div>').show();
    }
}

function syncSearchResult(searchCheckbox, value, tipo) {
    const isChecked = $(searchCheckbox).is(':checked');
    const originalCheckbox = $(`input[name="sucursal"][value="${value}"]`);

    // Sincronizar con el checkbox original
    originalCheckbox.prop('checked', isChecked);

    // Actualizar contador del tipo
    updateTipoCounter(tipo);
    actualizarContadorSucursales();

    console.log(`🔄 Sincronizado: ${value} (${tipo}) = ${isChecked}`);
}

// ===== FUNCIONES DE DEBUGGING =====

function testConectividadSucursales() {
    console.log('🧪 Probando conectividad con el endpoint de sucursales...');

    const empresaId = $('#idempresa').val() || 1; // Usar empresa 1 como test

    $.ajax({
        url: '{{ path("ventas-generales-obtener-sucursales") }}',
        type: 'GET',
        data: { idempresa: empresaId },
        success: function(data, textStatus, xhr) {
            console.log('✅ Test de conectividad exitoso');
            console.log('📊 Status:', xhr.status);
            console.log('📄 Content-Type:', xhr.getResponseHeader('Content-Type'));
            console.log('📝 Datos recibidos (primeros 500 chars):', data.substring(0, 500));
            console.log('📏 Tamaño total:', data.length, 'caracteres');
        },
        error: function(xhr, status, error) {
            console.error('❌ Test de conectividad falló');
            console.error('📊 Status:', xhr.status);
            console.error('📄 Response:', xhr.responseText);
            console.error('🔍 Error:', error);
        }
    });
}

// ===== INVENTORY DASHBOARD FUNCTIONS =====

let inventoryRefreshInterval;
let lastInventoryUpdate = null;
let graficasCargandose = false;

// Variables para el loading inicial
let loadingProgress = 0;
let loadingSteps = {
    empresa: false,
    fechas: false,
    sucursales: false,
    tipos: false,
    inventario: false,
    graficas: false
};

// Inicializar dashboard de inventario
function initInventoryDashboard() {
    console.log('🏭 Inicializando Dashboard de Inventario');

    // Cargar datos iniciales si hay empresa seleccionada
    const empresaId = $('#idempresa').val();
    if (empresaId) {
        loadInventoryData();
    } else {
        showInventoryMessage('Seleccione una empresa para ver el inventario');
    }

    // Configurar auto-refresh cada 30 segundos
    inventoryRefreshInterval = setInterval(() => {
        const currentEmpresaId = $('#idempresa').val();
        if (currentEmpresaId) {
            loadInventoryData();
        }
    }, 30000);

    console.log('✅ Dashboard de Inventario inicializado');
}

// Cargar todos los datos de inventario
async function loadInventoryData() {
    try {
        const empresaId = getEmpresaId();

        if (!empresaId) {
            showInventoryMessage('Error: Empresa fija no disponible');
            return;
        }

        console.log('📦 Cargando datos de inventario para empresa:', empresaId);

        // Cargar datos en paralelo
        const [sucursalesData, campanasData] = await Promise.all([
            loadTopSucursales(empresaId),
            loadTopCampanas(empresaId)
        ]);

        // Actualizar UI
        renderTopSucursales(sucursalesData);
        renderTopCampanas(campanasData);

        // Cargar gráfica de distribución de stock
        renderGraficaStockSucursales(sucursalesData);

        // Actualizar timestamp
        updateLastUpdateTime();

        console.log('✅ Datos de inventario cargados exitosamente');

    } catch (error) {
        console.error('❌ Error cargando datos de inventario:', error);
        showInventoryError('Error al cargar datos de inventario');
    }
}

// Cargar top sucursales
async function loadTopSucursales(empresaId) {
    console.log('🏪 Cargando top sucursales para empresa:', empresaId);

    const url = `{{ path('ventas-generales-inventario-sucursales') }}?idempresa=${empresaId}`;
    console.log('🔗 URL sucursales:', url);

    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    });

    if (!response.ok) {
        console.error('❌ Error en respuesta sucursales:', response.status, response.statusText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Datos de sucursales recibidos:', data);
    return data;
}

// Cargar top campañas
async function loadTopCampanas(empresaId) {
    console.log('🎯 Cargando top campañas para empresa:', empresaId);

    const url = `{{ path('ventas-generales-inventario-campanas') }}?idempresa=${empresaId}`;
    console.log('🔗 URL campañas:', url);

    const response = await fetch(url, {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest'
        }
    });

    if (!response.ok) {
        console.error('❌ Error en respuesta campañas:', response.status, response.statusText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log('📊 Datos de campañas recibidos:', data);
    return data;
}

// Renderizar top sucursales
function renderTopSucursales(data) {
    const container = $('#top-sucursales-container');

    if (!data.sucursales || data.sucursales.length === 0) {
        container.html(`
            <div style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
                <i class="fas fa-inbox" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p style="margin: 0;">No hay datos de sucursales disponibles</p>
            </div>
        `);
        return;
    }

    let html = '<div class="inventory-list">';

    data.sucursales.forEach((sucursal, index) => {
        const trendIcon = getTrendIcon(sucursal.tendencia_stock);
        const trendColor = getTrendColor(sucursal.tendencia_stock);
        const trendText = getTrendText(sucursal.tendencia_stock);

        // Formatear fechas de manera amigable
        const ultimaVenta = formatearFechaAmigable(sucursal.ultima_venta);
        const ultimaEntrada = formatearFechaAmigable(sucursal.ultima_entrada_stock);

        html += `
            <div class="inventory-item-card" style="background: white; border: 1px solid var(--do-gray-200); border-radius: 8px; padding: 1rem; margin-bottom: 0.75rem; transition: all 0.2s ease; box-shadow: 0 1px 3px rgba(0,0,0,0.1);" onmouseover="this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.style.transform='translateY(-1px)'" onmouseout="this.style.boxShadow='0 1px 3px rgba(0,0,0,0.1)'; this.style.transform='translateY(0)'">

                <!-- Header con nombre y tendencia -->
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.75rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-store" style="color: var(--do-primary-blue); font-size: 1.2rem;"></i>
                        <span style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">${index + 1}. ${sucursal.nombre}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.25rem;">
                        <span style="color: ${trendColor}; font-size: 1rem;">${trendIcon}</span>
                        <span style="color: ${trendColor}; font-size: 0.75rem; font-weight: 600;">${trendText}</span>
                    </div>
                </div>

                <!-- Métricas principales en grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-bottom: 0.75rem;">
                    <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-boxes" style="color: var(--do-warning-amber); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Stock Disponible</span>
                        </div>
                        <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">${sucursal.stock_disponible.toLocaleString()} unidades</div>
                    </div>

                    <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-dollar-sign" style="color: var(--vg-success-soft); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Valor Total</span>
                        </div>
                        <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">$${sucursal.valor_stock_disponible.toLocaleString()}</div>
                    </div>
                </div>

                <!-- Costo total -->
                <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px; margin-bottom: 0.75rem;">
                    <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-coins" style="color: var(--do-gray-600); font-size: 1rem;"></i>
                        <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Costo Total</span>
                    </div>
                    <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">$${sucursal.costo_total.toLocaleString()}</div>
                </div>


                <!-- Métricas secundarias -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-bottom: 0.75rem;">
                    <div style="background: var(--do-blue-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-chart-line" style="color: var(--do-primary-blue); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Rotación</span>
                        </div>
                        <div style="font-weight: 700; color: var(--do-primary-blue); font-size: 1.1rem;">${sucursal.rotacion_inventario.toFixed(1)}%</div>
                    </div>

                    <div style="background: var(--do-green-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-shopping-cart" style="color: var(--vg-success-soft); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Vendido (30d)</span>
                        </div>
                        <div style="font-weight: 700; color: var(--vg-success-soft); font-size: 1.1rem;">${sucursal.stock_vendido_30d.toLocaleString()} unidades</div>
                    </div>
                </div>

                <!-- Información temporal y botón de auditoría -->
                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.8rem; color: var(--do-gray-500); border-top: 1px solid var(--do-gray-200); padding-top: 0.75rem;">
                    <div style="display: flex; flex-direction: column; gap: 0.375rem;">
                        <div style="display: flex; align-items: center; gap: 0.375rem;">
                            <i class="fas fa-clock" style="font-size: 0.8rem;"></i>
                            <span>Última venta: ${ultimaVenta}</span>
                        </div>
                        <div style="display: flex; align-items: center; gap: 0.375rem;">
                            <i class="fas fa-truck" style="font-size: 0.8rem;"></i>
                            <span>Última entrada: ${ultimaEntrada}</span>
                        </div>
                    </div>
                </div>

            </div>
        `;
    });

    html += '</div>';
    container.html(html);
}

// Renderizar top campañas
function renderTopCampanas(data) {
    const container = $('#top-campanas-container');

    if (!data.campanas || data.campanas.length === 0) {
        container.html(`
            <div style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
                <i class="fas fa-bullhorn" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p style="margin: 0;">No hay campañas disponibles</p>
            </div>
        `);
        return;
    }

    let html = '<div class="inventory-list">';

    data.campanas.forEach((campana, index) => {
        const trendIcon = getTrendIcon(campana.tendencia_ventas);
        const trendColor = getTrendColor(campana.tendencia_ventas);
        const trendText = getTrendText(campana.tendencia_ventas);

        // Calcular estado de la campaña
        const estadoCampana = getEstadoCampana(campana.eficiencia_campana);
        const colorEficiencia = getColorEficiencia(campana.eficiencia_campana);

        html += `
            <div class="inventory-item-card" style="background: white; border: 1px solid var(--do-gray-200); border-radius: 8px; padding: 1rem; margin-bottom: 0.75rem; transition: all 0.2s ease; box-shadow: 0 1px 3px rgba(0,0,0,0.1);" onmouseover="this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'; this.style.transform='translateY(-1px)'" onmouseout="this.style.boxShadow='0 1px 3px rgba(0,0,0,0.1)'; this.style.transform='translateY(0)'">

                <!-- Header con nombre y tendencia -->
                <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 0.75rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <i class="fas fa-bullhorn" style="color: var(--do-warning-amber); font-size: 1.2rem;"></i>
                        <span style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">${index + 1}. ${campana.nombre}</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.25rem;">
                        <span style="color: ${trendColor}; font-size: 1rem;">${trendIcon}</span>
                        <span style="color: ${trendColor}; font-size: 0.75rem; font-weight: 600;">${trendText}</span>
                    </div>
                </div>

                <!-- Métricas principales en grid -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-bottom: 0.75rem;">
                    <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-boxes" style="color: var(--do-warning-amber); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Stock Disponible</span>
                        </div>
                        <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">${campana.stock_disponible.toLocaleString()} unidades</div>
                    </div>

                    <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-dollar-sign" style="color: var(--vg-success-soft); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Valor Total</span>
                        </div>
                        <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">$${campana.valor_stock_disponible.toLocaleString()}</div>
                    </div>
                </div>

                <!-- Costo total (campaña) -->
                <div style="background: var(--do-gray-50); padding: 0.75rem; border-radius: 6px; margin-bottom: 0.75rem;">
                    <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                        <i class="fas fa-coins" style="color: var(--do-gray-600); font-size: 1rem;"></i>
                        <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Costo Total</span>
                    </div>
                    <div style="font-weight: 700; color: var(--do-gray-900); font-size: 1.1rem;">$${campana.costo_total.toLocaleString()}</div>
                </div>


                <!-- Métricas de rendimiento -->
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.75rem; margin-bottom: 0.75rem;">
                    <div style="background: ${colorEficiencia.bg}; padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-chart-pie" style="color: ${colorEficiencia.text}; font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Eficiencia</span>
                        </div>
                        <div style="font-weight: 700; color: ${colorEficiencia.text}; font-size: 1.1rem;">${campana.eficiencia_campana.toFixed(1)}% ${estadoCampana.icon}</div>
                    </div>

                    <div style="background: var(--do-green-50); padding: 0.75rem; border-radius: 6px;">
                        <div style="display: flex; align-items: center; gap: 0.25rem; margin-bottom: 0.5rem;">
                            <i class="fas fa-shopping-cart" style="color: var(--vg-success-soft); font-size: 1rem;"></i>
                            <span style="font-size: 0.875rem; color: var(--do-gray-600); font-weight: 500;">Total Vendido</span>
                        </div>
                        <div style="font-weight: 700; color: var(--vg-success-soft); font-size: 1.1rem;">${campana.stock_vendido.toLocaleString()} unidades</div>
                    </div>
                </div>

                <!-- Información temporal y estado -->
                <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.8rem; color: var(--do-gray-500); border-top: 1px solid var(--do-gray-200); padding-top: 0.75rem;">
                    <div style="display: flex; align-items: center; gap: 0.375rem;">
                        <i class="fas fa-calendar-alt" style="font-size: 0.8rem;"></i>
                        <span>${campana.dias_activa} días activa</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.375rem;">
                        <i class="fas fa-fire" style="font-size: 0.8rem;"></i>
                        <span>${campana.ventas_ultimos_7d} ventas (7d)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <div style="background: ${estadoCampana.color}; color: white; padding: 0.25rem 0.5rem; border-radius: 12px; font-size: 0.75rem; font-weight: 600;">
                            ${estadoCampana.text}
                        </div>
                        <button onclick="abrirModalDetalleCampana(${campana.id}, '${campana.nombre.replace(/'/g, "\\'")}')" style="padding: 0.35rem 0.6rem; background: var(--do-primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer; font-size: 0.75rem;">
                            Ver Detalles
                        </button>
                    </div>
                </div>

            </div>
        `;
    });

    // Modal de detalle de campaña (definición global)
    function abrirModalDetalleCampana(campanaId, campanaNombre) {
        const modalId = 'modal-detalle-campana';
        // Eliminar si existe previo
        const existing = document.getElementById(modalId);
        if (existing) existing.remove();

        // Registrar modal y obtener z-index
        const zIndex = window.modalManager.registerModal(modalId, 'detalle-campana');

        const modalHtml = `
        <div id="${modalId}" style="position:fixed; inset:0; background:rgba(0,0,0,0.45); display:flex; align-items:center; justify-content:center; z-index:${zIndex};">
          <div style="background:#fff; width:min(1400px,98vw); max-height:95vh; border-radius:10px; box-shadow:0 10px 25px rgba(0,0,0,.2); display:flex; flex-direction:column; overflow:hidden;">
            <div style="padding:1rem 1.25rem; border-bottom:1px solid var(--do-gray-200); display:flex; align-items:center; justify-content:space-between;">
              <h3 style="margin:0; font-size:1.125rem; font-weight:700; color:var(--do-gray-900); display:flex; align-items:center; gap:.5rem;">
                <i class="fas fa-bullhorn" style="color:var(--do-warning-amber);"></i>
                Detalle de Campaña: ${campanaNombre}
              </h3>
              <button onclick="cerrarModal('${modalId}')" style="background:transparent; border:none; font-size:1.25rem; cursor:pointer; color:var(--do-gray-500);">&times;</button>
            </div>
            <div style="padding:1rem 1.25rem; overflow:auto;">
              <div id="detalle-campana-loading" style="text-align:center; padding:2rem; color:var(--do-gray-500);">
                <i class="fas fa-spinner fa-spin" style="font-size:2rem; margin-bottom:.5rem;"></i>
                <p style="margin:0;">Cargando detalles...</p>
              </div>
              <div id="detalle-campana-error" style="display:none; text-align:center; padding:2rem; color: var(--vg-error-soft);"></div>
              <div id="detalle-campana-contenido" style="display:none;"></div>
            </div>
            <div style="padding:.75rem 1.25rem; border-top:1px solid var(--do-gray-200); display:flex; justify-content:flex-end; gap:.5rem;">
              <button onclick="cerrarModal('${modalId}')" style="padding:.5rem .75rem; border:1px solid var(--do-gray-300); background:#fff; border-radius:6px; cursor:pointer;">Cerrar</button>
            </div>
          </div>
        </div>`;

        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Cargar detalles (si el endpoint aún no existe, manejar error)
        fetch(`/ajax/inventario-detalle-campana?campana_id=${encodeURIComponent(campanaId)}`, {
          headers: { 'X-Requested-With': 'XMLHttpRequest' }
        }).then(resp => {
          if (!resp.ok) throw new Error('Respuesta no válida');
          return resp.json();
        }).then(data => {
          const cont = document.getElementById('detalle-campana-contenido');
          const loader = document.getElementById('detalle-campana-loading');
          const err = document.getElementById('detalle-campana-error');

          if (!data || !Array.isArray(data.sucursales) || data.sucursales.length === 0) {
            loader.style.display = 'none';
            err.style.display = 'block';
            err.textContent = 'No hay información de stock para esta campaña';
            return;
          }

          let html = '<div style="overflow-x:auto;">\n' +
                     '<table class="table table-striped" style="width:100%; font-size: 1.2rem;">\n' +
                     '<thead style="background-color: var(--do-gray-100);"><tr>' +
                     '<th style="font-weight: 600;">Sucursal</th>' +
                     '<th style="font-weight: 600;">Modelo</th>' +
                     '<th style="font-weight: 600;">SKU</th>' +
                     '<th style="font-weight: 600;">UPC</th>' +
                     '<th style="font-weight: 600; text-align:center;">Cantidad</th>' +
                     '<th style="font-weight: 600; text-align:center;">Tipo de Producto</th>' +
                     '<th style="font-weight: 600;">Categoría</th>' +
                     '<th style="font-weight: 600;">Marca</th>' +
                     '<th style="font-weight: 600; text-align:right;">Costo</th>' +
                     '<th style="font-weight: 600; text-align:right;">Precio</th>' +
                     '<th style="font-weight: 600; text-align:center;">Opciones</th>' +
                     '</tr></thead><tbody>';

          // Iterar por sucursales y luego por productos
          data.sucursales.forEach(sucursal => {
            if (sucursal.productos && sucursal.productos.length > 0) {
              sucursal.productos.forEach((producto, index) => {
                // Determinar tipo de producto
                const tipoTexto = producto.tipoProducto === '1' ? 'Armazón' :
                                 producto.tipoProducto === '2' ? 'Servicio' :
                                 'Otro';

                // Mostrar nombre de sucursal solo en la primera fila de cada sucursal
                const sucursalNombre = index === 0 ?
                  `<td rowspan="${sucursal.productos.length}" style="vertical-align:middle; background-color: var(--do-gray-50); font-weight: 600; border-right: 2px solid var(--do-primary-blue);">${sucursal.nombre}</td>` :
                  '';

                // Formatear precios
                const costoFormateado = producto.costo ? `$${producto.costo.toLocaleString('es-MX', {minimumFractionDigits: 2})}` : '$0.00';
                const precioFormateado = producto.precio ? `$${producto.precio.toLocaleString('es-MX', {minimumFractionDigits: 2})}` : '$0.00';

                // Lógica para SKU: mostrar SKU del stock, si no existe mostrar UPC del producto
                const skuDisplay = producto.sku ?
                  `<code style="color: var(--do-primary-blue); font-weight: 600;">${producto.sku}</code>` :
                  (producto.upc ?
                    `<code style="color: var(--do-warning-amber); font-weight: 600;">${producto.upc}</code>` :
                    `<span style="color: var(--do-gray-400); font-style: italic;">Sin SKU</span>`);

                // UPC siempre del producto
                const upcDisplay = producto.upc ?
                  `<code style="color: var(--do-success-green); font-weight: 600;">${producto.upc}</code>` :
                  `<span style="color: var(--do-gray-400); font-style: italic;">Sin UPC</span>`;

                html += `<tr style="border-bottom: 1px solid var(--do-gray-200);">` +
                        sucursalNombre +
                        `<td style="font-weight: 500;">${producto.modelo || '-'}</td>` +
                        `<td style="font-family: monospace; background: var(--do-gray-50); padding: 0.5rem; border-radius: 4px;">
                          ${skuDisplay}
                        </td>` +
                        `<td style="font-family: monospace; background: var(--do-gray-50); padding: 0.5rem; border-radius: 4px;">
                          ${upcDisplay}
                        </td>` +
                        `<td style="text-align:center; font-weight: 600; color: var(--do-primary-blue);">${producto.disponible}</td>` +
                        `<td style="text-align:center;">${tipoTexto}</td>` +
                        `<td>${producto.categoria || '-'}</td>` +
                        `<td>${producto.marca || '-'}</td>` +
                        `<td style="text-align:right; font-weight: 500;">${costoFormateado}</td>` +
                        `<td style="text-align:right; font-weight: 600; color: var(--do-success-green);">${precioFormateado}</td>` +
                        `<td style="text-align:center;">
                          <button onclick="traspasarProducto(${producto.stockId}, '${producto.modelo}', ${producto.disponible})"
                                  class="btn btn-primary btn-sm"
                                  title="Traspasar producto a otra sucursal"
                                  data-sku="${producto.sku || ''}"
                                  data-upc="${producto.upc || ''}"
                                  data-stock-id="${producto.stockId}"
                                  data-sucursal-id="${producto.sucursalId}"
                                  style="font-size: 1.4rem; padding: 0.5rem 1rem;">
                            Traspasar
                          </button>
                        </td>` +
                        `</tr>`;
              });
            }
          });

          html += '</tbody></table></div>';

          loader.style.display = 'none';
          cont.innerHTML = html;
          cont.style.display = 'block';
        }).catch(e => {
          const loader = document.getElementById('detalle-campana-loading');
          const err = document.getElementById('detalle-campana-error');
          loader.style.display = 'none';
          err.style.display = 'block';
          err.textContent = 'Error al cargar detalles de la campaña';
          console.error('Detalle campaña error:', e);
        });
    }

    function cerrarModal(id) {
      const el = document.getElementById(id);
      if (el) {
        // Desregistrar del gestor de modales
        window.modalManager.unregisterModal(id);
        el.remove();
        console.log(`🗑️ Modal cerrado: ${id}`);
      }
    }


    html += '</div>';
    container.html(html);
}


// ===== Modal Detalle Campaña (Scope Global) =====
function abrirModalDetalleCampana(campanaId, campanaNombre) {
  const modalId = 'modal-detalle-campana';
  const existing = document.getElementById(modalId);
  if (existing) existing.remove();

  // Registrar modal y obtener z-index
  const zIndex = window.modalManager.registerModal(modalId, 'detalle-campana-alt');

  const modalHtml = `
  <div id="${modalId}" style="position:fixed; inset:0; background:rgba(0,0,0,0.45); display:flex; align-items:center; justify-content:center; z-index:${zIndex};">
    <div style="background:#fff; width:min(1400px,98vw); max-height:95vh; border-radius:10px; box-shadow:0 10px 25px rgba(0,0,0,.2); display:flex; flex-direction:column; overflow:hidden;">
      <div style="padding:1rem 1.25rem; border-bottom:1px solid var(--do-gray-200); display:flex; align-items:center; justify-content:space-between;">
        <h3 style="margin:0; font-size:1.125rem; font-weight:700; color:var(--do-gray-900); display:flex; align-items:center; gap:.5rem;">
          <i class="fas fa-bullhorn" style="color:var(--do-warning-amber);"></i>
          Detalle de Campaña: ${campanaNombre}
        </h3>
        <button onclick="cerrarModal('${modalId}')" style="background:transparent; border:none; font-size:1.25rem; cursor:pointer; color:var(--do-gray-500);">&times;</button>
      </div>
      <div style="padding:1rem 1.25rem; overflow:auto;">
        <div id="detalle-campana-loading" style="text-align:center; padding:2rem; color:var(--do-gray-500);">
          <i class="fas fa-spinner fa-spin" style="font-size:2rem; margin-bottom:.5rem;"></i>
          <p style="margin:0;">Cargando detalles...</p>
        </div>
        <div id="detalle-campana-error" style="display:none; text-align:center; padding:2rem; color: var(--vg-error-soft);"></div>
        <div id="detalle-campana-contenido" style="display:none;"></div>
      </div>
      <div style="padding:.75rem 1.25rem; border-top:1px solid var(--do-gray-200); display:flex; justify-content:flex-end; gap:.5rem;">
        <button onclick="cerrarModal('${modalId}')" style="padding:.5rem .75rem; border:1px solid var(--do-gray-300); background:#fff; border-radius:6px; cursor:pointer;">Cerrar</button>
      </div>
    </div>
  </div>`;

  document.body.insertAdjacentHTML('beforeend', modalHtml);

  fetch(`/ajax/inventario-detalle-campana?campana_id=${encodeURIComponent(campanaId)}`, {
    headers: { 'X-Requested-With': 'XMLHttpRequest' }
  }).then(resp => {
    if (!resp.ok) throw new Error('Respuesta no válida');
    return resp.json();
  }).then(data => {
    const cont = document.getElementById('detalle-campana-contenido');
    const loader = document.getElementById('detalle-campana-loading');
    const err = document.getElementById('detalle-campana-error');

    if (!data || !Array.isArray(data.sucursales) || data.sucursales.length === 0) {
      loader.style.display = 'none';
      err.style.display = 'block';
      err.textContent = 'No hay información de stock para esta campaña';
      return;
    }

    let html = '<div style="overflow-x:auto;">\n' +
               '<table class="table table-striped" style="width:100%; font-size: 1.2rem;">\n' +
               '<thead style="background-color: var(--do-gray-100);"><tr>' +
               '<th style="font-weight: 600;">Sucursal</th>' +
               '<th style="font-weight: 600;">Modelo</th>' +
               '<th style="font-weight: 600;">SKU</th>' +
               '<th style="font-weight: 600;">UPC</th>' +
               '<th style="font-weight: 600; text-align:center;">Cantidad</th>' +
               '<th style="font-weight: 600; text-align:center;">Tipo de Producto</th>' +
               '<th style="font-weight: 600;">Categoría</th>' +
               '<th style="font-weight: 600;">Marca</th>' +
               '<th style="font-weight: 600; text-align:right;">Costo</th>' +
               '<th style="font-weight: 600; text-align:right;">Precio</th>' +
               '<th style="font-weight: 600; text-align:center;">Opciones</th>' +
               '</tr></thead><tbody>';

    // Iterar por sucursales y luego por productos
    data.sucursales.forEach(sucursal => {
      if (sucursal.productos && sucursal.productos.length > 0) {
        sucursal.productos.forEach((producto, index) => {
          // Determinar tipo de producto
          const tipoTexto = producto.tipoProducto === '1' ? 'Armazón' :
                           producto.tipoProducto === '2' ? 'Servicio' :
                           'Otro';

          // Mostrar nombre de sucursal solo en la primera fila de cada sucursal
          const sucursalNombre = index === 0 ?
            `<td rowspan="${sucursal.productos.length}" style="vertical-align:middle; background-color: var(--do-gray-50); font-weight: 600; border-right: 2px solid var(--do-primary-blue);">${sucursal.nombre}</td>` :
            '';

          // Formatear precios
          const costoFormateado = producto.costo ? `$${producto.costo.toLocaleString('es-MX', {minimumFractionDigits: 2})}` : '$0.00';
          const precioFormateado = producto.precio ? `$${producto.precio.toLocaleString('es-MX', {minimumFractionDigits: 2})}` : '$0.00';

          // Lógica para SKU: mostrar SKU del stock, si no existe mostrar UPC del producto
          const skuDisplay = producto.sku ?
            `<code style="color: var(--do-primary-blue); font-weight: 600;">${producto.sku}</code>` :
            (producto.upc ?
              `<code style="color: var(--do-warning-amber); font-weight: 600;">${producto.upc}</code>` :
              `<span style="color: var(--do-gray-400); font-style: italic;">Sin SKU</span>`);

          // UPC siempre del producto
          const upcDisplay = producto.upc ?
            `<code style="color: var(--do-success-green); font-weight: 600;">${producto.upc}</code>` :
            `<span style="color: var(--do-gray-400); font-style: italic;">Sin UPC</span>`;

          html += `<tr style="border-bottom: 1px solid var(--do-gray-200);">` +
                  sucursalNombre +
                  `<td style="font-weight: 500;">${producto.modelo || '-'}</td>` +
                  `<td style="font-family: monospace; background: var(--do-gray-50); padding: 0.5rem; border-radius: 4px;">
                    ${skuDisplay}
                  </td>` +
                  `<td style="font-family: monospace; background: var(--do-gray-50); padding: 0.5rem; border-radius: 4px;">
                    ${upcDisplay}
                  </td>` +
                  `<td style="text-align:center; font-weight: 600; color: var(--do-primary-blue);">${producto.disponible}</td>` +
                  `<td style="text-align:center;">${tipoTexto}</td>` +
                  `<td>${producto.categoria || '-'}</td>` +
                  `<td>${producto.marca || '-'}</td>` +
                  `<td style="text-align:right; font-weight: 500;">${costoFormateado}</td>` +
                  `<td style="text-align:right; font-weight: 600; color: var(--do-success-green);">${precioFormateado}</td>` +
                  `<td style="text-align:center;">
                    <button onclick="traspasarProducto(${producto.stockId}, '${producto.modelo}', ${producto.disponible})"
                            class="btn btn-primary btn-sm"
                            title="Traspasar producto a otra sucursal"
                            data-sku="${producto.sku || ''}"
                            data-upc="${producto.upc || ''}"
                            data-stock-id="${producto.stockId}"
                            data-sucursal-id="${producto.sucursalId}"
                            style="font-size: 1.4rem; padding: 0.5rem 1rem;">
                      Traspasar
                    </button>
                  </td>` +
                  `</tr>`;
        });
      }
    });

    html += '</tbody></table></div>';

    loader.style.display = 'none';
    cont.innerHTML = html;
    cont.style.display = 'block';
  }).catch(e => {
    const loader = document.getElementById('detalle-campana-loading');
    const err = document.getElementById('detalle-campana-error');
    loader.style.display = 'none';
    err.style.display = 'block';
    err.textContent = 'Error al cargar detalles de la campaña';
    console.error('Detalle campaña error:', e);
  });
}

function cerrarModal(id) {
  const el = document.getElementById(id);
  if (el) el.remove();
}

// Funciones para los botones de opciones en la tabla de productos
function verDetalleProducto(stockId) {
  console.log('Ver detalle del producto con stockId:', stockId);

  // Aquí puedes agregar la lógica para mostrar más detalles del producto
  // Por ejemplo, abrir otro modal con información completa del stock
  Swal.fire({
    title: 'Detalle del Producto',
    text: `Cargando información del stock ID: ${stockId}`,
    icon: 'info',
    showConfirmButton: true,
    confirmButtonText: 'Cerrar'
  });

  // TODO: Implementar llamada AJAX para obtener detalles completos del producto
  // fetch(`/ajax/detalle-stock/${stockId}`)...
}

function auditarStock(stockId) {
  console.log('Auditar stock con stockId:', stockId);

  Swal.fire({
    title: 'Auditoría de Stock',
    html: `
      <div style="text-align: left;">
        <p><strong>Stock ID:</strong> ${stockId}</p>
        <div style="margin: 1rem 0;">
          <label for="cantidad-fisica" style="display: block; margin-bottom: 0.5rem;">Cantidad física contada:</label>
          <input type="number" id="cantidad-fisica" class="swal2-input" placeholder="Ingrese cantidad..." min="0">
        </div>
        <div style="margin: 1rem 0;">
          <label for="observaciones" style="display: block; margin-bottom: 0.5rem;">Observaciones:</label>
          <textarea id="observaciones" class="swal2-textarea" placeholder="Observaciones de la auditoría..."></textarea>
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: 'Guardar Auditoría',
    cancelButtonText: 'Cancelar',
    preConfirm: () => {
      const cantidad = document.getElementById('cantidad-fisica').value;
      const observaciones = document.getElementById('observaciones').value;

      if (!cantidad || cantidad < 0) {
        Swal.showValidationMessage('Por favor ingrese una cantidad válida');
        return false;
      }

      return { cantidad: parseInt(cantidad), observaciones: observaciones };
    }
  }).then((result) => {
    if (result.isConfirmed) {
      console.log('Datos de auditoría:', result.value);

      // TODO: Implementar llamada AJAX para guardar la auditoría
      // fetch('/ajax/guardar-auditoria-stock', {
      //   method: 'POST',
      //   body: JSON.stringify({
      //     stockId: stockId,
      //     cantidadFisica: result.value.cantidad,
      //     observaciones: result.value.observaciones
      //   })
      // })...

      Swal.fire({
        title: 'Auditoría Guardada',
        text: 'La auditoría se ha registrado correctamente',
        icon: 'success',
        timer: 2000,
        showConfirmButton: false
      });
    }
  });
}

// Renderizar gráfica de distribución de stock por sucursales
function renderGraficaStockSucursales(data) {
    // Verificar que Highcharts esté disponible
    if (typeof Highcharts === 'undefined') {
        console.error('❌ Highcharts no está cargado');
        $('#grafica-stock-sucursales').html(`
            <div style="text-align: center; padding: 2rem; color: var(--vg-error-soft);">
                <i class="fas fa-exclamation-triangle" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p style="margin: 0;">Error: Highcharts no disponible</p>
            </div>
        `);
        return;
    }

    if (!data.sucursales || data.sucursales.length === 0) {
        $('#grafica-stock-sucursales').html(`
            <div style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
                <i class="fas fa-chart-pie" style="font-size: 2rem; margin-bottom: 0.5rem;"></i>
                <p style="margin: 0;">No hay datos para mostrar</p>
            </div>
        `);
        return;
    }

    // Preparar datos para la gráfica
    const categorias = data.sucursales.map(s => s.nombre);
    const valores = data.sucursales.map(s => s.stock_disponible);

    // Total global de inventario (unidades)
    const totalGlobal = valores.reduce((acc, v) => acc + (parseInt(v) || 0), 0);
    const totalElem = document.getElementById('total-inventario-global-num');
    if (totalElem) totalElem.textContent = totalGlobal.toLocaleString('es-MX');

    // Configurar la gráfica como barras
    Highcharts.chart('grafica-stock-sucursales', {
        chart: { type: 'bar', backgroundColor: 'transparent', height: 300 },
        title: { text: null },
        xAxis: { categories: categorias, title: { text: null } },
        yAxis: { min: 0, title: { text: 'Unidades', align: 'high' } },
        tooltip: { valueSuffix: ' u' },
        plotOptions: {
            bar: {
                dataLabels: { enabled: true, formatter: function(){ return Highcharts.numberFormat(this.y, 0) + ' u'; } },
                color: '#3b82f6'
            }
        },
        series: [{ name: 'Stock', data: valores }],
        credits: { enabled: false },
        exporting: { enabled: false }
    });
}

// Utilidades para tendencias
function getTrendIcon(tendencia) {
    switch(tendencia) {
        case 'up': return '<i class="fas fa-arrow-up"></i>';
        case 'down': return '<i class="fas fa-arrow-down"></i>';
        default: return '<i class="fas fa-minus"></i>';
    }
}

function getTrendColor(tendencia) {
    switch(tendencia) {
        case 'up': return '#10b981'; // Verde
        case 'down': return '#ef4444'; // Rojo
        default: return '#6b7280'; // Gris
    }
}

function getTrendText(tendencia) {
    switch(tendencia) {
        case 'up': return 'Subiendo';
        case 'down': return 'Bajando';
        default: return 'Estable';
    }
}

// Utilidades para formateo de fechas
function formatearFechaAmigable(fecha) {
    if (!fecha) return 'No disponible';

    try {
        const fechaObj = new Date(fecha);
        const ahora = new Date();
        const diffMs = ahora - fechaObj;
        const diffHoras = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDias = Math.floor(diffHoras / 24);

        if (diffHoras < 1) return 'Hace menos de 1 hora';
        if (diffHoras < 24) return `Hace ${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
        if (diffDias === 1) return 'Ayer';
        if (diffDias < 7) return `Hace ${diffDias} días`;

        return fechaObj.toLocaleDateString('es-MX', {
            day: '2-digit',
            month: '2-digit',
            year: '2-digit'
        });
    } catch (e) {
        return fecha;
    }
}

// Utilidades para campañas
function getEstadoCampana(eficiencia) {
    if (eficiencia >= 80) {
        return { text: 'EXCELENTE', color: '#10b981', icon: '🔥' };
    } else if (eficiencia >= 60) {
        return { text: 'BUENA', color: '#f59e0b', icon: '👍' };
    } else if (eficiencia >= 40) {
        return { text: 'REGULAR', color: '#f97316', icon: '⚠️' };
    } else if (eficiencia >= 20) {
        return { text: 'BAJA', color: '#ef4444', icon: '📉' };
    } else {
        return { text: 'MUY BAJA', color: '#dc2626', icon: '🚨' };
    }
}

function getColorEficiencia(eficiencia) {
    if (eficiencia >= 80) {
        return { bg: '#dcfce7', text: '#166534' }; // Verde
    } else if (eficiencia >= 60) {
        return { bg: '#fef3c7', text: '#92400e' }; // Amarillo
    } else if (eficiencia >= 40) {
        return { bg: '#fed7aa', text: '#c2410c' }; // Naranja
    } else {
        return { bg: '#fecaca', text: '#dc2626' }; // Rojo
    }
}

// Refresh manual
function refreshInventoryData() {
    const button = $('#refresh-inventory');
    const icon = button.find('i');

    // Animación de loading
    icon.addClass('fa-spin');
    button.prop('disabled', true);

    loadInventoryData().finally(() => {
        setTimeout(() => {
            icon.removeClass('fa-spin');
            button.prop('disabled', false);
        }, 500);
    });
}

// Actualizar timestamp
function updateLastUpdateTime() {
    const now = new Date();
    const dateString = now.toLocaleDateString('es-MX', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    const timeString = now.toLocaleTimeString('es-MX', {
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });

    $('#inventory-last-update').text(`Última actualización: ${dateString} a las ${timeString}`);
    lastInventoryUpdate = now;
}

// Mostrar mensajes
function showInventoryMessage(message) {
    $('#top-sucursales-container, #top-campanas-container').html(`
        <div style="text-align: center; padding: 2rem; color: var(--do-gray-500);">
            <i class="fas fa-info-circle"></i>
            <p style="margin: 0.5rem 0 0 0;">${message}</p>
        </div>
    `);
}

function showInventoryError(message) {
    $('#top-sucursales-container, #top-campanas-container').html(`
        <div style="text-align: center; padding: 2rem; color: var(--vg-error-soft);">
            <i class="fas fa-exclamation-triangle"></i>
            <p style="margin: 0.5rem 0 0 0;">${message}</p>
        </div>
    `);
}

// ===== AUDITORÍA RÁPIDA FUNCTIONS =====

let auditoriaData = {
    sucursalId: null,
    sucursalNombre: '',
    stockSistema: 0,
    valorSistema: 0
};

// Abrir modal de auditoría rápida
function abrirAuditoriaRapida(sucursalId, sucursalNombre, stockSistema, valorSistema) {
    console.log('🔍 Abriendo auditoría rápida para:', sucursalNombre);

    // Guardar datos
    auditoriaData = {
        sucursalId: sucursalId,
        sucursalNombre: sucursalNombre,
        stockSistema: stockSistema,
        valorSistema: valorSistema
    };

    // Actualizar contenido del modal
    $('#modal-sucursal-nombre').text(sucursalNombre);
    $('#stock-sistema-unidades').text(stockSistema.toLocaleString() + ' unidades');
    $('#stock-sistema-valor').text('$' + valorSistema.toLocaleString());

    // Limpiar campos
    $('#stock-fisico-input').val('');
    $('#stock-fisico-valor').text('$0');
    $('#notas-auditoria').val('');
    $('#diferencias-container').hide();

    // Actualizar fecha
    const ahora = new Date();
    $('#fecha-auditoria').text(ahora.toLocaleDateString('es-MX') + ' ' + ahora.toLocaleTimeString('es-MX', {hour: '2-digit', minute: '2-digit'}));

    // Mostrar modal
    $('#modal-auditoria').css('display', 'flex');
    $('#modal-auditoria-contenido').show();
    $('#modal-auditoria-loading').hide();

    // Focus en el input
    setTimeout(() => {
        $('#stock-fisico-input').focus();
    }, 100);
}

// Cerrar modal de auditoría
function cerrarModalAuditoria() {
    $('#modal-auditoria').css('display', 'none');

    // Limpiar datos
    auditoriaData = {
        sucursalId: null,
        sucursalNombre: '',
        stockSistema: 0,
        valorSistema: 0
    };
}

// Calcular diferencias en tiempo real
function calcularDiferencias() {
    const stockFisico = parseInt($('#stock-fisico-input').val()) || 0;

    if (stockFisico === 0) {
        $('#stock-fisico-valor').text('$0');
        $('#diferencias-container').hide();
        return;
    }

    // Calcular valor físico estimado
    const valorPromedio = auditoriaData.stockSistema > 0 ? auditoriaData.valorSistema / auditoriaData.stockSistema : 0;
    const valorFisico = stockFisico * valorPromedio;

    // Calcular diferencias
    const diferenciaUnidades = stockFisico - auditoriaData.stockSistema;
    const diferenciaValor = valorFisico - auditoriaData.valorSistema;

    // Actualizar UI
    $('#stock-fisico-valor').text('$' + valorFisico.toLocaleString());

    // Mostrar diferencias
    $('#diferencias-container').show();

    // Colorear diferencias según sean positivas o negativas
    const colorUnidades = diferenciaUnidades > 0 ? '#10b981' : diferenciaUnidades < 0 ? '#ef4444' : '#6b7280';
    const colorValor = diferenciaValor > 0 ? '#10b981' : diferenciaValor < 0 ? '#ef4444' : '#6b7280';

    const signoUnidades = diferenciaUnidades > 0 ? '+' : '';
    const signoValor = diferenciaValor > 0 ? '+$' : diferenciaValor < 0 ? '-$' : '$';

    $('#diferencia-unidades').text(signoUnidades + diferenciaUnidades.toLocaleString() + ' unidades').css('color', colorUnidades);
    $('#diferencia-valor').text(signoValor + Math.abs(diferenciaValor).toLocaleString()).css('color', colorValor);
}

// Guardar auditoría
async function guardarAuditoria() {
    const stockFisico = parseInt($('#stock-fisico-input').val());
    const notas = $('#notas-auditoria').val().trim();

    if (stockFisico === null || stockFisico === undefined || isNaN(stockFisico)) {
        alert('Por favor ingrese el stock físico contado');
        $('#stock-fisico-input').focus();
        return;
    }

    if (stockFisico < 0) {
        alert('El stock físico no puede ser negativo');
        $('#stock-fisico-input').focus();
        return;
    }

    // Mostrar loading
    $('#modal-auditoria-contenido').hide();
    $('#modal-auditoria-loading').show();

    try {
        const response = await fetch('{{ path("ventas-generales-auditoria-rapida") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                sucursal_id: auditoriaData.sucursalId,
                stock_fisico: stockFisico,
                notas: notas
            })
        });

        const result = await response.json();

        if (result.success) {
            // Mostrar mensaje de éxito
            alert('✅ Auditoría guardada exitosamente');

            // Cerrar modal
            cerrarModalAuditoria();

            // Opcional: Recargar datos de inventario para reflejar cambios
            loadInventoryData();

            console.log('✅ Auditoría guardada:', result.auditoria);

        } else {
            throw new Error(result.error || 'Error desconocido');
        }

    } catch (error) {
        console.error('❌ Error guardando auditoría:', error);
        alert('❌ Error al guardar la auditoría: ' + error.message);

        // Volver a mostrar el contenido
        $('#modal-auditoria-loading').hide();
        $('#modal-auditoria-contenido').show();
    }
}

// Cerrar modal al hacer click fuera
$(document).on('click', '#modal-auditoria', function(e) {
    if (e.target === this) {
        cerrarModalAuditoria();
    }
});

// Cerrar modal con tecla Escape
$(document).on('keydown', function(e) {
    if (e.key === 'Escape' && $('#modal-auditoria').is(':visible')) {
        cerrarModalAuditoria();
    }
});

// Función para verificar que toda la configuración automática esté completa
function verificarConfiguracionCompleta() {
    console.log('🔍 Verificando configuración automática completa...');

    const empresaId = getEmpresaId();
    const sucursalesSeleccionadas = $('input[name="sucursal"]:checked').length;
    const tiposVentaSeleccionados = $('input[name="tipoventa"]:checked').length;
    const fechaInicio = $('#fecha-inicio').val();
    const fechaFin = $('#fecha-fin').val();

    console.log('📊 Estado final de configuración:');
    console.log('  🏢 Empresa:', empresaId);
    console.log('  🏪 Sucursales seleccionadas:', sucursalesSeleccionadas);
    console.log('  🏷️ Tipos de venta seleccionados:', tiposVentaSeleccionados);
    console.log('  📅 Fechas:', fechaInicio, 'a', fechaFin);

    // Verificar que todos los pasos estén completados (usando contenido de badges)
    const paso1Completado = $('.filtro-step[data-step="1"] .step-status').text().includes('COMPLETADO');
    const paso2Completado = $('.filtro-step[data-step="2"] .step-status').text().includes('COMPLETADO');
    const paso3Completado = $('.filtro-step[data-step="3"] .step-status').text().includes('COMPLETADO');
    const paso4Completado = $('.filtro-step[data-step="4"] .step-status').text().includes('COMPLETADO');

    console.log('✅ Estados de pasos:');
    console.log('  Paso 1 (Empresa):', paso1Completado ? '✅' : '❌');
    console.log('  Paso 2 (Sucursales):', paso2Completado ? '✅' : '❌');
    console.log('  Paso 3 (Fechas):', paso3Completado ? '✅' : '❌');
    console.log('  Paso 4 (Tipos Venta):', paso4Completado ? '✅' : '❌');

    // Verificar botón de generar
    const botonHabilitado = !$('#btn-generar-reporte').prop('disabled');
    console.log('🎯 Botón generar habilitado:', botonHabilitado ? '✅' : '❌');

    if (empresaId && sucursalesSeleccionadas > 0 && tiposVentaSeleccionados > 0 && fechaInicio && fechaFin) {
        console.log('🎉 ¡CONFIGURACIÓN AUTOMÁTICA COMPLETADA EXITOSAMENTE!');
        console.log('📊 Sistema listo para generar reportes del día actual');

        // Forzar actualización visual de todos los pasos
        forzarActualizacionPasos();

        // No mostrar alerta verde - el loading overlay ya indica que está listo
    } else {
        console.warn('⚠️ Configuración automática incompleta, verificando...');
        console.log('🔍 Faltantes: Tipos de venta:', tiposVentaSeleccionados);

        // Si faltan tipos de venta, intentar cargarlos
        if (tiposVentaSeleccionados === 0) {
            console.log('🔄 Intentando cargar tipos de venta...');
            cargarTiposVenta();
        }

        // Intentar validar filtros una vez más
        setTimeout(() => {
            validarFiltrosParaGenerar();
        }, 1000);
    }
}

// Función para forzar la actualización visual de todos los pasos
function forzarActualizacionPasos() {
    console.log('🔄 Forzando actualización visual de todos los pasos...');

    // Verificar y actualizar cada paso
    const empresaId = getEmpresaId();
    const sucursalesSeleccionadas = $('input[name="sucursal"]:checked').length;
    const tiposVentaSeleccionados = $('input[name="tipoventa"]:checked').length;
    const fechaInicio = $('#fecha-inicio').val();

    if (empresaId) {
        completarPaso(1);
    }

    if (fechaInicio) {
        completarPaso(2);
    }

    if (sucursalesSeleccionadas > 0) {
        completarPaso(3);
    }

    if (tiposVentaSeleccionados > 0) {
        completarPaso(4);
    }

    console.log('✅ Actualización visual de pasos completada');
}

// Limpiar interval al salir
window.addEventListener('beforeunload', () => {
    if (inventoryRefreshInterval) {
        clearInterval(inventoryRefreshInterval);
    }
});

// Modificar la función onEmpresaChange existente para incluir inventario
const originalOnEmpresaChange = window.onEmpresaChange;
window.onEmpresaChange = function() {
    // Llamar función original
    if (originalOnEmpresaChange) {
        originalOnEmpresaChange();
    }

    // Cargar datos de inventario cuando cambie la empresa
    const empresaId = getEmpresaId();
    if (empresaId) {
        console.log('🏢 Empresa cambiada, cargando inventario para:', empresaId);
        loadInventoryData();
    } else {
        showInventoryMessage('Error: Empresa fija no disponible');
    }
};

// Función para confirmar cierre de sesión
function confirmarCerrarSesion() {
  if (confirm('¿Está seguro que desea cerrar sesión?')) {
    window.location.href = '{{ path("admin_logout") }}';
  }
}

// ===== FUNCIONES PARA LOADING INICIAL =====

// Actualizar progreso de carga
function updateLoadingProgress(step, message) {
    // console.log('🔄 Loading step:', step, '-', message);

    // Marcar paso como completado
    loadingSteps[step] = true;

    // Actualizar UI del paso
    const stepElement = document.getElementById(`step-${step}`);
    if (stepElement) {
        stepElement.classList.add('step-completed');
        stepElement.innerHTML = `<i class="fas fa-check-circle" style="margin-right: 0.5rem; font-size: 0.75rem;"></i>${message}`;
    }

    // Calcular progreso total
    const completedSteps = Object.values(loadingSteps).filter(Boolean).length;
    const totalSteps = Object.keys(loadingSteps).length;
    loadingProgress = (completedSteps / totalSteps) * 100;

    // Actualizar barra de progreso
    const progressBar = document.getElementById('loading-progress');
    if (progressBar) {
        progressBar.style.width = loadingProgress + '%';
    }

    // Actualizar mensaje principal
    const loadingMessage = document.getElementById('loading-message');
    if (loadingMessage) {
        loadingMessage.textContent = message;
    }

    // Si todo está completado, ocultar overlay
    if (loadingProgress >= 100) {
        setTimeout(() => {
            hideLoadingOverlay();
        }, 1000);
    }
}

// Ocultar overlay de carga
function hideLoadingOverlay() {
    const overlay = document.getElementById('initial-loading-overlay');
    if (overlay) {
        overlay.classList.add('fade-out');
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 500);
    }
    // console.log('✅ Loading completado - Dashboard listo para usar');
}

// Función para traspasar producto
function traspasarProducto(stockId, modelo, cantidadDisponible) {
  console.log('Traspasar producto:', { stockId, modelo, cantidadDisponible });

  Swal.fire({
    title: 'Traspasar Producto',
    html: `
      <div style="text-align: left; font-size: 1.1rem;">
        <div style="background: var(--do-gray-50); padding: 1rem; border-radius: 8px; margin-bottom: 1.5rem;">
          <p style="margin: 0; font-weight: 600; color: var(--do-gray-800);">
            <i class="fas fa-box" style="color: var(--do-primary-blue); margin-right: 0.5rem;"></i>
            Producto: <span style="color: var(--do-primary-blue);">${modelo}</span>
          </p>
          <p style="margin: 0.5rem 0 0 0; color: var(--do-gray-600);">
            <i class="fas fa-warehouse" style="margin-right: 0.5rem;"></i>
            Stock ID: ${stockId} | Disponible: <strong>${cantidadDisponible}</strong>
          </p>
        </div>

        <div style="margin: 1rem 0;">
          <label for="sucursal-destino" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
            <i class="fas fa-store" style="color: var(--do-success-green); margin-right: 0.5rem;"></i>
            Sucursal de destino:
          </label>
          <div style="position: relative;">
            <input type="text"
                   id="sucursal-destino"
                   class="swal2-input"
                   placeholder="Buscar sucursal..."
                   autocomplete="off"
                   style="width: 100%; padding: 0.75rem; font-size: 1rem; border: 1px solid #ddd; border-radius: 6px;">
            <input type="hidden" id="sucursal-destino-id" value="">
            <div id="sucursal-dropdown"
                 style="position: absolute; top: 100%; left: 0; right: 0; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 6px 6px; max-height: 200px; overflow-y: auto; z-index: 1000; display: none;">
            </div>
          </div>
        </div>

        <div style="margin: 1rem 0;">
          <label for="cantidad-traspasar" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
            <i class="fas fa-sort-numeric-up" style="color: var(--do-warning-amber); margin-right: 0.5rem;"></i>
            Cantidad a traspasar:
          </label>
          <input type="number" id="cantidad-traspasar" class="swal2-input"
                 placeholder="Cantidad..." min="1" max="${cantidadDisponible}" value="1"
                 style="font-size: 1rem;">
          <small style="color: var(--do-gray-500); font-size: 0.9rem;">Máximo disponible: ${cantidadDisponible}</small>
        </div>

        <div style="margin: 1rem 0;">
          <label for="motivo-traspaso" style="display: block; margin-bottom: 0.5rem; font-weight: 600;">
            <i class="fas fa-comment" style="color: var(--do-info); margin-right: 0.5rem;"></i>
            Motivo del traspaso:
          </label>
          <textarea id="motivo-traspaso" class="swal2-textarea"
                    placeholder="Describe el motivo del traspaso..."
                    style="font-size: 1rem; min-height: 80px;"></textarea>
        </div>
      </div>
    `,
    showCancelButton: true,
    confirmButtonText: '<i class="fas fa-exchange-alt"></i> Confirmar Traspaso',
    cancelButtonText: 'Cancelar',
    confirmButtonColor: '#28a745',
    cancelButtonColor: '#6c757d',
    width: '600px',
    backdrop: true,
    allowOutsideClick: false,
    customClass: {
      container: 'swal-traspaso-container'
    },
    didOpen: () => {
      // Aplicar z-index gestionado
      const container = document.querySelector('.swal-traspaso-container');
      if (container) {
        const zIndex = window.modalManager.getSweetAlertZIndex();
        container.style.zIndex = zIndex;
        console.log(`🍭 Modal de traspaso inicial con z-index: ${zIndex}`);
      }

      // Cargar sucursales dinámicamente
      cargarSucursalesDisponibles();
    },
    preConfirm: () => {
      const sucursalDestinoId = document.getElementById('sucursal-destino-id').value;
      const sucursalDestinoNombre = document.getElementById('sucursal-destino').value;
      const cantidad = document.getElementById('cantidad-traspasar').value;
      const motivo = document.getElementById('motivo-traspaso').value;

      if (!sucursalDestinoId || !sucursalDestinoNombre) {
        Swal.showValidationMessage('Por favor seleccione una sucursal de destino válida');
        return false;
      }

      if (!cantidad || cantidad <= 0 || cantidad > cantidadDisponible) {
        Swal.showValidationMessage(`La cantidad debe ser entre 1 y ${cantidadDisponible}`);
        return false;
      }

      if (!motivo.trim()) {
        Swal.showValidationMessage('Por favor ingrese el motivo del traspaso');
        return false;
      }

      return {
        sucursalDestino: sucursalDestinoId,
        sucursalNombre: sucursalDestinoNombre,
        cantidad: parseInt(cantidad),
        motivo: motivo.trim()
      };
    }
  }).then((result) => {
    if (result.isConfirmed) {
      console.log('Datos del traspaso:', {
        stockId: stockId,
        modelo: modelo,
        sucursalDestino: result.value.sucursalDestino,
        cantidad: result.value.cantidad,
        motivo: result.value.motivo
      });

      // Mostrar loading mientras se procesa
      const loadingConfig = window.modalManager.configureSweetAlert({
        title: 'Procesando Traspaso...',
        html: 'Por favor espere mientras se procesa el traspaso',
        allowOutsideClick: false,
        didOpen: () => {
          Swal.showLoading();
          console.log('🔄 Modal de loading mostrado con z-index gestionado');
        }
      });

      Swal.fire(loadingConfig);

      // Preparar datos para el traspaso
      const sucursalSalida = getStockSucursalId(stockId); // Obtener sucursal real del stock
      const productCode = getProductCode(stockId); // Solo para mostrar en logs

      console.log('=== DEBUGGING TRASPASO ===');
      console.log('Stock ID:', stockId);
      console.log('Modelo:', modelo);
      console.log('Cantidad Disponible:', cantidadDisponible);
      console.log('Sucursal Salida:', sucursalSalida);
      console.log('Sucursal Destino:', result.value.sucursalDestino);
      console.log('SKU del producto:', productCode);
      console.log('Cantidad a traspasar:', result.value.cantidad);
      console.log('Motivo:', result.value.motivo);

      // Validaciones antes de enviar
      if (!sucursalSalida) {
        console.error('❌ No se pudo determinar la sucursal donde está el stock');
        Swal.fire({
          title: 'Error',
          text: 'No se pudo determinar la sucursal donde está el stock. Intente de nuevo.',
          icon: 'error'
        });
        return;
      }

      console.log('✅ CORRECCIÓN: Usando sucursal real del stock');
      console.log('✅ El controlador buscará stock', stockId, 'en sucursal', sucursalSalida);

      // Crear FormData para enviar datos como formulario
      const formData = new FormData();
      formData.append('sucursalSalida', sucursalSalida);
      formData.append('sucursalDestino', result.value.sucursalDestino);
      formData.append('razonTraspaso', result.value.motivo);
      formData.append('fechaActual', new Date().toISOString().split('T')[0]);
      formData.append('usuarioActual', getCurrentUserId());

      // CORRECCIÓN: El controlador busca por idstock, no por código de barras
      // Enviamos stockId como "código" para que el controlador lo use en la búsqueda
      formData.append('productos[0][0]', stockId);               // código (usar stockId)
      formData.append('productos[0][1]', stockId);               // idstock (mismo valor)
      formData.append('productos[0][2]', result.value.cantidad); // cantidad

      // Log de FormData
      console.log('FormData entries:');
      for (let [key, value] of formData.entries()) {
        console.log(key + ':', value);
      }

      // Llamada AJAX real al TraspasoController
      fetch('/traspaso/traspasar-productos', {
        method: 'POST',
        headers: {
          'X-Requested-With': 'XMLHttpRequest'
        },
        body: formData
      })
      .then(response => {
        console.log('Response status:', response.status);
        return response.text();
      })
      .then(html => {
        console.log('Response HTML:', html.substring(0, 500) + '...');

        // El controlador retorna HTML, parseamos para ver si fue exitoso
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        // Buscar indicadores de éxito en el HTML retornado
        const successIndicator = tempDiv.querySelector('.alert-success, .success, [class*="success"]');
        const errorIndicator = tempDiv.querySelector('.alert-danger, .error, [class*="error"]');

        // Buscar texto específico de éxito
        const hasSuccessText = html.includes('Traspaso realizado correctamente') ||
                              html.includes('exito') ||
                              html.includes('success');

        console.log('Success indicator:', !!successIndicator);
        console.log('Error indicator:', !!errorIndicator);
        console.log('Has success text:', hasSuccessText);

        if (successIndicator || hasSuccessText) {
          const successConfig = window.modalManager.configureSweetAlert({
            title: '¡Traspaso Exitoso!',
            html: `
              <div style="text-align: center;">
                <i class="fas fa-check-circle" style="color: var(--do-success-green); font-size: 3rem; margin-bottom: 1rem;"></i>
                <p style="font-size: 1.1rem; margin: 0;">
                  Se ha completado el traspaso de <strong>${result.value.cantidad}</strong> unidad(es) del producto
                  <strong>${modelo}</strong> a la sucursal seleccionada.
                </p>
                <p style="font-size: 0.9rem; color: var(--do-gray-600); margin-top: 1rem;">
                  El traspaso está pendiente de aceptación en la sucursal destino.
                </p>
              </div>
            `,
            icon: 'success',
            confirmButtonText: 'Entendido',
            timer: 5000,
            didOpen: () => {
              console.log('✅ Modal de éxito mostrado con z-index gestionado');
            }
          });

          Swal.fire(successConfig).then(() => {
            // Opcional: recargar el modal para actualizar cantidades
            // cerrarModal('modal-detalle-campana');
          });
        } else {
          // Extraer mensaje de error más específico
          let errorMessage = 'Error desconocido en el traspaso';

          if (errorIndicator) {
            errorMessage = errorIndicator.textContent.trim();
          } else if (html.includes('Error al procesar el traspaso:')) {
            const match = html.match(/Error al procesar el traspaso:\s*([^<\n]+)/);
            if (match) {
              errorMessage = match[1].trim();
            }
          } else if (html.includes('Faltan datos requeridos')) {
            errorMessage = 'Faltan datos requeridos para el traspaso. Verifique la sucursal de origen.';
          }

          console.error('Error en traspaso:', errorMessage);
          throw new Error(errorMessage);
        }
      })
      .catch(error => {
        console.error('Error en traspaso:', error);

        const errorConfig = window.modalManager.configureSweetAlert({
          title: 'Error en el Traspaso',
          html: `
            <div style="text-align: center;">
              <i class="fas fa-exclamation-triangle" style="color: var(--vg-error-red); font-size: 3rem; margin-bottom: 1rem;"></i>
              <p style="font-size: 1.1rem; margin: 0;">
                No se pudo completar el traspaso del producto <strong>${modelo}</strong>.
              </p>
              <p style="font-size: 0.9rem; color: var(--do-gray-600); margin-top: 1rem;">
                Error: ${error.message}
              </p>
            </div>
          `,
          icon: 'error',
          confirmButtonText: 'Cerrar',
          didOpen: () => {
            console.log('❌ Modal de error mostrado con z-index gestionado');
          }
        });

        Swal.fire(errorConfig);
      });
    }
  });
}

// ========================================
// SISTEMA DE GESTIÓN DE MODALES Y Z-INDEX
// ========================================

class ModalManager {
  constructor() {
    this.baseZIndex = 9999;
    this.currentZIndex = this.baseZIndex;
    this.modalStack = [];
    this.sweetAlertInstances = [];
  }

  // Obtener el siguiente z-index disponible
  getNextZIndex() {
    this.currentZIndex += 100;
    return this.currentZIndex;
  }

  // Registrar un modal en el stack
  registerModal(modalId, type = 'custom') {
    const zIndex = this.getNextZIndex();
    const modalInfo = {
      id: modalId,
      type: type,
      zIndex: zIndex,
      timestamp: Date.now()
    };

    this.modalStack.push(modalInfo);
    console.log(`📋 Modal registrado: ${modalId} (z-index: ${zIndex}, tipo: ${type})`);
    return zIndex;
  }

  // Desregistrar un modal del stack
  unregisterModal(modalId) {
    const index = this.modalStack.findIndex(modal => modal.id === modalId);
    if (index !== -1) {
      const removed = this.modalStack.splice(index, 1)[0];
      console.log(`📋 Modal desregistrado: ${modalId} (z-index: ${removed.zIndex})`);

      // Ajustar currentZIndex si era el último modal
      if (this.modalStack.length === 0) {
        this.currentZIndex = this.baseZIndex;
      }
    }
  }

  // Obtener z-index para SweetAlert
  getSweetAlertZIndex() {
    const zIndex = this.getNextZIndex();
    console.log(`🍭 SweetAlert z-index asignado: ${zIndex}`);
    return zIndex;
  }

  // Configurar SweetAlert con z-index correcto
  configureSweetAlert(options = {}) {
    const zIndex = this.getSweetAlertZIndex();

    const defaultConfig = {
      customClass: {
        container: `swal-container-${Date.now()}`,
        popup: 'swal-popup-managed'
      },
      didOpen: () => {
        // Aplicar z-index al contenedor de SweetAlert
        const container = document.querySelector(`.${options.customClass?.container || defaultConfig.customClass.container}`);
        if (container) {
          container.style.zIndex = zIndex;
          console.log(`🍭 SweetAlert z-index aplicado: ${zIndex}`);
        }

        // Ejecutar callback original si existe
        if (options.didOpen) {
          options.didOpen();
        }
      },
      willClose: () => {
        // Ejecutar callback original si existe
        if (options.willClose) {
          options.willClose();
        }
      }
    };

    // Combinar configuraciones
    return {
      ...options,
      customClass: {
        ...defaultConfig.customClass,
        ...options.customClass
      },
      didOpen: defaultConfig.didOpen,
      willClose: defaultConfig.willClose
    };
  }

  // Obtener información del stack actual
  getStackInfo() {
    return {
      count: this.modalStack.length,
      currentZIndex: this.currentZIndex,
      stack: [...this.modalStack]
    };
  }
}

// Instancia global del gestor de modales
window.modalManager = new ModalManager();

// Función de debugging para monitorear el estado de modales
window.debugModals = function() {
  const info = window.modalManager.getStackInfo();
  console.log('🔍 ESTADO ACTUAL DE MODALES:');
  console.log(`   📊 Total de modales: ${info.count}`);
  console.log(`   🔢 Z-index actual: ${info.currentZIndex}`);
  console.log('   📋 Stack de modales:', info.stack);
  return info;
};

// Funciones auxiliares para el traspaso
function getCurrentSucursalId() {
  console.log('=== FUNCIÓN OBSOLETA: getCurrentSucursalId() ===');
  console.warn('⚠️ Esta función ya no se usa. Ahora obtenemos la sucursal directamente del stock.');
  return null;
}

// Nueva función para obtener la sucursal real donde está el stock
function getStockSucursalId(stockId) {
  console.log('=== OBTENIENDO SUCURSAL REAL DEL STOCK ===');
  console.log('Stock ID:', stockId);

  // Buscar el botón específico con el stockId
  const button = document.querySelector(`button[data-stock-id="${stockId}"]`);
  if (button) {
    const sucursalId = button.getAttribute('data-sucursal-id');
    if (sucursalId) {
      console.log('✅ Sucursal real del stock encontrada:', sucursalId);
      return sucursalId;
    }
  }

  // Fallback: buscar en la tabla del modal
  const modalContent = document.querySelector('#detalle-campana-contenido');
  if (modalContent) {
    const table = modalContent.querySelector('table');
    if (table) {
      const rows = table.querySelectorAll('tbody tr');

      for (let row of rows) {
        const buttonInRow = row.querySelector(`button[onclick*="traspasarProducto(${stockId}"]`);
        if (buttonInRow) {
          console.log('Fila encontrada para stockId:', stockId);

          // Obtener sucursal desde el atributo data del botón
          const sucursalId = buttonInRow.getAttribute('data-sucursal-id');
          if (sucursalId) {
            console.log('✅ Sucursal del stock encontrada en tabla:', sucursalId);
            return sucursalId;
          }

          break;
        }
      }
    }
  }

  console.error('❌ No se pudo determinar la sucursal del stock:', stockId);
  return null;
}

function getCurrentUserId() {
  // Obtener el ID del usuario actual
  // Esto normalmente vendría del contexto de Symfony/Twig
  if (window.currentUserId) {
    return window.currentUserId;
  }

  // Fallback: obtener desde un elemento hidden si existe
  const userIdInput = document.querySelector('input[name="current_user_id"]');
  if (userIdInput) {
    return userIdInput.value;
  }

  return 'current_user'; // Placeholder
}

function getProductCode(stockId) {
  console.log('Buscando código para stockId:', stockId);

  // Buscar el botón específico con el stockId
  const button = document.querySelector(`button[data-stock-id="${stockId}"]`);
  if (button) {
    // Prioridad 1: SKU del stock (codigobarras de la tabla stock)
    const sku = button.getAttribute('data-sku');
    if (sku && sku.trim() !== '' && sku !== stockId.toString()) {
      console.log('✅ SKU del stock encontrado:', sku);
      return sku;
    }

    // Prioridad 2: UPC del producto (codigobarrasuniversal de la tabla producto)
    const upc = button.getAttribute('data-upc');
    if (upc && upc.trim() !== '') {
      console.log('✅ UPC del producto encontrado:', upc);
      return upc;
    }
  }

  // Fallback: buscar en la tabla del modal
  const modalContent = document.querySelector('#detalle-campana-contenido');
  if (modalContent) {
    const table = modalContent.querySelector('table');
    if (table) {
      const rows = table.querySelectorAll('tbody tr');

      for (let row of rows) {
        const buttonInRow = row.querySelector(`button[onclick*="traspasarProducto(${stockId}"]`);
        if (buttonInRow) {
          console.log('Fila encontrada para stockId:', stockId);

          // Buscar códigos en las celdas de la tabla (columnas SKU y UPC)
          const cells = row.querySelectorAll('td code');
          for (let cell of cells) {
            const code = cell.textContent.trim();
            if (code && code !== 'Sin SKU' && code !== 'Sin UPC' && code !== stockId.toString()) {
              console.log('✅ Código encontrado en tabla:', code);
              return code;
            }
          }

          break;
        }
      }
    }
  }

  // Si no encontramos código específico, usar stockId
  console.warn('⚠️ No se encontró código específico, usando stockId:', stockId);
  return stockId.toString();
}

// Variables globales para el autocomplete
let sucursalesData = [];
let autocompleteTimeout = null;

// Función para cargar sucursales disponibles e inicializar autocomplete
function cargarSucursalesDisponibles() {
  const input = document.getElementById('sucursal-destino');
  const dropdown = document.getElementById('sucursal-dropdown');
  const hiddenInput = document.getElementById('sucursal-destino-id');

  if (!input || !dropdown || !hiddenInput) return;

  // Cargar datos de sucursales
  fetch('/traspaso/sucursales-disponibles', {
    method: 'GET',
    headers: {
      'X-Requested-With': 'XMLHttpRequest'
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success && data.sucursales) {
      sucursalesData = data.sucursales;
      setupAutocomplete(input, dropdown, hiddenInput);
    } else {
      input.placeholder = 'Error al cargar sucursales';
      input.disabled = true;
    }
  })
  .catch(error => {
    console.error('Error cargando sucursales:', error);
    input.placeholder = 'Error al cargar sucursales';
    input.disabled = true;
  });
}

// Configurar funcionalidad de autocomplete
function setupAutocomplete(input, dropdown, hiddenInput) {
  // Evento de input para filtrar
  input.addEventListener('input', function() {
    clearTimeout(autocompleteTimeout);
    autocompleteTimeout = setTimeout(() => {
      const query = this.value.toLowerCase().trim();

      if (query.length === 0) {
        dropdown.style.display = 'none';
        hiddenInput.value = '';
        return;
      }

      // Filtrar sucursales
      const filtered = sucursalesData.filter(sucursal =>
        sucursal.nombre.toLowerCase().includes(query)
      );

      showDropdownOptions(dropdown, filtered, input, hiddenInput);
    }, 300);
  });

  // Evento de focus para mostrar todas las opciones
  input.addEventListener('focus', function() {
    if (sucursalesData.length > 0) {
      showDropdownOptions(dropdown, sucursalesData, input, hiddenInput);
    }
  });

  // Cerrar dropdown al hacer click fuera
  document.addEventListener('click', function(e) {
    if (!input.contains(e.target) && !dropdown.contains(e.target)) {
      dropdown.style.display = 'none';
    }
  });

  // Navegación con teclado
  input.addEventListener('keydown', function(e) {
    const items = dropdown.querySelectorAll('.autocomplete-item');
    const activeItem = dropdown.querySelector('.autocomplete-item.active');

    if (e.key === 'ArrowDown') {
      e.preventDefault();
      if (activeItem) {
        activeItem.classList.remove('active');
        const next = activeItem.nextElementSibling;
        if (next) {
          next.classList.add('active');
        } else {
          items[0]?.classList.add('active');
        }
      } else {
        items[0]?.classList.add('active');
      }
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      if (activeItem) {
        activeItem.classList.remove('active');
        const prev = activeItem.previousElementSibling;
        if (prev) {
          prev.classList.add('active');
        } else {
          items[items.length - 1]?.classList.add('active');
        }
      } else {
        items[items.length - 1]?.classList.add('active');
      }
    } else if (e.key === 'Enter') {
      e.preventDefault();
      if (activeItem) {
        activeItem.click();
      }
    } else if (e.key === 'Escape') {
      dropdown.style.display = 'none';
    }
  });
}

// Mostrar opciones en el dropdown
function showDropdownOptions(dropdown, sucursales, input, hiddenInput) {
  dropdown.innerHTML = '';

  if (sucursales.length === 0) {
    dropdown.innerHTML = '<div style="padding: 0.75rem; color: #666; font-style: italic;">No se encontraron sucursales</div>';
    dropdown.style.display = 'block';
    return;
  }

  sucursales.forEach((sucursal, index) => {
    const item = document.createElement('div');
    item.className = 'autocomplete-item';
    item.style.cssText = `
      padding: 0.75rem;
      cursor: pointer;
      border-bottom: 1px solid #eee;
      transition: background-color 0.2s ease;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    `;

    item.innerHTML = `
      <i class="fas fa-store" style="color: var(--do-primary-blue); font-size: 0.9rem;"></i>
      <span style="font-weight: 500;">${sucursal.nombre}</span>
      <small style="color: #666; margin-left: auto;">ID: ${sucursal.idsucursal}</small>
    `;

    // Eventos hover
    item.addEventListener('mouseenter', function() {
      dropdown.querySelectorAll('.autocomplete-item').forEach(i => i.classList.remove('active'));
      this.classList.add('active');
    });

    item.addEventListener('mouseleave', function() {
      this.classList.remove('active');
    });

    // Evento click
    item.addEventListener('click', function() {
      input.value = sucursal.nombre;
      hiddenInput.value = sucursal.idsucursal;
      dropdown.style.display = 'none';

      // Trigger change event
      input.dispatchEvent(new Event('change'));
    });

    dropdown.appendChild(item);
  });

  dropdown.style.display = 'block';
}
</script>

<style>
/* ========================================
   ESTILOS PARA SISTEMA DE GESTIÓN DE MODALES
   ======================================== */

/* Estilos base para modales gestionados */
.swal2-container.swal-traspaso-container {
  /* z-index será aplicado dinámicamente por ModalManager */
}

.swal2-container.swal-traspaso-container .swal2-popup {
  /* z-index será heredado del contenedor */
}

/* Estilos para SweetAlert gestionados */
.swal-popup-managed {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3) !important;
  border: none !important;
}

/* Backdrop mejorado para modales superpuestos */
.swal2-container[style*="z-index"] .swal2-backdrop-show {
  background: rgba(0, 0, 0, 0.6) !important;
}

/* Asegurar que los contenedores de SweetAlert respeten el z-index */
.swal2-container {
  /* z-index será aplicado inline por ModalManager cuando sea necesario */
}

/* Animaciones suaves para modales superpuestos */
.swal2-container .swal2-popup {
  animation-duration: 0.3s !important;
}

/* Estilos específicos para diferentes tipos de modal */
.swal2-container[class*="swal-container-"] .swal2-popup {
  /* Estilos para modales con timestamp en la clase */
}

/* Estilos para el autocomplete de sucursales */
.autocomplete-item {
  transition: all 0.2s ease !important;
}

.autocomplete-item:hover,
.autocomplete-item.active {
  background-color: var(--do-primary-blue) !important;
  color: white !important;
}

.autocomplete-item:hover i,
.autocomplete-item.active i {
  color: white !important;
}

.autocomplete-item:hover small,
.autocomplete-item.active small {
  color: rgba(255, 255, 255, 0.8) !important;
}

.autocomplete-item:last-child {
  border-bottom: none !important;
}

/* Mejorar el input del autocomplete */
#sucursal-destino:focus {
  border-color: var(--do-primary-blue) !important;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1) !important;
  outline: none !important;
}

/* Scrollbar personalizado para el dropdown */
#sucursal-dropdown::-webkit-scrollbar {
  width: 6px;
}

#sucursal-dropdown::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

#sucursal-dropdown::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

#sucursal-dropdown::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>

{% endblock %}