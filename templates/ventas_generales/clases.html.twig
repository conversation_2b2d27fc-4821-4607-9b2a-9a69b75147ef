<!-- Header con switch alineado -->
<div class="card-header bg-light d-flex justify-content-between align-items-center">
  <div class="form-check form-switch m-0">
    <input class="form-check-input" type="checkbox" id="todasClases" onclick="toggleClases(this)" checked>
    <label class="form-check-label small" for="todasClases">Todas</label>
  </div>
</div>

<!-- Cuerpo con scroll y checkboxes -->
<div class="card-body p-3" style="max-height: 260px; overflow-y: auto;">
  <fieldset id="idclase" class="row row-cols-2 g-1">
    {% for clase in clases %}
    <div class="col">
      <div class="form-check">
        <input type="checkbox" class="form-check-input" id="checkbox-clase-{{ clase.idclase }}" name="clase" value="{{ clase.idclase }}" checked>
        <label class="form-check-label" for="checkbox-clase-{{ clase.idclase }}">{{ clase.nombre }}</label>
      </div>
    </div>
    {% endfor %}
  </fieldset>
</div>

<script>
function toggleClases(source) {
  const checkboxes = document.querySelectorAll('input[name="clase"]');
  checkboxes.forEach(cb => cb.checked = source.checked);
}
</script>