<!-- Header con switch alineado -->
<div class="card-header bg-light d-flex justify-content-between align-items-center">
  <div class="form-check form-switch m-0">
    <input class="form-check-input" type="checkbox" id="todasSucursales" onclick="toggleSucursales(this)" checked>
    <label class="form-check-label small" for="todasSucursales">Todas</label>
  </div>
</div>

<!-- C<PERSON>po con scroll y checkboxes -->
<div class="card-body p-3" style="max-height: 260px; overflow-y: auto;">
  <fieldset id="idsucursal" class="row row-cols-2 g-1">
    {% for sucursal in sucursales %}
    <div class="col">
      <div class="form-check">
        <input type="checkbox" class="form-check-input" id="checkbox-sucursal-{{ sucursal.idsucursal }}" name="sucursal" value="{{ sucursal.idsucursal }}" checked onchange="actualizarContadorSucursales()">
        <label class="form-check-label" for="checkbox-sucursal-{{ sucursal.idsucursal }}">{{ sucursal.nombre }}</label>
      </div>
    </div>
    {% endfor %}
  </fieldset>
</div>

<script>
function toggleSucursales(source) {
  const checkboxes = document.querySelectorAll('input[name="sucursal"]');
  checkboxes.forEach(cb => cb.checked = source.checked);
  actualizarContadorSucursales();
}
</script>