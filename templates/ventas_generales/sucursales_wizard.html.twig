{% if error is defined %}
    <div style="text-align: center; padding: 1rem; color: var(--error-red);">
        <i class="fas fa-exclamation-triangle"></i>
        {{ error }}
    </div>
{% else %}
    {% if sucursalesPorTipo is defined and sucursalesPorTipo|length > 0 %}
        <div class="sucursales-selector-tipos">
            <!-- Header con acciones rápidas -->
            <div class="quick-actions" style="margin-bottom: 1rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button type="button" onclick="selectAllSucursales()" style="padding: 0.75rem 1rem; background: #10B981; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#059669'" onmouseout="this.style.background='#10B981'">Todas</button>
                <button type="button" onclick="selectNoneSucursales()" style="padding: 0.75rem 1rem; background: #EF4444; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#DC2626'" onmouseout="this.style.background='#EF4444'">Ninguna</button>
                {% if sucursalesPorTipo['sucursal'] is defined %}
                    <button type="button" onclick="selectByType('sucursal')" style="padding: 0.75rem 1rem; background: #3B82F6; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#2563EB'" onmouseout="this.style.background='#3B82F6'">Solo Sucursales</button>
                {% endif %}
                {% if sucursalesPorTipo['bodega'] is defined %}
                    <button type="button" onclick="selectByType('bodega')" style="padding: 0.75rem 1rem; background: #F59E0B; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#D97706'" onmouseout="this.style.background='#F59E0B'">Solo Bodegas</button>
                {% endif %}
                {% if sucursalesPorTipo['campaña'] is defined %}
                    <button type="button" onclick="selectByType('campaña')" style="padding: 0.75rem 1rem; background: #8B5CF6; color: white; border: none; border-radius: 6px; font-size: 0.875rem; cursor: pointer; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#7C3AED'" onmouseout="this.style.background='#8B5CF6'">Solo Campañas</button>
                {% endif %}
            </div>

            <!-- Cards por tipo -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); gap: 1rem; margin-bottom: 1rem;">
                {% for tipo, sucursales in sucursalesPorTipo %}
                    <div class="tipo-card" data-tipo="{{ tipo }}" style="background: var(--white); border: 2px solid var(--gray-200); border-radius: 12px; padding: 1rem; transition: all 0.3s ease;">
                        <div class="tipo-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 0.75rem;">
                            <h4 style="margin: 0; font-size: 1rem; font-weight: 600; color: var(--gray-900);">
                                {% if tipo == 'sucursal' %}🏪 SUCURSALES
                                {% elseif tipo == 'bodega' %}📦 BODEGAS
                                {% elseif tipo == 'campaña' %}🎯 CAMPAÑAS
                                {% else %}📍 {{ tipo|upper }}
                                {% endif %}
                            </h4>
                            <label style="display: flex; align-items: center; gap: 0.5rem; cursor: pointer;">
                                <input type="checkbox"
                                       id="tipo-{{ tipo }}"
                                       checked
                                       onchange="toggleTipo('{{ tipo }}')"
                                       data-count="{{ sucursales|length }}"
                                       style="width: 18px; height: 18px;">
                                <span style="font-size: 0.875rem; font-weight: 500; color: var(--gray-700);">Todas ({{ sucursales|length }})</span>
                            </label>
                        </div>

                        <div class="tipo-status" style="margin-bottom: 0.75rem;">
                            <span id="status-{{ tipo }}" style="font-size: 0.875rem; font-weight: 500;">{{ sucursales|length }}/{{ sucursales|length }} ✅</span>
                        </div>

                        <div class="tipo-actions" style="display: flex; gap: 0.5rem; margin-bottom: 0.75rem;">
                            <button type="button" onclick="toggleTipoDetails('{{ tipo }}')" style="flex: 1; padding: 0.75rem; background: #F3F4F6; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 0.875rem; cursor: pointer; color: #374151; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#E5E7EB'" onmouseout="this.style.background='#F3F4F6'">
                                Ver lista
                            </button>
                            <button type="button" onclick="searchInTipo('{{ tipo }}')" style="flex: 1; padding: 0.75rem; background: #F3F4F6; border: 1px solid #D1D5DB; border-radius: 6px; font-size: 0.875rem; cursor: pointer; color: #374151; font-weight: 500; transition: all 0.2s ease;" onmouseover="this.style.background='#E5E7EB'" onmouseout="this.style.background='#F3F4F6'">
                                Buscar
                            </button>
                        </div>

                        <!-- Lista detallada (oculta por defecto) -->
                        <div id="details-{{ tipo }}" class="tipo-details" style="display: none; max-height: 200px; overflow-y: auto; border: 1px solid var(--gray-200); border-radius: 6px; padding: 0.5rem;">
                            {% for sucursal in sucursales %}
                                <label style="display: flex; align-items: center; gap: 0.5rem; padding: 0.25rem; cursor: pointer; border-radius: 4px;" onmouseover="this.style.background='var(--gray-50)'" onmouseout="this.style.background='transparent'">
                                    <input type="checkbox"
                                           name="sucursal"
                                           value="{{ sucursal.idsucursal }}"
                                           data-tipo="{{ tipo }}"
                                           checked
                                           onchange="updateTipoCounter('{{ tipo }}')"
                                           style="width: 16px; height: 16px;">
                                    <span style="font-size: 1rem; color: var(--gray-800); font-weight: 500;">{{ sucursal.nombre }}</span>
                                </label>
                            {% endfor %}
                        </div>
                    </div>
                {% endfor %}
            </div>

            <!-- Búsqueda global -->
            <div class="global-search" style="margin-top: 1rem;">
                <input type="text"
                       id="search-global"
                       placeholder="Buscar por nombre en todas las ubicaciones..."
                       onkeyup="searchGlobal(this.value)"
                       style="width: 100%; padding: 1rem; border: 2px solid var(--gray-300); border-radius: 8px; font-size: 1rem;">
                <div id="search-results" style="display: none; margin-top: 0.5rem; background: var(--white); border: 1px solid var(--gray-200); border-radius: 6px; max-height: 200px; overflow-y: auto;"></div>
            </div>

            <!-- Resumen final -->
            <div style="margin-top: 1rem; padding: 0.75rem; background: var(--info-light); border-radius: 8px; border-left: 4px solid var(--info);">
                <p style="margin: 0; font-size: 1rem; color: var(--info-dark);">
                    <i class="fas fa-info-circle"></i>
                    <strong>{{ totalSucursales }}</strong> ubicaciones encontradas en total.
                    {% for tipo, cantidad in contadorTipos %}
                        <span style="margin-left: 1rem;">
                            {% if tipo == 'sucursal' %}🏪
                            {% elseif tipo == 'bodega' %}📦
                            {% elseif tipo == 'campaña' %}🎯
                            {% else %}📍
                            {% endif %}
                            {{ cantidad }} {{ tipo }}{{ cantidad != 1 ? 's' : '' }}
                        </span>
                    {% endfor %}
                </p>
            </div>
        </div>
    {% else %}
        <div style="text-align: center; padding: 2rem; color: var(--gray-500);">
            <i class="fas fa-store-slash fa-2x" style="margin-bottom: 1rem; color: var(--gray-300);"></i>
            <h4 style="margin: 0 0 0.5rem 0; color: var(--gray-600);">No hay ubicaciones</h4>
            <p style="margin: 0; font-size: 0.875rem;">Esta empresa no tiene sucursales, bodegas o campañas activas configuradas.</p>
        </div>
    {% endif %}
{% endif %}
