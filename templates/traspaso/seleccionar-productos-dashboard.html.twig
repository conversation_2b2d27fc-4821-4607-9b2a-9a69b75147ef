{% extends 'base.html.twig' %}

{% block title %}Centro de Traspasos{% endblock %}

{% block stylesheets %}
{{ parent() }}
<style>
    .dashboard-container {
        background: linear-gradient(135deg, #f8f9fa 0%, #f8f9fa 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .main-card {
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
    }
    
    .header-section {
        background: linear-gradient(135deg, #f8f9fa 0%, #f8f9fa 100%);
        color: white;
        padding: 40px;
        text-align: center;
    }
    
    .method-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 30px;
        padding: 40px;
    }
    
    .method-option {
        background: white;
        border-radius: 15px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 3px solid transparent;
    }
    
    .method-option:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        border-color: #667eea;
    }
    
    .method-option.active {
        border-color: #667eea;
        background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
    }
    
    .method-icon {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        color: white;
    }
    
    .difficulty-badge {
        display: inline-block;
        padding: 5px 15px;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: bold;
        margin: 5px;
    }
    
    .difficulty-easy { background: #d4edda; color: #155724; }
    .difficulty-medium { background: #fff3cd; color: #856404; }
    .difficulty-advanced { background: #f8d7da; color: #721c24; }
    
    .google-sheets-panel {
        background: #f8f9fa;
        border-radius: 15px;
        padding: 30px;
        margin-top: 30px;
        display: none;
    }
    
    .google-sheets-panel.active {
        display: block;
        animation: slideDown 0.3s ease;
    }
    
    @keyframes slideDown {
        from { opacity: 0; transform: translateY(-20px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    .format-guide {
        background: white;
        border-radius: 10px;
        padding: 20px;
        margin: 20px 0;
        border-left: 5px solid #667eea;
    }
    
    .example-table {
        background: white;
        border-radius: 10px;
        overflow: hidden;
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .auth-section {
        background: white;
        border-radius: 10px;
        padding: 30px;
        text-align: center;
        margin: 20px 0;
    }
    
    .google-btn {
        background: #4285f4;
        color: white;
        border: none;
        padding: 15px 30px;
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: bold;
        transition: all 0.3s;
        box-shadow: 0 5px 15px rgba(66, 133, 244, 0.3);
    }
    
    .google-btn:hover {
        background: #3367d6;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(66, 133, 244, 0.4);
        color: white;
    }
    
    .progress-indicator {
        background: linear-gradient(90deg, #f8f9fa 0%, #f8f9fa 100%);
        height: 4px;
        border-radius: 2px;
        margin: 20px 0;
        overflow: hidden;
    }
    
    .progress-bar {
        height: 100%;
        background: rgba(255,255,255,0.8);
        width: 0%;
        transition: width 0.3s ease;
    }
    
    .status-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #f8f9fa 100%);
        color: white;
        border-radius: 15px;
        padding: 20px;
        margin: 20px 0;
        text-align: center;
    }
    
    .floating-help {
        position: fixed;
        bottom: 30px;
        right: 30px;
        width: 60px;
        height: 60px;
        background: #667eea;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
        transition: all 0.3s;
        z-index: 1000;
    }
    
    .floating-help:hover {
        transform: scale(1.1);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }
</style>
{% endblock %}

{% block body %}
<div class="dashboard-container">
    <div class="container">
        <div class="main-card">
            <!-- Header -->
            <div class="header-section">
                <h1><i class="fas fa-exchange-alt"></i> Centro de Traspasos</h1>
                <p class="lead">Transfiere productos entre sucursales de manera fácil y eficiente</p>
                <div class="progress-indicator">
                    <div class="progress-bar" id="main-progress"></div>
                </div>
            </div>
            
            <!-- Métodos de Traspaso -->
            <div class="method-grid">
                <!-- Método Manual -->
                <div class="method-option" onclick="selectMethod('manual')" data-method="manual">
                    <div class="method-icon">
                        <i class="fas fa-barcode"></i>
                    </div>
                    <h3>Escaneo Manual</h3>
                    <p class="text-muted">Escanea códigos de barras o ingresa SKUs uno por uno. Perfecto para traspasos pequeños y control detallado.</p>
                    <div class="mt-3">
                        <span class="difficulty-badge difficulty-easy">Fácil</span>
                        <span class="difficulty-badge difficulty-easy">1-50 productos</span>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-clock"></i> ~2 min por producto
                        </small>
                    </div>
                </div>
                
                <!-- Método Google Sheets -->
                <div class="method-option" onclick="selectMethod('sheets')" data-method="sheets">
                    <div class="method-icon">
                        <i class="fab fa-google"></i>
                    </div>
                    <h3>Google Sheets</h3>
                    <p class="text-muted">Carga masiva desde Google Drive. Ideal para traspasos complejos con múltiples sucursales de origen y destino.</p>
                    <div class="mt-3">
                        <span class="difficulty-badge difficulty-medium">Intermedio</span>
                        <span class="difficulty-badge difficulty-advanced">50+ productos</span>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-rocket"></i> Procesamiento masivo
                        </small>
                    </div>
                </div>
                
                <!-- Método Excel -->
                <div class="method-option" onclick="selectMethod('excel')" data-method="excel">
                    <div class="method-icon">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <h3>Archivo Excel</h3>
                    <p class="text-muted">Sube un archivo Excel desde tu computadora. Transferencia a sucursal fija predefinida.</p>
                    <div class="mt-3">
                        <span class="difficulty-badge difficulty-medium">Intermedio</span>
                        <span class="difficulty-badge difficulty-medium">Sucursal fija</span>
                    </div>
                    <div class="mt-3">
                        <small class="text-muted">
                            <i class="fas fa-upload"></i> Carga local
                        </small>
                    </div>
                </div>
            </div>
            
            <!-- Panel de Google Sheets -->
            <div class="google-sheets-panel" id="sheets-panel">
                <div class="row">
                    <div class="col-md-6">
                        <div class="format-guide">
                            <h4><i class="fas fa-table text-primary"></i> Formato de Archivo</h4>
                            <p>Tu archivo de Google Sheets debe tener exactamente estas columnas:</p>
                            
                            <div class="example-table">
                                <table class="table table-sm mb-0">
                                    <thead style="background: #667eea; color: white;">
                                        <tr>
                                            <th>A: SKU</th>
                                            <th>B: Cantidad</th>
                                            <th>C: Sucursal Origen</th>
                                            <th>D: Sucursal Destino</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td><code>ABC123</code></td>
                                            <td><span class="badge badge-info">2</span></td>
                                            <td><span class="badge badge-warning">1</span></td>
                                            <td><span class="badge badge-success">5</span></td>
                                        </tr>
                                        <tr>
                                            <td><code>DEF456</code></td>
                                            <td><span class="badge badge-info">1</span></td>
                                            <td><span class="badge badge-warning">3</span></td>
                                            <td><span class="badge badge-success">7</span></td>
                                        </tr>
                                        <tr>
                                            <td><code>GHI789</code></td>
                                            <td><span class="badge badge-info">5</span></td>
                                            <td><span class="badge badge-warning">2</span></td>
                                            <td><span class="badge badge-success">1</span></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <div class="alert alert-info mt-3">
                                <i class="fas fa-lightbulb"></i>
                                <strong>Tip:</strong> Los números de sucursal deben corresponder a IDs válidos en el sistema.
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="auth-section">
                            <h4><i class="fab fa-google text-primary"></i> Conectar con Google</h4>
                            <p>Autoriza el acceso a tu Google Drive para seleccionar el archivo</p>
                            
                            <div class="mb-3">
                                <button id="authorize_button" class="google-btn d-none" onclick="handleAuthClick()">
                                    <i class="fab fa-google"></i> Conectar con Google
                                </button>
                                <button id="signout_button" class="btn btn-outline-danger d-none" onclick="handleSignoutClick()">
                                    <i class="fas fa-sign-out-alt"></i> Desconectar
                                </button>
                            </div>
                            
                            <button id="picker_button" class="btn btn-success btn-lg d-none" onclick="createPicker()">
                                <i class="fas fa-folder-open"></i> Buscar en Google Drive
                            </button>
                            
                            <div class="status-card d-none" id="processing-status">
                                <div class="spinner-border text-white mb-2" role="status"></div>
                                <h5>Procesando archivo...</h5>
                                <p class="mb-0">Validando productos y sucursales</p>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <a href="https://docs.google.com/spreadsheets/d/12p_cGGJUnvTamshLPEBlxNLfroCLhS0EL-S0SNzRB9U/edit?usp=sharing" 
                               target="_blank" class="btn btn-outline-primary">
                                <i class="fas fa-download"></i> Descargar Plantilla
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Ayuda Flotante -->
<div class="floating-help" onclick="showHelp()">
    <i class="fas fa-question"></i>
</div>

<!-- Modal de Ayuda -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                <h5 class="modal-title"><i class="fas fa-question-circle"></i> Guía de Traspasos</h5>
                <button type="button" class="close text-white" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="fas fa-barcode text-success"></i> Escaneo Manual</h6>
                        <ul class="list-unstyled">
                            <li>• Ideal para 1-50 productos</li>
                            <li>• Control producto por producto</li>
                            <li>• Una sucursal origen/destino</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fab fa-google text-primary"></i> Google Sheets</h6>
                        <ul class="list-unstyled">
                            <li>• Ideal para 50+ productos</li>
                            <li>• Múltiples sucursales</li>
                            <li>• Requiere formato específico</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="fas fa-file-excel text-warning"></i> Archivo Excel</h6>
                        <ul class="list-unstyled">
                            <li>• Carga desde computadora</li>
                            <li>• Sucursal destino fija</li>
                            <li>• Formato simple</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
let selectedMethod = null;

function selectMethod(method) {
    selectedMethod = method;
    
    // Remover selección anterior
    document.querySelectorAll('.method-option').forEach(option => {
        option.classList.remove('active');
    });
    
    // Seleccionar método actual
    document.querySelector(`[data-method="${method}"]`).classList.add('active');
    
    // Actualizar progreso
    updateProgress(33);
    
    // Mostrar panel específico o redirigir
    if (method === 'sheets') {
        document.getElementById('sheets-panel').classList.add('active');
        updateProgress(66);
    } else if (method === 'manual') {
        setTimeout(() => {
            window.location.href = "{{ path('seleccionar-productos') }}";
        }, 500);
    } else if (method === 'excel') {
        setTimeout(() => {
            window.location.href = "{{ path('traspaso_masivo_excel') }}";
        }, 500);
    }
}

function updateProgress(percentage) {
    document.getElementById('main-progress').style.width = percentage + '%';
}

function showHelp() {
    $('#helpModal').modal('show');
}

// Integración con Google Sheets API
// ... (código existente de Google API)

// Mostrar estado de procesamiento
function showProcessingStatus() {
    document.getElementById('processing-status').classList.remove('d-none');
    updateProgress(100);
}

function hideProcessingStatus() {
    document.getElementById('processing-status').classList.add('d-none');
}
</script>
{% endblock %}
