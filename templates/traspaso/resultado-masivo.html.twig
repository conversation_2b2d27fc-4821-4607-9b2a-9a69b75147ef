{% extends 'admin/layout.html.twig' %}

{% block title %}Resultado Traspaso Masivo{% endblock %}

{% block body %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-exchange-alt"></i>
                        Resultado del Traspaso Masivo
                    </h3>
                </div>
                <div class="card-body">
                    {% for label, messages in app.flashes %}
                        {% for message in messages %}
                            <div class="alert alert-{{ label }}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    {% endfor %}

                    {% if resultado.exito %}
                        <div class="alert alert-success">
                            <h4><i class="icon fa fa-check"></i> ¡Traspaso Masivo Completado!</h4>
                            <p>{{ resultado.mensaje }}</p>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-success"><i class="fas fa-boxes"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Total Productos</span>
                                        <span class="info-box-number">{{ resultado.total_productos }}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="info-box">
                                    <span class="info-box-icon bg-info"><i class="fas fa-exchange-alt"></i></span>
                                    <div class="info-box-content">
                                        <span class="info-box-text">Traspasos Creados</span>
                                        <span class="info-box-number">{{ resultado.total_traspasos }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        {% if resultado.traspasos_creados %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4>Traspasos Creados</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>ID Traspaso</th>
                                                    <th>Sucursal Origen</th>
                                                    <th>Sucursal Destino</th>
                                                    <th>Fecha Creación</th>
                                                    <th>Estado</th>
                                                    <th>Acciones</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for traspaso in resultado.traspasos_creados %}
                                                    <tr>
                                                        <td>{{ traspaso.idtranspasoalmacen }}</td>
                                                        <td>{{ traspaso.sucursalIdsucursalorigen.nombre }}</td>
                                                        <td>{{ traspaso.sucursalIdsucursaldestino.nombre }}</td>
                                                        <td>{{ traspaso.creacion|date('d/m/Y H:i') }}</td>
                                                        <td>
                                                            {% if traspaso.estado == '0' %}
                                                                <span class="badge badge-warning">Pendiente</span>
                                                            {% elseif traspaso.estado == '1' %}
                                                                <span class="badge badge-success">Completado</span>
                                                            {% else %}
                                                                <span class="badge badge-secondary">{{ traspaso.estado }}</span>
                                                            {% endif %}
                                                        </td>
                                                        <td>
                                                            <a href="{{ path('admin_app_transpasoalmacen_show', {'id': traspaso.idtranspasoalmacen}) }}" 
                                                               class="btn btn-sm btn-info" target="_blank">
                                                                <i class="fas fa-eye"></i> Ver Detalles
                                                            </a>
                                                        </td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        {% endif %}

                    {% else %}
                        <div class="alert alert-danger">
                            <h4><i class="icon fa fa-ban"></i> Error en el Traspaso Masivo</h4>
                            <p>{{ resultado.mensaje }}</p>
                        </div>

                        {% if resultado.errores %}
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h4>Errores Encontrados</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table class="table table-bordered table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Código Error</th>
                                                    <th>Descripción</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                {% for error in resultado.errores %}
                                                    <tr>
                                                        <td><code>{{ error.codigo }}</code></td>
                                                        <td>{{ error.mensaje }}</td>
                                                    </tr>
                                                {% endfor %}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    {% endif %}

                    <div class="mt-4">
                        <a href="{{ path('seleccionar-productos') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> Nuevo Traspaso
                        </a>
                        <a href="{{ path('homepage') }}" class="btn btn-secondary">
                            <i class="fas fa-home"></i> Inicio
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
$(document).ready(function() {
    // Auto-refresh de la página cada 30 segundos para ver actualizaciones de estado
    // setTimeout(function() {
    //     location.reload();
    // }, 30000);
});
</script>
{% endblock %}
