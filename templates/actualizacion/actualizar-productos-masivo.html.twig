{% extends 'admin/layout.html.twig' %}

{% block title %}Actualización masiva de productos{% endblock %}

{% block content %}
<div class="container-fluid">
  <div class="row">
    <div class="col-md-12">
      <div class="card">
        <div class="card-header card-header-info">
          <div class="row align-items-center">
            <div class="col-md-8">
              <h4 class="card-title mb-0">Actualización masiva de productos (por SKU / UPC / CLAVE)</h4>
              <small class="text-light">Usa Google Sheets o sube un Excel con los encabezados exactos</small>
            </div>
            <div class="col-md-4 text-right">
              <button id="authorize_button" class="btn btn-primary d-none" onclick="handleAuthClick()">Iniciar sesión Google</button>
              <button id="picker_button" class="btn btn-success d-none" onclick="createPicker()">Buscar hoja</button>
              <button id="signout_button" class="btn btn-danger d-none" onclick="handleSignoutClick()">Cerrar sesión</button>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="d-flex flex-wrap justify-content-between align-items-center mb-3">
            <div>
              <h5 class="mb-1">Actualización masiva de productos</h5>
              <small class="text-muted">Identificadores aceptados: SKU, UPC, CLAVE</small>
            </div>
            <div class="btn-group" role="group" aria-label="Acciones rápidas">
              <button class="btn btn-outline-secondary" onclick="downloadTemplate()"><i class="fa fa-download mr-1"></i>Descargar plantilla CSV</button>
              <button class="btn btn-outline-warning" onclick="clearPreview()"><i class="fa fa-eraser mr-1"></i>Limpiar</button>
            </div>
          </div>


          <div class="row mb-3">
            <div class="col-md-7">
              <label class="font-weight-bold">Cargar desde Excel</label>
              <form id="excel-upload-form" onsubmit="return false;" enctype="multipart/form-data">
                <div class="input-group">
                  <div class="custom-file">
                    <input type="file" class="custom-file-input" id="archivo_excel" name="file1" accept=".xlsx,.xls" />
                    <label class="custom-file-label" for="archivo_excel">Elegir archivo...</label>
                  </div>
                  <div class="input-group-append">
                    <button id="btnLeerExcel" class="btn btn-outline-primary" onclick="leerExcel()"><i class="fa fa-file-excel mr-1"></i> Leer Excel</button>
                  </div>
                </div>
              </form>
              <small class="text-muted">Se enviará el archivo al servidor para validar estructura y previsualizar filas.</small>
            </div>
            <div class="col-md-5">
              <label class="font-weight-bold">Cargar desde Google Sheets</label>
              <div class="d-flex align-items-center gap-2">
                <button id="authorize_button" class="btn btn-primary d-none" onclick="handleAuthClick()"><i class="fa fa-google mr-1"></i> Iniciar sesión</button>
                <button id="picker_button" class="btn btn-success d-none" onclick="createPicker()"><i class="fa fa-table mr-1"></i> Elegir hoja</button>
                <button id="signout_button" class="btn btn-danger d-none" onclick="handleSignoutClick()"><i class="fa fa-sign-out-alt mr-1"></i> Cerrar sesión</button>
              </div>
              <small class="text-muted">Leerá la hoja Sheet1 completa para previsualización.</small>
            </div>
          </div>

          <p id="msj" class="text-center text-capitalize">...</p>

          <div class="row mt-3">
            <div class="col-md-7">
              <div class="card">
                <div class="table-responsive">
                  <table class="table table-hover">
                    <thead class="thead-light">
                      <tr>
                        <th>Previsualización</th>
                        <th style="width:110px">Acciones</th>
                      </tr>
                    </thead>
                    <tbody id="contenedor-productos"></tbody>
                  </table>
                </div>
              </div>
            </div>
            <div class="col-md-5">
              <div class="card p-3">
                <div class="row">
                  <div class="col-12">
                    <div class="card text-center pt-3 pb-3">
                      <p class="text-sm mb-0">Instancias válidas</p>
                      <h1 class="mb-0" id="cantidad-productos"></h1>
                    </div>
                  </div>
                  <div class="col-12 mt-2">
                    <button id="error_button" class="btn btn-warning btn-block d-none" data-toggle="modal" data-target="#modal-error-detail" onclick="makeDataTable()">Errores</button>
                  </div>
                  <div class="col-12 mt-2">
                    <button id="btnProcesar" class="btn btn-lg btn-info btn-block" onclick="procesarYDeshabilitar()"><i class="fa fa-upload mr-1"></i> Procesar actualización</button>
                  </div>
                  <div class="col-12 text-center mt-3">
                    <img id="loader" src="{{ asset('img/log.gif') }}" alt="Cargando..." width="120" class="d-none" />
                  </div>
                  <div class="col-12 mt-2" id="contenedor-respuesta"></div>
                </div>
              </div>
            </div>
          </div>

          <input id="url-add-products" type="hidden" value="{{ path('actualizacion-update-add-products') }}" />


        </div>
      </div>
    </div>
  </div>

  <div class="modal" id="modal-error-detail" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-scrollable">
      <div class="modal-content" style="border-radius:10px">
        <div class="modal-header bg-primary text-white">
          <h5 class="modal-title">Detalle de errores</h5>
          <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
        </div>
        <div class="modal-body">
          <table class="table" id="error-table">
            <thead>
              <tr>
                <th>Fila (0..N)</th>
                <th>Identificador</th>
                <th>Mensaje</th>
              </tr>
            </thead>
            <tbody id="error-table-body"></tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script>
  function messageshow(msj, title){ Swal.fire({title, text: msj, icon:'info'}); }
  function clearPreview(){
    checkProducts = new Set();
    errorCount = 0; dictKey=''; cols={};
    $("#contenedor-productos").empty();
    $("#error_button").addClass('d-none').text('');
    $("#cantidad-productos").text('0');
    $("#contenedor-respuesta").empty();
    $("#doc-type").text('[]');
  }
  function downloadTemplate(){
    const headers = [
      'CLAVE','MARCA','MODELO','CÓDIGO DE COLOR','COLOR','UPC','TIPO DE MATERIAL','DESCRIPCIÓN','SOBREPUESTO','MÁXIMO ÚNICO','CATEGORÍA','SUBCATEGORÍA','UNIDAD DE MEDIDA','MEDIDA','ESFERA','CILINDRO','EJE','ADICIÓN','BASE','DISEÑO'
    ];
    const csv = headers.join(',') + '\n';
    const blob = new Blob([csv], {type:'text/csv;charset=utf-8;'});
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a'); a.href=url; a.download='plantilla-actualizacion.csv'; a.click(); URL.revokeObjectURL(url);
  }

  // Estado UI
  var checkProducts = new Set();
  var cols = {};
  var errorCount = 0;
  var dictKey = '';

  function addProducts(formatedCodes){
    const urlElement = document.getElementById('url-add-products');
    const url = urlElement ? urlElement.value : '{{ path("actualizacion-update-add-products") }}';
    const btn = document.getElementById('btnProcesar');
    btn.disabled = true;
    $("#error-table").dataTable().fnDestroy();
    $("#error-table-body").html('');
    $("#contenedor-respuesta").html('');
    errorCount = 0; $("#error_button").addClass("d-none");

    $.ajax({
      url: url, method: 'POST', dataType:'json',
      data: { formatedCodes: formatedCodes },
      beforeSend: function(){ $("#loader").removeClass("d-none"); }
    }).done(function(res){
      btn.disabled = false;
      Swal.fire({ icon: (res.exito? 'success':'error'), title: 'Lectura de archivo', text: (res.msj||'') });
      $("#msj").text(res.msj||'');
      if(res.exito){
        $("#contenedor-productos").html(res.html||'');
        $("#doc-type").text("["+(res.tipoDoc||'')+"]");
        contarProductos();
        checkProducts = new Set();
        cols = res.cols||{}; dictKey = res.dictKey||'';
        (res.codesAdded||[]).forEach((code)=>{ checkProducts.add(code); });
      }
      displayErrorsTable(res.errors||[]);
      $("#loader").addClass("d-none");
    });
  }

  function displayErrorsTable(errors){
    if(!errors || errors.length===0) return;
    errorCount += errors.length; $("#error_button").removeClass("d-none");
    let html = '';
    errors.forEach((e)=>{ html += `<tr><td>${e.index}</td><td>${e.code}</td><td>${e.msg}</td></tr>`; });
    $("#error_button").html(`<i class='fa-solid fa-triangle-exclamation mr-2'></i> inválidas: ${errorCount}`);
    $("#error-table-body").append(html);
  }

  function makeDataTable(){
    if(!$.fn.DataTable.isDataTable('#error-table')){
      $('#error-table').DataTable({ language:{ url:'//cdn.datatables.net/plug-ins/1.13.4/i18n/es-ES.json', lengthMenu:'Mostrar _MENU_ filas' }, dom:'Bfrtip', buttons:[{className:'btn-primary btn', filename:'errores_actualizacion', extend:'excelHtml5', text:'Exportar excel'}] });
    }
  }

  function contarProductos(){
    let cantidad = 0; $(".producto-encontrado").each(function(){ cantidad += 1; });
    $("#cantidad-productos").text(cantidad);
  }


  function quitarProducto(id, jsonValue){
    try{
      // remover fila
      const tr = document.getElementById('producto-'+id);
      if(tr){ tr.remove(); }
      // quitar del set de válidos
      if(jsonValue){ checkProducts.delete(jsonValue); }
    }catch(e){ console.warn('quitarProducto error', e); }
    contarProductos();
  }

  function procesarYDeshabilitar(){ procesarTodo(); document.getElementById('btnProcesar').disabled = true; }

  function procesarTodo(){
    const arr = Array.from(checkProducts).map((s)=>JSON.parse(s));
    if(arr.length<=0){ Swal.fire('Datos Incompletos','Debe agregar al menos una fila válida','warning'); document.getElementById('btnProcesar').disabled=false; return; }
    const urlElement = document.getElementById('url-upload-drive');
    const url = urlElement ? urlElement.value : '{{ path("actualizacion-update-upload-drive") }}';
    Swal.fire({ icon:'info', title:'¿Procesar actualización?', showDenyButton:true, showCancelButton:true, confirmButtonText:'Sí', denyButtonText:'No' }).then((r)=>{
      if(!r.isConfirmed) return;
      $.ajax({ url, method:'POST', dataType:'json', data:{ checkProducts: arr, cols: cols, dictKey: dictKey, extrainfo: {} }, beforeSend: ()=>$("#loader").removeClass("d-none") })
      .done(function(res){ $("#loader").addClass("d-none"); document.getElementById('btnProcesar').disabled=false; $("#contenedor-respuesta").html(res.html||''); Swal.fire({ icon: (res.exito?'success':'error'), title:'Actualización '+(res.exito?'completada':'fallida'), text: res.msj||'' }); });
    });
  }

  // Subida de Excel (servidor valida y devuelve preview similar a Google Sheets)
  function leerExcel(){
    const input = document.getElementById('archivo_excel');
    if(!input.files || input.files.length===0){ Swal.fire('Selecciona un archivo', 'Debes elegir un Excel .xlsx o .xls', 'warning'); return false; }
    const formData = new FormData(); formData.append('file1', input.files[0]);
    const urlElement = document.getElementById('url-upload-doc');
    const url = urlElement ? urlElement.value : '{{ path("actualizacion-update-upload-doc") }}';
    $.ajax({ url, method:'POST', data: formData, contentType:false, processData:false, dataType:'json', beforeSend: ()=>$("#loader").removeClass("d-none") })
    .done(function(res){
      $("#loader").addClass("d-none");
      $("#msj").text(res.msj||'');
      if(res.exito){
        $("#contenedor-productos").html(res.html||'');
        $("#doc-type").text("["+((res.tipoDoc||res.tipoDocumento)||'')+"]");
        contarProductos();
        // poblar conjuntos como en addProducts()
        checkProducts = new Set();
        cols = res.cols||{}; dictKey = res.dictKey||'';
        (res.codesAdded||[]).forEach((code)=>{ checkProducts.add(code); });
      }
      displayErrorsTable(res.errors||[]);
      if(!res.exito){ Swal.fire('Error', res.msj||'Archivo inválido', 'error'); }
    });
    return false;
  }
</script>

<!-- Google Sheets (igual que CargaMasiva) -->
<script>
  const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
  const API_KEY = 'AIzaSyB1-aw7lXPz5acAYPnQF5XjPE66stFeAVk';
  const DISCOVERY_DOC = 'https://sheets.googleapis.com/$discovery/rest?version=v4';
  const SCOPES = 'https://www.googleapis.com/auth/drive.readonly https://www.googleapis.com/auth/spreadsheets.readonly';
  const APP_ID = 'pv360-416621';
  let tokenClient, accessToken = null, pickerInited=false, gisInited=false, gapiInited=false;

  function gapiLoaded(){ gapi.load('client:picker', initializePicker); gapi.load('client', initializeGapiClient); }
  async function initializeGapiClient(){ await gapi.client.init({ apiKey: API_KEY, discoveryDocs:[DISCOVERY_DOC] }); gapiInited=true; maybeEnableButtons(); }
  async function initializePicker(){ await gapi.client.load('https://www.googleapis.com/discovery/v1/apis/drive/v3/rest'); pickerInited=true; maybeEnableButtons(); }
  function gisLoaded(){ tokenClient = google.accounts.oauth2.initTokenClient({ client_id: CLIENT_ID, scope: SCOPES, callback: ''}); gisInited=true; maybeEnableButtons(); }
  function maybeEnableButtons(){ if(pickerInited && gisInited && gapiInited){ $("#authorize_button").removeClass('d-none'); } }
  function handleAuthClick(){ tokenClient.callback = async (resp)=>{ if(resp.error!==undefined) throw(resp); accessToken = resp.access_token; $("#signout_button").removeClass('d-none'); $("#picker_button").removeClass('d-none'); $("#authorize_button").text('Cambiar de cuenta'); await createPicker(); }; if(accessToken===null) tokenClient.requestAccessToken({prompt:'consent'}); else tokenClient.requestAccessToken({prompt:''}); }
  function handleSignoutClick(){ if(accessToken){ const tmp=accessToken; accessToken=null; google.accounts.oauth2.revoke(tmp); $("#authorize_button").text('Iniciar sesión'); $("#signout_button").addClass('d-none'); $("#picker_button").addClass('d-none'); $("#error_button").addClass('d-none'); } }
  function createPicker(){ const view=new google.picker.View(google.picker.ViewId.DOCS); view.setMimeTypes('application/vnd.google-apps.spreadsheet'); const picker=new google.picker.PickerBuilder().enableFeature(google.picker.Feature.NAV_HIDDEN).setDeveloperKey(API_KEY).setAppId(APP_ID).setOAuthToken(accessToken).addView(view).addView(new google.picker.DocsUploadView()).setCallback(pickerCallback).build(); picker.setVisible(true); }
  async function pickerCallback(data){ if(data.action===google.picker.Action.PICKED){ const doc=data[google.picker.Response.DOCUMENTS][0]; const fileId=doc[google.picker.Document.ID]; const response=await gapi.client.sheets.spreadsheets.values.get({ spreadsheetId:fileId, range:'Sheet1', majorDimension:'ROWS' }); const values=response.result.values; if(values && values.length>0){ const maxLen=values[0].length; const padded=values.map(r=>{ const diff=maxLen - r.length; return r.concat(Array(diff>0?diff:0).fill('')); }); addProducts(padded); } } }
</script>
<script async defer src="https://apis.google.com/js/api.js" onload="gapiLoaded()"></script>
<script async defer src="https://accounts.google.com/gsi/client" onload="gisLoaded()"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/dataTables.buttons.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.4.1/js/buttons.html5.min.js"></script>
<script>
  $(document).ready(function(){ $('.custom-file-input').on('change', function(){ var fileName=$(this).val().split('\\\\').pop(); $(this).next('.custom-file-label').html(fileName); }); });
</script>
{% endblock %}

