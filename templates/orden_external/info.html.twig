<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Información de Orden - {{ folio }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card {
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            border: none;
        }
        .card-header {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            padding: 1.5rem;
        }
        .status-badge {
            font-size: 1.1rem;
            padding: 0.5rem 1rem;
            border-radius: 25px;
        }
        .info-item {
            border-bottom: 1px solid #eee;
            padding: 1rem 0;
        }
        .info-item:last-child {
            border-bottom: none;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 0.25rem;
        }
        .info-value {
            color: #212529;
            font-size: 1.1rem;
        }
        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="container py-4">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="card">
                    <div class="card-header text-center">
                        <h2 class="mb-0">
                            <i class="fas fa-flask me-2"></i>
                            Orden de Laboratorio
                        </h2>
                        <p class="mb-0 mt-2 opacity-75">Folio: {{ folio }}</p>
                    </div>
                    
                    <div class="card-body p-4">
                        <!-- Estado de la orden -->
                        <div class="text-center mb-4">
                            {% set estadoClass = 'secondary' %}
                            {% set estadoIcon = 'clock' %}
                            
                            {% if orden.estado == 'Pendiente' %}
                                {% set estadoClass = 'warning' %}
                                {% set estadoIcon = 'clock' %}
                            {% elseif orden.estado == 'En Proceso' %}
                                {% set estadoClass = 'info' %}
                                {% set estadoIcon = 'cog fa-spin' %}
                            {% elseif orden.estado == 'Completada' %}
                                {% set estadoClass = 'success' %}
                                {% set estadoIcon = 'check-circle' %}
                            {% elseif orden.estado == 'Entregada' %}
                                {% set estadoClass = 'primary' %}
                                {% set estadoIcon = 'handshake' %}
                            {% elseif orden.estado == 'Cancelada' %}
                                {% set estadoClass = 'danger' %}
                                {% set estadoIcon = 'times-circle' %}
                            {% endif %}
                            
                            <span class="badge bg-{{ estadoClass }} status-badge">
                                <i class="fas fa-{{ estadoIcon }} me-2"></i>
                                {{ orden.estado|default('Sin Estado') }}
                            </span>
                        </div>

                        <!-- Información de la orden -->
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-hashtag text-primary me-2"></i>
                                ID de Orden
                            </div>
                            <div class="info-value">{{ orden.idordenlaboratorio }}</div>
                        </div>

                        {% if cliente %}
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-user text-primary me-2"></i>
                                Cliente
                            </div>
                            <div class="info-value">{{ cliente.nombre }} {{ cliente.apellidopaterno }} {{ cliente.apellidomaterno }}</div>
                        </div>
                        {% endif %}

                        {% if sucursal %}
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                Sucursal
                            </div>
                            <div class="info-value">{{ sucursal.nombre }}</div>
                        </div>
                        {% endif %}

                        {% if orden.fechacreacion %}
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-plus text-primary me-2"></i>
                                Fecha de Creación
                            </div>
                            <div class="info-value">{{ orden.fechacreacion|date('d/m/Y H:i') }}</div>
                        </div>
                        {% endif %}

                        {% if orden.fechaentrega %}
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-calendar-check text-primary me-2"></i>
                                Fecha de Entrega
                            </div>
                            <div class="info-value">{{ orden.fechaentrega|date('d/m/Y') }}</div>
                        </div>
                        {% endif %}

                        {% if orden.observaciones %}
                        <div class="info-item">
                            <div class="info-label">
                                <i class="fas fa-sticky-note text-primary me-2"></i>
                                Observaciones
                            </div>
                            <div class="info-value">{{ orden.observaciones }}</div>
                        </div>
                        {% endif %}

                        <!-- Última actualización -->
                        <div class="text-center mt-4 pt-3 border-top">
                            <small class="text-muted">
                                <i class="fas fa-sync-alt me-1"></i>
                                Última actualización: <span id="lastUpdate">{{ "now"|date('d/m/Y H:i:s') }}</span>
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botón de actualizar -->
    <button class="btn btn-primary btn-lg rounded-circle refresh-btn" onclick="location.reload()" title="Actualizar información">
        <i class="fas fa-sync-alt"></i>
    </button>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Auto-refresh cada 30 segundos
        setInterval(function() {
            location.reload();
        }, 30000);

        // Actualizar timestamp cada segundo
        setInterval(function() {
            document.getElementById('lastUpdate').textContent = new Date().toLocaleString('es-ES');
        }, 1000);
    </script>
</body>
</html>
