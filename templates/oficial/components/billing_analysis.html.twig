<!-- Facturación Section (Open by default) -->
<section class="billing-analysis">
  <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
    <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
      <i class="fas fa-file-invoice-dollar" style="color: var(--info);"></i>
      🧾 Análisis de Facturación
      <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
    </summary>

    <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
      <!-- Year Selector for Billing -->
      <div style="margin-bottom: 1.5rem;">
        <label for="year-select-facturacion" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
          <i class="fas fa-calendar"></i> Año para Facturación:
        </label>
        <select id="year-select-facturacion" name="year" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900); min-width: 150px;">
          <option value="2025" selected>2025</option>
          <option value="2024">2024</option>
          <option value="2023">2023</option>
          <option value="2022">2022</option>
        </select>
        <button onclick="buscarDatosFacturacion()" style="margin-left: 1rem; padding: 0.75rem 1rem; background: var(--info); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer;">
          <i class="fas fa-sync-alt"></i> Actualizar
        </button>
      </div>

      <!-- Billing Charts Grid -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem;">

        <!-- Facturación por Estado -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-info-circle" style="color: var(--info);"></i>
            Facturación por Estado
          </h4>
          <div id="estatus" style="height: 300px; min-height: 300px;"></div>
        </div>

        <!-- Total Facturado -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-dollar-sign" style="color: var(--success-green);"></i>
            Total Facturado
          </h4>
          <div id="tio" style="height: 300px; min-height: 300px;"></div>
        </div>

        <!-- Facturación Mensual -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-chart-line" style="color: var(--primary-blue);"></i>
            Evolución Mensual de Facturación
          </h4>
          <div id="Sumaim" style="height: 350px; min-height: 350px;"></div>
        </div>

      </div>
    </div>
  </details>
</section>