<!-- Header con controles -->
<div style="position: relative; margin-bottom: 1rem;">
  <!-- Theme Toggle Button - Posición ajustada -->
  <button class="theme-toggle" onclick="toggleTheme()" title="Cambiar tema" style="position: fixed; top: 80px; right: 20px; z-index: 1000;">
    <span id="theme-icon">🌙</span>
  </button>

  <!-- Botón de Cerrar Sesión - Posición prominente -->
  <div style="position: absolute; top: 0; right: 0; z-index: 999;">
    <button onclick="confirmarCerrarSesion()" style="padding: 0.75rem 1.5rem; background: var(--danger-red); color: white; border: none; border-radius: 12px; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.3s ease; display: flex; align-items: center; gap: 0.5rem; box-shadow: var(--shadow-lg);" onmouseover="this.style.background='#c53030'; this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(220, 53, 69, 0.3)'" onmouseout="this.style.background='var(--danger-red)'; this.style.transform='translateY(0)'; this.style.boxShadow='var(--shadow-lg)'">
      <i class="fas fa-sign-out-alt"></i>
      Cerrar Sesión
    </button>
  </div>
</div>