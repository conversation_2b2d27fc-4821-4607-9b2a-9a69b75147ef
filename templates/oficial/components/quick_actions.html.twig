<!-- Quick Actions Section -->
<section class="quick-actions" style="margin-bottom: 2rem;">
  <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
    <h3 style="font-size: 1.125rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
      <i class="fas fa-sliders-h" style="color: var(--primary-blue);"></i>
      Acciones Rápidas
    </h3>

    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; align-items: end;">
      <div>
        <label for="fecha-inicio" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
          <i class="fas fa-calendar"></i> Fecha Inicio:
        </label>
        <input type="date" id="fecha-inicio" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
      </div>
      
      <div>
        <label for="fecha-fin" style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
          <i class="fas fa-calendar"></i> Fecha Fin:
        </label>
        <input type="date" id="fecha-fin" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);">
      </div>

      <div>
        <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
          <i class="fas fa-store"></i> Sucursales:
        </label>
        <div id="sucursales-summary" style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700); cursor: pointer;" onclick="toggleSucursalesPanel()">
          <span id="sucursales-count">Cargando...</span>
          <i class="fas fa-chevron-down" style="float: right; margin-top: 0.125rem;"></i>
        </div>
      </div>

      <div>
        <label style="display: block; font-size: 0.875rem; font-weight: 500; color: var(--gray-700); margin-bottom: 0.5rem;">
          <i class="fas fa-building"></i> Empresa:
        </label>
        {% if not is_hardcoded %}
          <select name="empresa" id="idempresa" style="width: 100%; padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--white); color: var(--gray-900);" onchange="cargaDefiltros();">
            <option value="-1">Seleccione una empresa</option>
            {% for empresa in empresas %}
              <option value="{{ empresa.idempresa }}">{{ empresa.nombre }}</option>
            {% endfor %}
          </select>
        {% else %}
          <input type="hidden" id="idempresa" value="{{ empresas[0].idempresa }}">
          <div style="padding: 0.75rem; border: 1px solid var(--gray-300); border-radius: 8px; font-size: 0.875rem; background: var(--gray-50); color: var(--gray-700);">
            <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>
            {{ empresas[0].nombre }} (BIMBO)
          </div>
        {% endif %}
      </div>

      <div>
        <button onclick="buscarIngresosDiarios()" style="width: 100%; padding: 0.75rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 8px; font-size: 0.875rem; font-weight: 500; cursor: pointer; transition: all 0.2s ease; display: flex; align-items: center; justify-content: center; gap: 0.5rem;" onmouseover="this.style.background='var(--primary-blue-dark)'" onmouseout="this.style.background='var(--primary-blue)'">
          <i class="fas fa-sync-alt"></i>
          Actualizar
        </button>
      </div>
    </div>
  </div>
</section>

<!-- Sucursales Panel (Collapsible) -->
<div id="sucursales-panel" style="display: none; margin-bottom: 2rem;">
  <div style="background: var(--white); border-radius: 12px; padding: 1.5rem; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200);">
    <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0;">
      <i class="fas fa-store"></i> Seleccionar Sucursales
    </h4>
    <div id="sucursales"></div>
  </div>
</div>

<!-- Hidden inputs for compatibility -->
<input type="hidden" id="tipo-venta" value="BIMBO">