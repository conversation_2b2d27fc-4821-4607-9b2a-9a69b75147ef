{# Loading Indicator #}
{% macro loading_indicator(message = 'Cargando datos...') %}
<div class="loading-indicator" style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);">
  <div style="text-align: center;">
    <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i>
    <p>{{ message }}</p>
    <small style="color: var(--gray-400);">Esto puede tomar unos segundos</small>
  </div>
</div>
{% endmacro %}

{# Error Message #}
{% macro error_message(message = 'Error al cargar datos', retry_function = null) %}
<div class="error-message" style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--danger-red);">
  <div style="text-align: center;">
    <i class="fas fa-exclamation-triangle fa-2x" style="margin-bottom: 1rem;"></i>
    <h4 style="color: var(--danger-red); margin-bottom: 0.5rem;">{{ message }}</h4>
    <p style="color: var(--gray-500); margin: 0;">
      Hubo un problema al obtener los datos.
    </p>
    {% if retry_function %}
    <button onclick="{{ retry_function }}" style="margin-top: 1rem; padding: 0.5rem 1rem; background: var(--primary-blue); color: white; border: none; border-radius: 6px; cursor: pointer;">
      <i class="fas fa-redo"></i> Reintentar
    </button>
    {% endif %}
  </div>
</div>
{% endmacro %}

{# No Data Message #}
{% macro no_data_message(message = 'No hay datos disponibles') %}
<div class="no-data-message" style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--gray-500);">
  <div style="text-align: center;">
    <i class="fas fa-inbox fa-3x" style="margin-bottom: 1rem; color: var(--gray-400);"></i>
    <h4 style="color: var(--gray-600); margin-bottom: 0.5rem;">{{ message }}</h4>
    <p style="color: var(--gray-500); margin: 0;">
      No se encontraron datos para los criterios seleccionados.
    </p>
    <small style="color: var(--gray-400); margin-top: 0.5rem; display: block;">
      Intente seleccionar otros filtros o verifique que haya datos disponibles.
    </small>
  </div>
</div>
{% endmacro %}

{# Success Message #}
{% macro success_message(message = 'Operación completada correctamente') %}
<div class="success-message" style="display: flex; align-items: center; justify-content: center; height: 300px; color: var(--success-green);">
  <div style="text-align: center;">
    <i class="fas fa-check-circle fa-3x" style="margin-bottom: 1rem;"></i>
    <h4 style="color: var(--success-green); margin-bottom: 0.5rem;">{{ message }}</h4>
    <p style="color: var(--gray-500); margin: 0;">
      La operación se ha completado correctamente.
    </p>
  </div>
</div>
{% endmacro %}

{# Info Message #}
{% macro info_message(title = 'Información', message = '') %}
<div style="margin-bottom: 1.5rem; padding: 0.75rem; background: var(--info-light); border-radius: 8px; border-left: 4px solid var(--info);">
  <p style="margin: 0; font-size: 0.875rem; color: var(--info-dark);">
    <i class="fas fa-info-circle me-2"></i>
    <strong>{{ title }}:</strong> {{ message }}
  </p>
</div>
{% endmacro %}

{# Warning Message #}
{% macro warning_message(title = 'Atención', message = '') %}
<div style="margin-bottom: 1.5rem; padding: 0.75rem; background: #fff8e6; border-radius: 8px; border-left: 4px solid var(--warning-amber);">
  <p style="margin: 0; font-size: 0.875rem; color: #92400e;">
    <i class="fas fa-exclamation-circle me-2"></i>
    <strong>{{ title }}:</strong> {{ message }}
  </p>
</div>
{% endmacro %}

{# Progress Bar #}
{% macro progress_bar(progress = 0, message = 'Cargando...') %}
<div class="progress-container" style="margin-bottom: 1.5rem;">
  <div style="display: flex; justify-content: space-between; margin-bottom: 0.5rem;">
    <span style="font-size: 0.875rem; color: var(--gray-700);">{{ message }}</span>
    <span style="font-size: 0.875rem; color: var(--gray-700);">{{ progress }}%</span>
  </div>
  <div style="height: 8px; background-color: var(--gray-200); border-radius: 4px; overflow: hidden;">
    <div style="height: 100%; width: {{ progress }}%; background-color: var(--primary-blue); border-radius: 4px; transition: width 0.3s ease;"></div>
  </div>
</div>
{% endmacro %}