<!-- Detailed Analysis Section (Open by default) -->
<section class="detailed-analysis">
  <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
    <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
      <i class="fas fa-chart-pie" style="color: var(--primary-blue);"></i>
      📊 Análisis de Ingresos Diarios
      <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
    </summary>

    <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
      <!-- Charts Grid -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 1.5rem; margin-top: 1.5rem;">

        <!-- Ingresos por Sucursal -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-store" style="color: var(--success-green);"></i>
            Ingresos por Sucursal
          </h4>
          <div id="graficaSucursal" style="height: 300px; min-height: 300px;"></div>
        </div>

        <!-- Tipos de Pago -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-credit-card" style="color: var(--primary-blue);"></i>
            Tipos de Pago
          </h4>
          <div id="graficaTipoPago" style="height: 300px; min-height: 300px;"></div>
        </div>

        <!-- Deuda por Sucursal -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200); grid-column: 1 / -1;">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-exclamation-triangle" style="color: var(--warning-amber);"></i>
            Deuda por Sucursal
          </h4>
          <div id="deudaTotal" style="height: 300px; min-height: 300px; display: flex; align-items: center; justify-content: center; color: var(--gray-500);">
            <div style="text-align: center;">
              <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem;"></i>
              <p>Cargando datos...</p>
            </div>
          </div>
        </div>

      </div>

      <!-- DataTable Section -->
      <div style="margin-top: 2rem;">
        <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
          <i class="fas fa-table" style="color: var(--info);"></i>
          Detalle de Ingresos por Sucursal
        </h4>

        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <table id="tablaIngresosDiarios" class="table table-striped table-hover" style="width: 100%; margin: 0;">
            <tbody>
              <!-- DataTables manejará el contenido -->
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </details>
</section>