<!-- Productos y Marcas Section (Open by default) -->
<section class="products-analysis">
  <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
    <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
      <i class="fas fa-tags" style="color: var(--warning-amber);"></i>
      🏷️ Productos y Marcas
      <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
    </summary>

    <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
      <!-- Info: Usa la fecha seleccionada en el dashboard principal -->
      <div style="margin-bottom: 1.5rem; padding: 0.75rem; background: var(--info-light); border-radius: 8px; border-left: 4px solid var(--info);">
        <p style="margin: 0; font-size: 0.875rem; color: var(--info-dark);">
          <i class="fas fa-info-circle me-2"></i>
          <strong>Nota:</strong> Las gráficas y tabla usan la fecha seleccionada en el dashboard principal.
        </p>
      </div>

      <!-- Products Charts Grid -->
      <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 1.5rem;">

        <!-- Análisis de Marcas -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-tag" style="color: var(--warning-amber);"></i>
            Modelos por Marca
          </h4>
          <div id="recuentoMarcas" style="height: 350px; min-height: 350px;"></div>
        </div>

        <!-- Modelos y Tratamientos -->
        <div style="background: var(--gray-50); border-radius: 8px; padding: 1.5rem; border: 1px solid var(--gray-200);">
          <h4 style="font-size: 1rem; font-weight: 600; color: var(--gray-900); margin: 0 0 1rem 0; display: flex; align-items: center; gap: 0.5rem;">
            <i class="fas fa-cogs" style="color: var(--info);"></i>
            Distribución de Tratamientos
          </h4>
          <div id="tratamientoGrafica" style="height: 350px; min-height: 350px;"></div>
        </div>

      </div>
    </div>
  </details>
</section>

<!-- Tabla de Productos y Marcas Section -->
<section class="productos-table-analysis">
  <details class="analysis-section" open style="background: var(--white); border-radius: 12px; box-shadow: var(--shadow-sm); border: 1px solid var(--gray-200); margin-bottom: 2rem;">
    <summary style="padding: 1.5rem; cursor: pointer; font-size: 1.125rem; font-weight: 600; color: var(--gray-900); display: flex; align-items: center; gap: 0.5rem; user-select: none;">
      <i class="fas fa-table" style="color: var(--primary-blue);"></i>
      📋 Tabla de Productos y Marcas
      <i class="fas fa-chevron-down" style="margin-left: auto; transition: transform 0.3s ease;"></i>
    </summary>

    <div style="padding: 0 1.5rem 1.5rem 1.5rem; border-top: 1px solid var(--gray-200);">
      <!-- Controles de la Tabla -->
      <div class="d-flex justify-content-end align-items-center mb-3">
        <button id="refresh-productos-table" class="btn btn-primary btn-sm">
          <i class="fas fa-sync-alt me-1"></i>
          Actualizar
        </button>
      </div>

      <!-- Tabla -->
      <div class="table-responsive">
        <table id="productos-table" class="table table-hover table-sm" style="background: var(--white); width: 100%;">
          <tbody>
            <!-- DataTables manejará el contenido -->
          </tbody>
        </table>
      </div>
    </div>
  </details>
</section>