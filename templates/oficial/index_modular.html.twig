{% if is_supervisor %}
{% extends 'supervisor_layout.html.twig' %}
{% endif %}

{% block titleHead %}{% endblock %}
{% block title %}Reporte de Productos{% endblock %}

{% block stylesheets %}
{{ parent() }}
<!-- Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
<!-- DataTables CSS -->
<link href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<link href="https://cdn.datatables.net/buttons/2.3.2/css/buttons.bootstrap5.min.css" rel="stylesheet" />

<!-- Dashboard Oficial CSS -->
<link href="{{ asset('css/puntodeventa/dashboard-oficial.css') }}" rel="stylesheet" />
{% endblock %}

{% block content %}
{# Import common elements macros #}
{% import 'oficial/components/common_elements.html.twig' as elements %}

<!-- Executive Dashboard -->
<div class="executive-dashboard" data-theme="light">

  {# Include Header Component #}
  {% include 'oficial/components/header.html.twig' %}

  {# Include Hero Metrics Component #}
  {% include 'oficial/components/hero_metrics.html.twig' %}

  {# Include Quick Actions Component #}
  {% include 'oficial/components/quick_actions.html.twig' %}

  {# Include Detailed Analysis Component #}
  {% include 'oficial/components/detailed_analysis.html.twig' %}

  {# Include Annual Analysis Component #}
  {% include 'oficial/components/annual_analysis.html.twig' %}

  {# Include Products Analysis Component #}
  {% include 'oficial/components/products_analysis.html.twig' %}

  {# Include Billing Analysis Component #}
  {% include 'oficial/components/billing_analysis.html.twig' %}

  {# Include Advanced Config Component #}
  {% include 'oficial/components/advanced_config.html.twig' %}

</div>
<!-- End Executive Dashboard -->

<!-- Hidden Elements for Compatibility -->
<div style="display: none;">
  <!-- Keep all the old content hidden for JavaScript compatibility -->
  <div class="filters-container">
    <h3 class="filters-title">
      <i class="fas fa-calendar"></i>
      Ventas Acumuladas por Año
    </h3>
    <div class="row g-3 align-items-end">
      <div class="col-md-3">
        <label for="year-select" class="form-label">Año:</label>
        <select name="year" id="year-select" class="form-select">
          <!-- Options will be dynamically populated here -->
        </select>
      </div>
    </div>
  </div>

  <div class="chart-container mb-4">
    <div class="chart-header">
      <h3 class="chart-title">
        <i class="fas fa-chart-bar"></i>
        Análisis de Ventas Anuales
      </h3>
      <div class="chart-actions">
        <button class="btn btn-success btn-corporate btn-sm" onclick="buscarVentasAnuales()">
          <i class="fas fa-chart-bar"></i> Actualizar Ventas
        </button>
      </div>
    </div>
    <!-- Sección duplicada eliminada - ahora solo usamos la del dashboard ejecutivo -->
  </div>
  <!-- Sección de Marcas y Modelos -->
  <div class="chart-container mb-4">
    <div class="chart-header">
      <h3 class="chart-title">
        <i class="fas fa-tags"></i>
        Análisis de Marcas y Modelos
      </h3>
      <div class="chart-actions">
        <button class="btn btn-outline-primary btn-corporate btn-sm" onclick="buscarVentasAnuales()">
          <i class="fas fa-sync-alt"></i> Actualizar Marcas
        </button>
      </div>
    </div>
    <div class="row g-4">
      <div class="col-lg-6">
        <div class="corporate-card">
          <div class="corporate-card-header">
            <h4 class="corporate-card-title">
              <i class="fas fa-trademark"></i>
              Modelos por Marca
            </h4>
          </div>
          <div class="corporate-card-body">
            <div id="recuentoMarcas" style="height: 400px; min-height: 400px;"></div>
          </div>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="corporate-card">
          <div class="corporate-card-header">
            <h4 class="corporate-card-title">
              <i class="fas fa-chart-pie"></i>
              Distribución de Tratamientos
            </h4>
          </div>
          <div class="corporate-card-body">
            <div id="tratamientoGrafica" style="height: 400px; min-height: 400px;"></div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Sección de Facturación -->
  <div class="filters-container">
    <h3 class="filters-title">
      <i class="fas fa-file-invoice"></i>
      Configuración de Facturación
    </h3>
    <div class="row g-3 align-items-end">
      <div class="col-md-3">
        <label for="year-select-facturacion" class="form-label">Año:</label>
        <select name="year" id="year-select-facturacion" class="form-select">
          <!-- Options will be dynamically populated here -->
        </select>
      </div>
    </div>
  </div>

  <!-- Métrica de Suma de Importes -->
  <div class="row g-4 mb-4">
    <div class="col-12">
      <div class="metric-card success">
        <div class="metric-icon success">
          <i class="fas fa-file-invoice-dollar"></i>
        </div>
        <div class="metric-value" id="suma-im">$0.00</div>
        <div class="metric-label">Total Facturado</div>
        <div class="metric-description">Suma total de importes facturados</div>
      </div>
    </div>
  </div>
  <!-- Sección duplicada eliminada - ahora solo usamos la sección nueva en el dashboard ejecutivo -->
</div>
{% endblock %}

{% block javascripts %}
{{ parent() }}
<script src="{{ asset('lib/apexcharts-bundle/dist/apexcharts.min.js') }}"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
<script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/l10n/es.js"></script>
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/dataTables.buttons.min.js"></script>
<script src="https://cdn.datatables.net/buttons/2.3.2/js/buttons.html5.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
<!-- SheetJS para exportación a Excel -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
// ===== FUNCIONES PRINCIPALES DE CARGA =====
function actualizarTodasLasGraficas() {
     ('Actualizando todas las gráficas del dashboard...');
    mostrarBarraProgreso();
    cargarGraficasSecuencialmente();
}

// ===== SISTEMA DE CARGA SECUENCIAL =====
async function cargarGraficasSecuencialmente() {
    const pasos = [
        { nombre: 'Resumen de Ingresos', funcion: cargarResumenIngresos, progreso: 20 },
        { nombre: 'Gráficas de Ingresos Diarios', funcion: cargarGraficasIngresosDiarios, progreso: 40 },
        { nombre: 'Gráficas de Ventas Anuales', funcion: cargarGraficasVentasAnuales, progreso: 60 },
        { nombre: 'Gráficas de Marcas y Modelos', funcion: cargarGraficasMarcasYModelos, progreso: 80 },
        { nombre: 'Gráficas de Facturación', funcion: cargarGraficasFacturacion, progreso: 100 }
    ];

    for (let i = 0; i < pasos.length; i++) {
        const paso = pasos[i];
        actualizarProgreso(paso.progreso, `Cargando ${paso.nombre}...`);

        try {
            await new Promise((resolve) => {
                setTimeout(() => {
                    paso.funcion();
                    resolve();
                }, 500); // Pequeña pausa entre cargas para mejor UX
            });
        } catch (error) {
            console.error(`Error al cargar ${paso.nombre}:`, error);
        }
    }

    setTimeout(() => {
        ocultarBarraProgreso();
        mostrarMensajeExito('¡Dashboard actualizado correctamente!');
    }, 1000);
}

// Función para cuando cambia la empresa
function onEmpresaChange() {
    actualizarTodasLasGraficas();
}

// ===== FUNCIONES ESPECÍFICAS PARA CADA BOTÓN =====
function buscarIngresosDiarios() {
     ('Buscando ingresos diarios...');

    // Validar que se hayan seleccionado fechas y sucursales
    const fechaInicio = document.getElementById('fecha-inicio').value;
    const fechaFin = document.getElementById('fecha-fin').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!fechaInicio) {
        mostrarAlertaAdvertencia('Por favor, seleccione una fecha de inicio.');
        return;
    }

    if (!fechaFin) {
        mostrarAlertaAdvertencia('Por favor, seleccione una fecha de fin.');
        return;
    }

    // Validar que la fecha de inicio no sea posterior a la fecha de fin
    if (fechaInicio > fechaFin) {
        mostrarAlertaAdvertencia('La fecha de inicio no puede ser posterior a la fecha de fin.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarResumenIngresos();
    cargarGraficasIngresosDiarios(false); // isAutoLoad = false (llamada manual)
    mostrarMensajeExito('Ingresos diarios actualizados correctamente');
}

function buscarVentasAnuales() {
     ('Buscando ventas anuales...');

    // Validar que se haya seleccionado año y sucursales
    const year = document.getElementById('year-select').value;
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')];

    if (!year) {
        mostrarAlertaAdvertencia('Por favor, seleccione un año.');
        return;
    }

    if (sucursales.length === 0) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Cargar datos
    cargarGraficasVentasAnuales();
    cargarGraficasMarcasYModelos();
    mostrarMensajeExito('Ventas anuales actualizadas correctamente');
}

function buscarDatosFacturacion() {
    console.log('🔄 Iniciando búsqueda de datos de facturación...');

    // Validar que se haya seleccionado año
    const year = document.getElementById('year-select-facturacion').value || new Date().getFullYear();
    const sucursales = [...document.querySelectorAll('input[name="sucursal"]:checked')].map(cb => cb.value).join(',');

    console.log('📅 Año seleccionado:', year);
    console.log('🏪 Sucursales seleccionadas:', sucursales);

    if (!sucursales) {
        mostrarAlertaAdvertencia('Por favor, seleccione al menos una sucursal.');
        return;
    }

    // Mostrar indicadores de carga mejorados
    const estatusElement = document.getElementById('estatus');
    const tioElement = document.getElementById('tio');
    const sumaimElement = document.getElementById('Sumaim');

    const loadingHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: var(--gray-500);">
            <div style="text-align: center;">
                <i class="fas fa-spinner fa-spin fa-2x" style="margin-bottom: 1rem; color: var(--primary-blue);"></i>
                <p>Cargando datos de facturación...</p>
                <small style="color: var(--gray-400);">Esto puede tomar unos segundos</small>
            </div>
        </div>
    `;

    if (estatusElement) estatusElement.innerHTML = loadingHTML;
    if (tioElement) tioElement.innerHTML = loadingHTML;
    if (sumaimElement) sumaimElement.innerHTML = loadingHTML;

    // Hacer llamada a la API
    const startTime = performance.now();

    fetch(`/cliente-api/get-facturacion-data?year=${year}&sucursales=${sucursales}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            const endTime = performance.now();
            console.log(`✅ Datos de facturación recibidos en ${(endTime - startTime).toFixed(2)}ms:`, data);

            // Verificar si hay datos
            const hasData = (
                (data.facturasPorEstado && data.facturasPorEstado.length > 0) ||
                (data.facturacionPorSucursal && data.facturacionPorSucursal.length > 0) ||
                (data.facturacionMensual && data.facturacionMensual.length > 0)
            );

            if (!hasData) {
                console.log('⚠️ No se encontraron datos de facturación');
                mostrarMensajeNoData();
                return;
            }

            // Cargar gráficas con datos reales
            cargarGraficasFacturacionConDatos(data);
            mostrarMensajeExito('Datos de facturación actualizados correctamente');

        })
        .catch(error => {
            console.error('❌ Error al cargar datos de facturación:', error);
            mostrarAlertaError('Error al cargar los datos de facturación. Por favor, intente de nuevo.');
            mostrarMensajeError();
        });
}

// Resto del código JavaScript (sin cambios)
// ...

// ===== FUNCIÓN PARA CERRAR SESIÓN CON CONFIRMACIÓN =====
function confirmarCerrarSesion() {
    Swal.fire({
        title: '¿Cerrar Sesión?',
        text: '¿Estás seguro de que deseas cerrar tu sesión?',
        icon: 'question',
        showCancelButton: true,
        confirmButtonColor: '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: '<i class="fas fa-sign-out-alt"></i> Sí, Cerrar Sesión',
        cancelButtonText: '<i class="fas fa-times"></i> Cancelar',
        reverseButtons: true,
        customClass: {
            popup: 'swal2-popup-custom',
            title: 'swal2-title-custom',
            content: 'swal2-content-custom',
            confirmButton: 'swal2-confirm-custom',
            cancelButton: 'swal2-cancel-custom'
        },
        buttonsStyling: true,
        allowOutsideClick: false,
        allowEscapeKey: true,
        focusConfirm: false,
        focusCancel: true
    }).then((result) => {
        if (result.isConfirmed) {
            // Mostrar loading mientras se cierra sesión
            Swal.fire({
                title: 'Cerrando Sesión...',
                text: 'Por favor espera un momento',
                icon: 'info',
                allowOutsideClick: false,
                allowEscapeKey: false,
                showConfirmButton: false,
                didOpen: () => {
                    Swal.showLoading();
                }
            });

            // Redirigir al logout después de un breve delay
            setTimeout(() => {
                window.location.href = '/admin/logout';
            }, 1000);
        }
    });
}

</script>
{% endblock %}