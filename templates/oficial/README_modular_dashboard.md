# Dashboard Modular - Documentación

## Introducción

Este documento describe la estructura modular implementada para el dashboard de administrador/supervisor. La modularización se ha realizado para mejorar la mantenibilidad, escalabilidad y reutilización del código.

## Estructura de Archivos

La estructura de archivos del dashboard modular es la siguiente:

```
templates/oficial/
├── components/                      # Componentes reutilizables
│   ├── header.html.twig             # Cabecera con botones de tema y cerrar sesión
│   ├── hero_metrics.html.twig       # Sección de métricas principales
│   ├── quick_actions.html.twig      # Sección de acciones rápidas (filtros)
│   ├── detailed_analysis.html.twig  # Análisis detallado de ingresos diarios
│   ├── annual_analysis.html.twig    # Análisis anual de ventas
│   ├── products_analysis.html.twig  # Análisis de productos y marcas
│   ├── billing_analysis.html.twig   # Análisis de facturación
│   ├── advanced_config.html.twig    # Configuración avanzada
│   └── common_elements.html.twig    # Elementos comunes (macros)
├── index.html.twig                  # Versión original del dashboard
├── index_modular.html.twig          # Versión modular del dashboard
└── README_modular_dashboard.md      # Esta documentación
```

## Uso del Dashboard Modular

Para utilizar la versión modular del dashboard, simplemente renombra `index_modular.html.twig` a `index.html.twig` (después de hacer una copia de seguridad del archivo original).

## Componentes

### 1. Header (`header.html.twig`)
Contiene el botón para cambiar el tema (claro/oscuro) y el botón para cerrar sesión.

### 2. Hero Metrics (`hero_metrics.html.twig`)
Contiene el título del dashboard, la fecha y hora actuales, y las métricas principales (Ventas del Día, Pagos Recibidos, Por Cobrar).

### 3. Quick Actions (`quick_actions.html.twig`)
Contiene los filtros para seleccionar fechas, sucursales y empresa, así como el botón para actualizar los datos.

### 4. Detailed Analysis (`detailed_analysis.html.twig`)
Contiene las gráficas de ingresos por sucursal, tipos de pago, deuda por sucursal, y la tabla de detalle de ingresos por sucursal.

### 5. Annual Analysis (`annual_analysis.html.twig`)
Contiene el selector de año y las gráficas de ventas mensuales y pagos anuales por sucursal.

### 6. Products Analysis (`products_analysis.html.twig`)
Contiene las gráficas de modelos por marca y distribución de tratamientos, así como la tabla de productos y marcas.

### 7. Billing Analysis (`billing_analysis.html.twig`)
Contiene el selector de año para facturación y las gráficas de facturación por estado, total facturado, y evolución mensual de facturación.

### 8. Advanced Config (`advanced_config.html.twig`)
Contiene la configuración avanzada para seleccionar un rango de fechas personalizado.

### 9. Common Elements (`common_elements.html.twig`)
Contiene macros para elementos comunes como indicadores de carga, mensajes de error, mensajes de éxito, etc.

## Elementos Comunes (Macros)

El archivo `common_elements.html.twig` contiene macros para elementos comunes que se pueden reutilizar en diferentes partes del dashboard:

```twig
{# Uso de macros #}
{% import 'oficial/components/common_elements.html.twig' as elements %}

{# Indicador de carga #}
{{ elements.loading_indicator('Cargando datos...') }}

{# Mensaje de error #}
{{ elements.error_message('Error al cargar datos', 'recargarDatos()') }}

{# Mensaje de no hay datos #}
{{ elements.no_data_message('No hay datos disponibles') }}

{# Mensaje de éxito #}
{{ elements.success_message('Operación completada correctamente') }}

{# Mensaje informativo #}
{{ elements.info_message('Nota', 'Este es un mensaje informativo') }}

{# Mensaje de advertencia #}
{{ elements.warning_message('Atención', 'Este es un mensaje de advertencia') }}

{# Barra de progreso #}
{{ elements.progress_bar(50, 'Cargando...') }}
```

## Ventajas de la Modularización

1. **Mantenibilidad**: Cada componente está en su propio archivo, lo que facilita la localización y modificación de código.
2. **Reutilización**: Los componentes pueden reutilizarse en diferentes partes de la aplicación.
3. **Escalabilidad**: Es más fácil añadir nuevos componentes o modificar los existentes sin afectar al resto del dashboard.
4. **Legibilidad**: El código es más legible y fácil de entender al estar organizado en componentes lógicos.
5. **Colaboración**: Varios desarrolladores pueden trabajar en diferentes componentes simultáneamente sin conflictos.

## Consideraciones para el Futuro

Para mejorar aún más la modularización, se recomienda:

1. **Refactorizar el JavaScript**: Separar el código JavaScript en archivos modulares por funcionalidad.
2. **Reducir estilos en línea**: Utilizar las clases CSS definidas en `dashboard-oficial.css` en lugar de estilos en línea.
3. **Implementar un sistema de gestión de estado**: Para manejar el estado compartido entre componentes.
4. **Añadir documentación de componentes**: Documentar cada componente con ejemplos de uso.

## Compatibilidad

La versión modular mantiene elementos ocultos para compatibilidad con el código JavaScript existente. Esto asegura que todas las funcionalidades sigan funcionando correctamente mientras se realiza la transición a una estructura completamente modular.