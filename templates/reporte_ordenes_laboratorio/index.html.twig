{% extends 'admin/layout.html.twig' %}

{% block title %}| Reporte Graduaciones{% endblock %}

{% block stylesheets %}
    {{ parent() }}
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.7/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="{{ asset('css/reportes/reporte-ordenes-laboratorio.css') }}">
{% endblock %}

{% block content %}
    <div class="container-fluid reporte-ordenes-container">
        <div class="row">
            <div class="col-md-12">
                <!-- Header rediseñado con UI/UX mejorado -->
                <div class="modern-header">
                    <div class="header-content">
                        <div class="header-icon">
                            <i class="fas fa-microscope"></i>
                        </div>
                        <div class="header-text">
                            <h1 class="header-title">Graduaciones</h1>
                            <p class="header-subtitle">Gestión integral de órdenes y seguimiento en tiempo real</p>
                        </div>
                    </div>
                </div>

                <!-- Panel de filtros moderno -->
                <div class="filters-panel">
                    <div class="filters-header">
                        <div class="filters-title">
                            <i class="fas fa-filter"></i>
                            <span>Filtros de Búsqueda</span>
                        </div>
                        <div class="filters-toggle">
                            <button class="toggle-btn" id="toggleFilters">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>

                    <div class="filters-content" id="filtersContent">
                        <!-- Filtros rápidos -->
                        <div class="quick-filters">
                            <div class="quick-filter-item">
                                <label class="filter-label">Folio</label>
                                <input type="text" class="quick-search" id="folioFilter" placeholder="Buscar por número de folio...">
                            </div>
                            <div class="quick-filter-item">
                                <label class="filter-label">Sucursal</label>
                                <select class="quick-select" id="sucursalFilter">
                                    <option value="">Seleccionar sucursal...</option>
                                </select>
                            </div>
                            <div class="quick-filter-item">
                                <label class="filter-label">Estado de Pago</label>
                                <select class="quick-select" id="liquidadaFilter">
                                    <option value="">Seleccionar estado de pago...</option>
                                    <option value="1">Liquidada</option>
                                    <option value="0">Pendiente</option>
                                </select>
                            </div>
                        </div>

                        <!-- Filtros avanzados -->
                        <div class="advanced-filters">
                            <div class="filter-group">
                                <div class="filter-group-header">
                                    <i class="fas fa-calendar-alt"></i>
                                    <span>Rango de Fechas</span>
                                </div>
                                <div class="filter-group-content">
                                    <div class="date-range">
                                        <div class="date-input-group">
                                            <label>Fecha de Venta</label>
                                            <div class="date-inputs">
                                                <input type="date" class="form-control" id="fechaVentaDesde" placeholder="Desde">
                                                <span class="date-separator">hasta</span>
                                                <input type="date" class="form-control" id="fechaVentaHasta" placeholder="Hasta">
                                            </div>
                                        </div>
                                        <div class="date-input-group">
                                            <label>Fecha de Creación</label>
                                            <div class="date-inputs">
                                                <input type="date" class="form-control" id="fechaCreacionDesde" placeholder="Desde">
                                                <span class="date-separator">hasta</span>
                                                <input type="date" class="form-control" id="fechaCreacionHasta" placeholder="Hasta">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-group">
                                <div class="filter-group-header">
                                    <i class="fas fa-user"></i>
                                    <span>Información del Cliente</span>
                                </div>
                                <div class="filter-group-content">
                                    <div class="client-filters">
                                        <div class="client-input-group">
                                            <input type="text" class="form-control" id="nombreClienteFilter" placeholder="Nombre del cliente">
                                        </div>
                                        <div class="client-input-group">
                                            <input type="email" class="form-control" id="emailClienteFilter" placeholder="Correo electrónico">
                                        </div>
                                        <div class="client-input-group">
                                            <input type="text" class="form-control" id="telefonoClienteFilter" placeholder="Número de teléfono">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="filter-group">
                                <div class="filter-group-header">
                                    <i class="fas fa-cog"></i>
                                    <span>Configuración Adicional</span>
                                </div>
                                <div class="filter-group-content">
                                    <div class="additional-filters">
                                        <div class="filter-item">
                                            <label>Tipo de Orden</label>
                                            <select class="form-control" id="cotizacionFilter">
                                                <option value="">Todos los tipos</option>
                                                <option value="0">Venta</option>
                                                <option value="1">Cotización</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Botones de acción -->
                        <div class="filter-actions">
                            <button class="action-btn primary" id="aplicarFiltros">
                                <i class="fas fa-search"></i>
                                <span>Buscar Órdenes</span>
                            </button>
                            <button class="action-btn secondary" id="limpiarFiltros">
                                <i class="fas fa-eraser"></i>
                                <span>Limpiar Filtros</span>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Panel de resultados y visualización -->
                <div class="results-panel">
                    <div class="results-header">
                        <div class="results-info">
                            <div class="results-count">
                                <span class="count-number" id="contadorResultados">0</span>
                            </div>
                            <div class="results-status" id="resultsStatus">
                                <i class="fas fa-circle"></i>
                                <span>Datos actualizados</span>
                            </div>
                        </div>
                        <div class="view-controls">
                            <div class="view-toggle">
                                <button class="view-btn active" id="btnVistaCards" onclick="cambiarVista('cards')" data-view="cards">
                                    <i class="fas fa-th-large"></i>
                                    <span>Tarjetas</span>
                                </button>
                                <button class="view-btn" id="btnVistaTable" onclick="cambiarVista('table')" data-view="table">
                                    <i class="fas fa-table"></i>
                                    <span>Tabla</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                        <!-- Contenedor de tarjetas -->
                        <div id="ordenesContainer" class="cards-container">
                            <!-- Las tarjetas se cargarán aquí dinámicamente -->
                        </div>

                        <!-- Contenedor de tabla -->
                        <div id="tablaContainer" class="table-container" style="display: none;">
                            <div class="table-responsive">
                                <table id="ordenesTable" class="table table-striped table-hover" style="width: 100%;">
                                    <thead>
                                        <tr class="table-secondary">
                                            <th style="width: 80px;">Graduación</th>
                                            <th style="width: 100px;">Folio</th>
                                            <th style="width: 200px;">Cliente</th>
                                            <th style="width: 150px;">Empresa</th>
                                            <th style="width: 120px;">Sucursal</th>
                                            <th style="width: 100px;">Etapa</th>
                                            <th style="width: 120px;">Fecha Creación</th>
                                            <th style="width: 120px;">Fecha Venta</th>
                                            <th style="width: 100px;">Total</th>
                                            <th style="width: 100px;">Liquidada</th>
                                            <th style="width: 180px;" class="text-center">Opciones</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <!-- Los datos se cargarán aquí dinámicamente -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Paginación -->
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div id="infoRegistros" class="text-muted">
                                <!-- Información de registros -->
                            </div>
                            <nav aria-label="Paginación">
                                <ul class="pagination" id="paginacion">
                                    <!-- Botones de paginación -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para detalle de orden -->
    <div class="modal fade" id="detalleModal" tabindex="-1" aria-labelledby="detalleModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="detalleModalLabel">
                        <i class="fas fa-receipt mr-2"></i>Información de la Orden
                    </h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body" id="detalleModalBody">
                    <!-- El contenido se cargará dinámicamente -->
                    <div class="text-center p-4 d-flex flex-column justify-content-center align-items-center" style="min-height: 200px;">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary"></i>
                        <p class="mt-3 mb-0">Cargando detalles...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cerrar</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal para visor de PDF -->
    <div class="modal fade" id="pdfViewerModal" tabindex="-1" aria-labelledby="pdfViewerModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="pdfViewerModalLabel">
                        <i class="fas fa-file-pdf mr-2"></i>Visor de Documento
                    </h5>
                    <div class="modal-header-actions">
                        <button type="button" class="btn btn-sm" id="btnVolverDetalle">
                            <i class="fas fa-arrow-left mr-1"></i> Volver a Detalles
                        </button>
                        <button type="button" class="close ml-3" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
                <div class="modal-body p-0" id="pdfViewerBody">
                    <!-- Loading state -->
                    <div class="pdf-loading text-center p-5">
                        <i class="fas fa-spinner fa-spin fa-3x text-primary"></i>
                        <p class="mt-3 mb-0">Cargando documento PDF...</p>
                    </div>
                    <!-- PDF iframe will be inserted here -->
                </div>
            </div>
        </div>
    </div>
{% endblock %}

{% block javascripts %}
    {{ parent() }}
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/moment.js/2.29.4/moment.min.js"></script>

    <script>
        $(document).ready(function() {
            let currentPage = 1;
            let totalPages = 1;

            // Toggle filtros con animación mejorada
            $('#toggleFilters').click(function() {
                const filtersContent = $('#filtersContent');
                const toggleBtn = $(this);
                const icon = toggleBtn.find('i');

                if (filtersContent.hasClass('show')) {
                    filtersContent.removeClass('show').slideUp(300);
                    icon.removeClass('fa-chevron-up').addClass('fa-chevron-down');
                    toggleBtn.removeClass('active');
                } else {
                    filtersContent.addClass('show').slideDown(300);
                    icon.removeClass('fa-chevron-down').addClass('fa-chevron-up');
                    toggleBtn.addClass('active');
                }
            });

            // Configurar Select2 para sucursales (simplificado para evitar errores)
            setTimeout(function() {
                if ($('#sucursalFilter').length && $('#filtersContent').length) {
                    $('#sucursalFilter').select2({
                        placeholder: 'Seleccionar sucursal',
                        allowClear: true,
                        width: '100%',
                        minimumResultsForSearch: 6,
                ajax: {
                    url: "{{ path('obtener_sucursales_activas_ordenes') }}",
                    dataType: 'json',
                    delay: 250,
                    data: function(params) {
                        return {
                            q: params.term,
                            page: params.page
                        };
                    },
                    processResults: function(data) {
                        return {
                            results: data.items.map(function(item) {
                                return {
                                    id: item.idsucursal,
                                    text: item.nombre
                                };
                            })
                        };
                    },
                    cache: true
                },
                language: {
                    inputTooShort: function() {
                        return "Por favor ingrese 1 o más caracteres";
                    },
                    noResults: function() {
                        return "No se encontraron resultados";
                    },
                    searching: function() {
                        return "Buscando...";
                    },
                    errorLoading: function() {
                        return "Error cargando resultados";
                    }
                }
                    });
                }
            }, 500);

            // Función para obtener el badge de etapa
            function getEtapaBadge(etapa) {
                let badgeClass = 'etapa-default';
                let etapaText = 'Etapa ' + etapa;

                if (etapa == '11') {
                    badgeClass = 'etapa-entregado';
                    etapaText = 'Entregado';
                } else if (etapa == '10') {
                    badgeClass = 'etapa-pendiente';
                    etapaText = 'Pendiente';
                }

                return `<span class="etapa-badge ${badgeClass}">${etapaText}</span>`;
            }

            // Función para obtener la clase del header según etapa
            function getHeaderClass(etapa) {
                if (etapa == '11') {
                    return 'card-header-entregado';
                } else if (etapa == '10') {
                    return 'card-header-pendiente';
                }
                return 'card-header-default';
            }

            // Función para calcular días transcurridos
            function calcularDiasTranscurridos(fechaCreacion) {
                if (!fechaCreacion) return { dias: 0, clase: 'dias-recientes', texto: 'Sin fecha' };

                const ahora = moment();
                const creacion = moment(fechaCreacion);
                const dias = ahora.diff(creacion, 'days');

                let clase = 'dias-recientes';
                let texto = '';

                if (dias <= 3) {
                    clase = 'dias-recientes';
                    texto = `${dias} día${dias !== 1 ? 's' : ''} (Reciente)`;
                } else if (dias <= 7) {
                    clase = 'dias-moderados';
                    texto = `${dias} días (Moderado)`;
                } else {
                    clase = 'dias-antiguos';
                    texto = `${dias} días (Requiere atención)`;
                }

                return { dias, clase, texto };
            }

            // Función para crear una tarjeta de orden
            function createOrdenCard(orden) {
                const nombreCompleto = `${orden.nombreCliente || ''} ${orden.apellidoPaternoCliente || ''} ${orden.apellidoMaternoCliente || ''}`.trim();
                const fechaVenta = orden.fechaVenta ? moment(orden.fechaVenta).format('DD/MM/YYYY') : 'N/A';
                const fechaCreacion = orden.fechaCreacion ? moment(orden.fechaCreacion).format('DD/MM/YYYY') : 'N/A';
                const fechaCreacionOrden = orden.fechaCreacionOrden ? moment(orden.fechaCreacionOrden).format('DD/MM/YYYY HH:mm') : 'N/A';
                const headerClass = getHeaderClass(orden.etapa);
                const tiempoInfo = calcularDiasTranscurridos(orden.fechaCreacionOrden);

                return `
                    <div class="orden-card" data-orden-id="${orden.idstockventaordenlaboratorio}">
                        <div class="${headerClass} p-3">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <h6 class="mb-1">Folio: ${orden.folioVenta || 'N/A'}</h6>
                                    <small class="opacity-75">Autorización: ${orden.authorizationnumber || 'N/A'}</small>
                                </div>
                                <div class="text-right">
                                    ${getEtapaBadge(orden.etapa)}
                                </div>
                            </div>
                        </div>

                        <div class="card-body p-3">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">Cliente:</span>
                                        <span class="info-value">${nombreCompleto || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Empresa:</span>
                                        <span class="info-value">${orden.nombreEmpresaCliente || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Sucursal:</span>
                                        <span class="info-value">${orden.sucursal || 'N/A'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Tipo Venta:</span>
                                        <span class="info-value">${orden.tipoVenta || 'N/A'}</span>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="info-row">
                                        <span class="info-label">Fecha Venta:</span>
                                        <span class="info-value">${fechaVenta}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Total:</span>
                                        <span class="info-value">$${parseFloat(orden.total || 0).toLocaleString('es-MX', {minimumFractionDigits: 2})}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Liquidada:</span>
                                        <span class="info-value">${orden.liquidada == '1' ? 'Sí' : 'No'}</span>
                                    </div>
                                    <div class="info-row">
                                        <span class="info-label">Cambiar Etapa:</span>
                                        <select class="stage-select" onchange="cambiarStage(${orden.idstockventaordenlaboratorio}, this.value)" data-current-stage="${orden.etapa}">
                                            <option value="10" ${orden.etapa == '10' ? 'selected' : ''}>Pendiente</option>
                                            <option value="11" ${orden.etapa == '11' ? 'selected' : ''}>Entregado</option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <!-- Información de tiempo -->
                            <div class="tiempo-info">
                                <div class="tiempo-creacion">
                                    <i class="fas fa-clock"></i> Creada: ${fechaCreacionOrden}
                                </div>
                                <div class="dias-transcurridos ${tiempoInfo.clase}">
                                    <i class="fas fa-calendar-day"></i> ${tiempoInfo.texto}
                                </div>
                            </div>

                            ${createGraduacionSection(orden)}

                            <div class="text-center mt-3">
                                <button class="btn-detalle" onclick="verDetalle(${orden.idstockventaordenlaboratorio})">
                                    <i class="fas fa-eye"></i> Ver Detalle
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Función para crear la sección de graduación
            function createGraduacionSection(orden) {
                if (!orden.esferaDerecha && !orden.esferaIzquierda) {
                    return '';
                }

                return `
                    <div class="graduacion-section">
                        <div class="graduacion-title">
                            <i class="fas fa-eye"></i> Graduación
                        </div>
                        <div class="graduacion-grid">
                            <div class="graduacion-item">
                                <strong>OD Esfera:</strong> ${orden.esferaDerecha || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>OD Cilindro:</strong> ${orden.cilindroDerecho || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>OD Eje:</strong> ${orden.ejeDerecho || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>OI Esfera:</strong> ${orden.esferaIzquierda || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>OI Cilindro:</strong> ${orden.cilindroIzquierdo || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>OI Eje:</strong> ${orden.ejeIzquierdo || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>DIP:</strong> ${orden.distanciaPupilar || 'N/A'}
                            </div>
                            <div class="graduacion-item">
                                <strong>AO:</strong> ${orden.alturaOjos || 'N/A'}
                            </div>
                        </div>
                    </div>
                `;
            }

            // Función para cargar datos
            function cargarDatos(page = 1) {
                const filtros = {
                    fechaVentaDesde: $('#fechaVentaDesde').val(),
                    fechaVentaHasta: $('#fechaVentaHasta').val(),
                    fechaCreacionDesde: $('#fechaCreacionDesde').val(),
                    fechaCreacionHasta: $('#fechaCreacionHasta').val(),
                    cotizacion: $('#cotizacionFilter').val(),
                    folio: $('#folioFilter').val(),
                    sucursal: $('#sucursalFilter').val(),
                    nombreCliente: $('#nombreClienteFilter').val(),
                    emailCliente: $('#emailClienteFilter').val(),
                    telefonoCliente: $('#telefonoClienteFilter').val(),
                    liquidada: $('#liquidadaFilter').val(),
                    page: page
                };

                $.ajax({
                    url: "{{ path('obtener_datos_reporte_ordenes') }}",
                    type: "GET",
                    data: filtros,
                    beforeSend: function() {
                        if (currentView === 'cards') {
                            $('#ordenesContainer').css('display', 'flex').html(`
                                <div class="d-flex flex-column justify-content-center align-items-center text-center p-5 w-100" style="min-height: 300px;">
                                    <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                    <h5 class="text-muted">Cargando órdenes...</h5>
                                    <p class="text-muted mb-0">Obteniendo datos del servidor</p>
                                </div>
                            `);
                        } else {
                            // Para la vista de tabla, mostrar loading en el contenedor
                            $('#tablaContainer').html(`
                                <div class="d-flex flex-column justify-content-center align-items-center text-center p-5 w-100" style="min-height: 300px;">
                                    <i class="fas fa-spinner fa-spin fa-3x text-primary mb-3"></i>
                                    <h5 class="text-muted">Cargando tabla...</h5>
                                    <p class="text-muted mb-0">Preparando datos para visualización</p>
                                </div>
                            `);
                        }
                    },
                    success: function(response) {
                        console.log('Datos recibidos:', response);
                        console.log('Vista actual:', currentView);

                        if (response.data && response.data.length > 0) {
                            if (currentView === 'cards') {
                                // Vista de tarjetas
                                console.log('Cargando vista de tarjetas');
                                let cardsHtml = '';
                                response.data.forEach(function(orden) {
                                    cardsHtml += createOrdenCard(orden);
                                });
                                $('#ordenesContainer').css('display', 'grid').html(cardsHtml);
                            } else {
                                // Vista de tabla
                                console.log('Cargando vista de tabla');
                                poblarTabla(response.data);
                            }

                            // Actualizar información de paginación
                            currentPage = response.pagination.page;
                            totalPages = response.pagination.pages;
                            updatePaginationInfo(response.pagination);
                            updatePaginationButtons();

                            // Actualizar contador de resultados
                            $('#contadorResultados').text(`${response.pagination.total} resultados encontrados`);
                        } else {
                            // No hay datos
                            const noDataHtml = `
                                <div class="d-flex flex-column justify-content-center align-items-center text-center p-5 w-100" style="min-height: 200px;">
                                    <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                                    <h5 class="text-muted">No se encontraron registros</h5>
                                    <p class="text-muted mb-0">Intente ajustar los filtros de búsqueda</p>
                                </div>
                            `;

                            if (currentView === 'cards') {
                                $('#ordenesContainer').css('display', 'flex').html(noDataHtml);
                            } else {
                                $('#tablaContainer').html(noDataHtml);
                            }

                            $('#infoRegistros').text('');
                            $('#paginacion').empty();
                            $('#contadorResultados').text('0 resultados encontrados');
                        }
                    },
                    error: function() {
                        const errorHtml = `
                            <div class="d-flex flex-column justify-content-center align-items-center text-center p-5 w-100" style="min-height: 200px;">
                                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                                <h5 class="text-danger">Error al cargar los datos</h5>
                                <p class="text-muted mb-0">Verifique su conexión e intente nuevamente</p>
                            </div>
                        `;

                        if (currentView === 'cards') {
                            $('#ordenesContainer').css('display', 'flex').html(errorHtml);
                        } else {
                            $('#tablaContainer').html(errorHtml);
                        }

                        $('#contadorResultados').text('Error al cargar datos');
                    }
                });
            }

            // Función para actualizar información de paginación
            function updatePaginationInfo(pagination) {
                const inicio = ((pagination.page - 1) * pagination.limit) + 1;
                const fin = Math.min(pagination.page * pagination.limit, pagination.total);
                $('#infoRegistros').text(`Mostrando ${inicio} a ${fin} de ${pagination.total} registros`);
            }

            // Función para actualizar botones de paginación
            function updatePaginationButtons() {
                let paginationHtml = '';

                // Botón anterior
                if (currentPage > 1) {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="cambiarPagina(${currentPage - 1})">Anterior</a></li>`;
                }

                // Números de página
                for (let i = Math.max(1, currentPage - 2); i <= Math.min(totalPages, currentPage + 2); i++) {
                    const activeClass = i === currentPage ? 'active' : '';
                    paginationHtml += `<li class="page-item ${activeClass}"><a class="page-link" href="#" onclick="cambiarPagina(${i})">${i}</a></li>`;
                }

                // Botón siguiente
                if (currentPage < totalPages) {
                    paginationHtml += `<li class="page-item"><a class="page-link" href="#" onclick="cambiarPagina(${currentPage + 1})">Siguiente</a></li>`;
                }

                $('#paginacion').html(paginationHtml);
            }

            // Función global para cambiar página
            window.cambiarPagina = function(page) {
                cargarDatos(page);
            };

            // Función global para cambiar stage
            window.cambiarStage = function(ordenId, nuevoStage) {
                Swal.fire({
                    title: '¿Confirmar cambio?',
                    text: '¿Está seguro de cambiar el stage de esta orden?',
                    icon: 'question',
                    showCancelButton: true,
                    confirmButtonColor: '#007bff',
                    cancelButtonColor: '#6c757d',
                    confirmButtonText: 'Sí, cambiar',
                    cancelButtonText: 'Cancelar',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Mostrar loading
                        Swal.fire({
                            title: 'Actualizando...',
                            text: 'Cambiando el stage de la orden',
                            icon: 'info',
                            allowOutsideClick: false,
                            showConfirmButton: false,
                            didOpen: () => {
                                Swal.showLoading();
                            }
                        });

                        $.ajax({
                            url: '/reporte/ordenes/laboratorio/cambiar-stage',
                            type: 'POST',
                            data: {
                                ordenId: ordenId,
                                nuevoStage: nuevoStage
                            },
                            success: function(response) {
                                if (response.success) {
                                    // Recargar la tarjeta o toda la página
                                    cargarDatos(currentPage);

                                    // Mostrar mensaje de éxito
                                    Swal.fire({
                                        title: '¡Éxito!',
                                        text: 'Stage actualizado correctamente',
                                        icon: 'success',
                                        confirmButtonColor: '#28a745',
                                        timer: 2000,
                                        showConfirmButton: false
                                    });
                                } else {
                                    Swal.fire({
                                        title: 'Error',
                                        text: 'Error al actualizar el stage: ' + (response.message || 'Error desconocido'),
                                        icon: 'error',
                                        confirmButtonColor: '#dc3545'
                                    });
                                }
                            },
                            error: function() {
                                Swal.fire({
                                    title: 'Error de Conexión',
                                    text: 'No se pudo conectar con el servidor. Intente nuevamente.',
                                    icon: 'error',
                                    confirmButtonColor: '#dc3545'
                                });
                            }
                        });
                    }
                });
            };

            // Variables globales para el sistema de modales
            let currentOrderId = null;
            let currentOrderData = null;

            // Variables para la vista
            let currentView = 'cards'; // 'cards' o 'table'
            let ordenesTable = null;

            // Función para cambiar vista
            window.cambiarVista = function(vista) {
                console.log('Cambiando vista a:', vista);
                currentView = vista;

                // Actualizar botones
                $('#btnVistaCards, #btnVistaTable').removeClass('active');
                if (vista === 'cards') {
                    $('#btnVistaCards').addClass('active');
                    $('#ordenesContainer').show();
                    $('#tablaContainer').hide();
                } else {
                    $('#btnVistaTable').addClass('active');
                    $('#ordenesContainer').hide();
                    $('#tablaContainer').show();
                }

                // Guardar preferencia en sessionStorage
                sessionStorage.setItem('vistaPreferida', vista);

                // Recargar datos en la vista actual
                cargarDatos(currentPage);
            };

            // Función para inicializar DataTable
            function inicializarDataTable() {
                if (ordenesTable) {
                    ordenesTable.destroy();
                }

                ordenesTable = $('#ordenesTable').DataTable({
                    data: [], // Inicializar con array vacío
                    columns: [
                        { title: "Folio" },
                        { title: "Cliente" },
                        { title: "Empresa" },
                        { title: "Sucursal" },
                        { title: "Etapa" },
                        { title: "Fecha Creación" },
                        { title: "Fecha Venta" },
                        { title: "Total" },
                        { title: "Liquidada" },
                        { title: "Acciones" }
                    ],
                    responsive: true,
                    pageLength: 25,
                    lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                    order: [[5, 'desc']], // Ordenar por fecha de creación descendente
                    language: {
                        url: '//cdn.datatables.net/plug-ins/1.13.7/i18n/es-ES.json'
                    },
                    columnDefs: [
                        { targets: [9], orderable: false }, // Columna de acciones no ordenable
                        { targets: [7], className: 'text-end' }, // Total alineado a la derecha
                        { targets: [5, 6], className: 'text-center' }, // Fechas centradas
                        { targets: [8], className: 'text-center' } // Estado centrado
                    ],
                    dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>rtip',
                    searching: false, // Deshabilitamos la búsqueda de DataTable para usar nuestros filtros
                    paging: false, // Deshabilitamos la paginación de DataTable para usar la nuestra
                    info: false // Deshabilitamos la info de DataTable
                });
            }

            // Función para poblar la tabla con datos
            function poblarTabla(ordenes) {
                console.log('Poblando tabla con', ordenes.length, 'órdenes');

                // Guardar los datos actuales para uso en otras funciones
                window.currentTableData = ordenes;

                // Primero, restaurar el HTML de la tabla
                $('#tablaContainer').html(`
                    <div class="table-responsive">
                        <table id="ordenesTable" class="table table-striped table-hover" style="width: 100%;">
                            <thead>
                                <tr class="table-secondary">
                                    <th style="width: 80px;">Graduación</th>
                                    <th style="width: 100px;">Folio</th>
                                    <th style="width: 200px;">Cliente</th>
                                    <th style="width: 150px;">Empresa</th>
                                    <th style="width: 120px;">Sucursal</th>
                                    <th style="width: 100px;">Etapa</th>
                                    <th style="width: 120px;">Fecha Creación</th>
                                    <th style="width: 120px;">Fecha Venta</th>
                                    <th style="width: 100px;">Total</th>
                                    <th style="width: 100px;">Liquidada</th>
                                    <th style="width: 180px;" class="text-center">Opciones</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                `);

                // Guardar los datos para uso en otras funciones
                window.currentTableData = ordenes;

                // Preparar datos para DataTable
                const datosTabla = ordenes.map(function(orden) {
                    // Construir nombre completo del cliente
                    const nombreCompleto = `${orden.nombreCliente || ''} ${orden.apellidoPaternoCliente || ''} ${orden.apellidoMaternoCliente || ''}`.trim();

                    return [
                        getBotonGraduacion(orden.idstockventaordenlaboratorio || 0, orden),
                        orden.folioVenta || 'N/A',
                        nombreCompleto || 'Sin cliente',
                        orden.nombreEmpresaCliente || 'N/A',
                        orden.sucursal || 'N/A',
                        getEtapaBadge(orden.etapa || 0),
                        orden.fechaCreacion ? new Date(orden.fechaCreacion).toLocaleDateString('es-ES') : 'N/A',
                        orden.fechaVenta ? new Date(orden.fechaVenta).toLocaleDateString('es-ES') : 'N/A',
                        orden.total ? '$' + parseFloat(orden.total).toLocaleString('es-ES', {minimumFractionDigits: 2}) : '$0.00',
                        getEstadoBadge(orden.liquidada || 0),
                        getAccionesTabla(orden.idstockventaordenlaboratorio || 0, orden.etapa || 0)
                    ];
                });

                console.log('Datos preparados para DataTable:', datosTabla);

                // Destruir tabla existente si existe
                if (ordenesTable) {
                    ordenesTable.destroy();
                    ordenesTable = null;
                }

                // Inicializar DataTable con datos
                try {
                    ordenesTable = $('#ordenesTable').DataTable({
                        data: datosTabla,
                        columns: [
                            { title: "Graduación", width: "80px" },
                            { title: "Folio", width: "100px" },
                            { title: "Cliente", width: "200px" },
                            { title: "Empresa", width: "150px" },
                            { title: "Sucursal", width: "120px" },
                            { title: "Etapa", width: "100px" },
                            { title: "Fecha Creación", width: "120px" },
                            { title: "Fecha Venta", width: "120px" },
                            { title: "Total", width: "100px" },
                            { title: "Liquidada", width: "100px" },
                            { title: "Opciones", width: "180px" }
                        ],
                        responsive: false,
                        pageLength: 25,
                        lengthMenu: [[10, 25, 50, 100], [10, 25, 50, 100]],
                        order: [[1, 'desc']], // Ordenar por folio descendente
                        columnDefs: [
                            { targets: [0, 10], orderable: false }, // Graduación y Acciones no ordenables
                            { targets: [8], className: 'text-end' }, // Total alineado a la derecha
                            { targets: [6, 7], className: 'text-center' }, // Fechas centradas
                            { targets: [9], className: 'text-center' }, // Estado centrado
                            { targets: [10], className: 'text-center' } // Opciones centradas
                        ],
                        searching: false,
                        paging: false,
                        info: false,
                        autoWidth: false,
                        scrollX: true,
                        language: {
                            "emptyTable": "No hay datos disponibles en la tabla",
                            "zeroRecords": "No se encontraron registros coincidentes"
                        }
                    });
                    console.log('DataTable inicializada correctamente');
                } catch (error) {
                    console.error('Error al inicializar DataTable:', error);
                    $('#tablaContainer').html(`
                        <div class="alert alert-danger">
                            <h5>Error al cargar la tabla</h5>
                            <p>Hubo un problema al inicializar la vista de tabla. Intente cambiar a vista de tarjetas.</p>
                        </div>
                    `);
                }
            }

            // Función para generar badge de etapa
            function getEtapaBadge(etapa) {
                const badges = {
                    0: '<span class="badge bg-secondary">Sin Asignar</span>',
                    1: '<span class="badge bg-info">Recibido</span>',
                    2: '<span class="badge bg-warning">En Proceso</span>',
                    3: '<span class="badge bg-primary">Laboratorio</span>',
                    4: '<span class="badge bg-success">Terminado</span>',
                    5: '<span class="badge bg-dark">Entregado</span>',
                    10: '<span class="badge bg-warning">Pendiente</span>',
                    11: '<span class="badge bg-success">Entregado</span>'
                };
                return badges[etapa] || '<span class="badge bg-secondary">Desconocido</span>';
            }

            // Función para generar badge de estado
            function getEstadoBadge(liquidada) {
                return liquidada == 1
                    ? '<span class="badge bg-success">Liquidada</span>'
                    : '<span class="badge bg-warning">Pendiente</span>';
            }

            // Función para generar botón de graduación
            function getBotonGraduacion(ordenId, orden) {
                return `
                    <button class="btn-expand-graduacion" onclick="toggleGraduacion(${ordenId})" title="Ver/Ocultar Graduación">
                        <i class="fas fa-chevron-right" id="icon-${ordenId}"></i>
                    </button>
                `;
            }

            // Función para generar detalles de graduación
            function getDetallesGraduacion(orden) {
                return `
                    <div class="graduacion-grid">
                        <div class="graduacion-item">
                            <label>Esfera OD</label>
                            <div class="value">${orden.esferaDerecha || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Cilindro OD</label>
                            <div class="value">${orden.cilindroDerecho || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Eje OD</label>
                            <div class="value">${orden.ejeDerecho || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Esfera OI</label>
                            <div class="value">${orden.esferaIzquierda || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Cilindro OI</label>
                            <div class="value">${orden.cilindroIzquierdo || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Eje OI</label>
                            <div class="value">${orden.ejeIzquierdo || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Distancia Pupilar</label>
                            <div class="value">${orden.distanciaPupilar || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Altura de Ojos</label>
                            <div class="value">${orden.alturaOjos || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>ACO</label>
                            <div class="value">${orden.ACO || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>AV Cerca Add OI</label>
                            <div class="value">${orden.AVCercaAddOI || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>AV Cerca Add OD</label>
                            <div class="value">${orden.AVCercaAddOD || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Diagnóstico</label>
                            <div class="value">${orden.Diagnostico || 'N/A'}</div>
                        </div>
                        <div class="graduacion-item">
                            <label>Notas</label>
                            <div class="value">${orden.Notas || 'N/A'}</div>
                        </div>
                    </div>
                `;
            }

            // Función para generar detalles de graduación en modal (UI/UX mejorado)
            function getDetallesGraduacionModal(orden) {
                return `
                    <div class="graduacion-modal-simple">
                        <!-- Sección principal de graduación -->
                        <div class="graduacion-eyes-section">
                            <div class="row g-4">
                                <!-- Ojo Derecho -->
                                <div class="col-md-6">
                                    <div class="eye-card eye-right">
                                        <div class="eye-header">
                                            <i class="fas fa-eye"></i>
                                            <h5>Ojo Derecho (OD)</h5>
                                        </div>
                                        <div class="eye-values">
                                            <div class="value-item">
                                                <span class="label">Esfera</span>
                                                <span class="value">${orden.esferaDerecha || '--'}</span>
                                            </div>
                                            <div class="value-item">
                                                <span class="label">Cilindro</span>
                                                <span class="value">${orden.cilindroDerecho || '--'}</span>
                                            </div>
                                            <div class="value-item">
                                                <span class="label">Eje</span>
                                                <span class="value">${orden.ejeDerecho || '--'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Ojo Izquierdo -->
                                <div class="col-md-6">
                                    <div class="eye-card eye-left">
                                        <div class="eye-header">
                                            <i class="fas fa-eye"></i>
                                            <h5>Ojo Izquierdo (OI)</h5>
                                        </div>
                                        <div class="eye-values">
                                            <div class="value-item">
                                                <span class="label">Esfera</span>
                                                <span class="value">${orden.esferaIzquierda || '--'}</span>
                                            </div>
                                            <div class="value-item">
                                                <span class="label">Cilindro</span>
                                                <span class="value">${orden.cilindroIzquierdo || '--'}</span>
                                            </div>
                                            <div class="value-item">
                                                <span class="label">Eje</span>
                                                <span class="value">${orden.ejeIzquierdo || '--'}</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Medidas adicionales -->
                        <div class="additional-measures">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <div class="measure-card">
                                        <div class="measure-icon">
                                            <i class="fas fa-arrows-alt-h"></i>
                                        </div>
                                        <div class="measure-content">
                                            <span class="measure-label">Distancia Pupilar</span>
                                            <span class="measure-value">${orden.distanciaPupilar || '--'}</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="measure-card">
                                        <div class="measure-icon">
                                            <i class="fas fa-arrows-alt-v"></i>
                                        </div>
                                        <div class="measure-content">
                                            <span class="measure-label">Altura de Ojos</span>
                                            <span class="measure-value">${orden.alturaOjos || '--'}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Información adicional (solo si existe) -->
                        ${(orden.Diagnostico || orden.Notas) ? `
                            <div class="additional-info">
                                ${orden.Diagnostico ? `
                                    <div class="info-item">
                                        <div class="info-header">
                                            <i class="fas fa-stethoscope"></i>
                                            <span>Diagnóstico</span>
                                        </div>
                                        <div class="info-content">${orden.Diagnostico}</div>
                                    </div>
                                ` : ''}
                                ${orden.Notas ? `
                                    <div class="info-item">
                                        <div class="info-header">
                                            <i class="fas fa-sticky-note"></i>
                                            <span>Notas</span>
                                        </div>
                                        <div class="info-content">${orden.Notas}</div>
                                    </div>
                                ` : ''}
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            // Función para mostrar graduación en modal
            window.toggleGraduacion = function(ordenId) {
                // Buscar los datos de la orden en la respuesta actual
                let orden = null;

                // Buscar en los datos actuales de la tabla
                if (window.currentTableData) {
                    orden = window.currentTableData.find(o => o.idstockventaordenlaboratorio == ordenId);
                }

                if (!orden) {
                    Swal.fire({
                        title: 'Error',
                        text: 'No se pudieron cargar los datos de graduación',
                        icon: 'error'
                    });
                    return;
                }

                const modalContent = `
                    <div class="graduacion-modal-wrapper">
                        ${getDetallesGraduacionModal(orden)}
                        <div class="modal-actions">
                            <div class="d-flex gap-3 justify-content-center">
                                <button class="btn btn-success btn-lg" onclick="verTicketGraduacion('${orden.folioVenta}', ${orden.idstockventaordenlaboratorio})">
                                    <i class="fas fa-file-pdf me-2"></i>Ticket de Graduación
                                </button>
                                <button class="btn btn-secondary btn-lg" onclick="cerrarModalGraduacion()">
                                    <i class="fas fa-times me-2"></i>Cerrar
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                Swal.fire({
                    title: `<div class="modal-title-custom">
                        <i class="fas fa-glasses me-2"></i>
                        <span>Graduación - Folio ${orden.folioVenta || 'N/A'}</span>
                    </div>`,
                    html: modalContent,
                    width: '800px',
                    showCloseButton: false,
                    showConfirmButton: false,
                    allowOutsideClick: true,
                    allowEscapeKey: true,
                    customClass: {
                        popup: 'graduacion-modal-popup',
                        title: 'graduacion-modal-title',
                        htmlContainer: 'graduacion-modal-container'
                    },
                    didOpen: () => {
                        // Agregar animación de entrada
                        const popup = Swal.getPopup();
                        popup.style.animation = 'modalFadeIn 0.3s ease-out';
                    }
                });
            };

            // Función para generar acciones de tabla
            function getAccionesTabla(ordenId, etapa) {
                return `
                    <div class="table-actions d-flex flex-column gap-1">
                        <button class="btn btn-sm btn-primary w-100" onclick="verDetalle(${ordenId})" title="Ver Detalles de la Venta">
                            <i class="fas fa-eye me-1"></i>Ver Detalle
                        </button>
                        <div class="dropdown w-100">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle w-100" type="button" data-toggle="dropdown">
                                <i class="fas fa-exchange-alt me-1"></i>Cambiar Etapa
                            </button>
                            <div class="dropdown-menu w-100">
                                <h6 class="dropdown-header">Etapas Disponibles</h6>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 0)">
                                    <i class="fas fa-circle text-secondary me-2"></i>Sin Asignar
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 1)">
                                    <i class="fas fa-circle text-info me-2"></i>Recibido
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 2)">
                                    <i class="fas fa-circle text-warning me-2"></i>En Proceso
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 3)">
                                    <i class="fas fa-circle text-primary me-2"></i>Laboratorio
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 4)">
                                    <i class="fas fa-circle text-success me-2"></i>Terminado
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 5)">
                                    <i class="fas fa-circle text-dark me-2"></i>Entregado
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 10)">
                                    <i class="fas fa-circle text-warning me-2"></i>Pendiente
                                </a>
                                <a class="dropdown-item" href="#" onclick="cambiarStage(${ordenId}, 11)">
                                    <i class="fas fa-circle text-success me-2"></i>Entregado
                                </a>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Función para ver ticket de graduación con flujo mejorado
            window.verTicketGraduacion = function(folio, ordenId) {
                // Guardar los datos de la orden actual para poder regresar
                const ordenActual = window.currentTableData ?
                    window.currentTableData.find(o => o.idstockventaordenlaboratorio == ordenId) : null;

                // Cerrar el modal de graduación
                Swal.close();

                // Mostrar loading mientras se carga el ticket
                Swal.fire({
                    title: 'Cargando Ticket de Graduación',
                    html: `
                        <div class="d-flex flex-column align-items-center p-4">
                            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                            <p class="mb-0">Generando ticket para el folio ${folio}...</p>
                        </div>
                    `,
                    showConfirmButton: false,
                    allowOutsideClick: false,
                    allowEscapeKey: false,
                    didOpen: () => {
                        // Después de un breve momento, mostrar el ticket
                        setTimeout(() => {
                            Swal.close();
                            mostrarTicketConRegreso(folio, ordenId, ordenActual);
                        }, 800);
                    }
                });
            };

            // Función para mostrar ticket con opción de regresar a graduación
            function mostrarTicketConRegreso(folio, ordenId, ordenActual) {
                // Mostrar el ticket de graduación
                mostrarPDFEnModal(folio, 'graduacion', 1);

                // Agregar botón de regreso al modal del PDF
                setTimeout(() => {
                    const pdfModal = document.getElementById('pdfViewerModal');
                    if (pdfModal && ordenActual) {
                        // Buscar el botón de volver detalle y modificarlo
                        const btnVolver = document.getElementById('btnVolverDetalle');
                        if (btnVolver) {
                            // Cambiar el texto y la función del botón
                            btnVolver.innerHTML = '<i class="fas fa-glasses me-2"></i>Regresar a Graduación';
                            btnVolver.onclick = function() {
                                // Cerrar el modal del PDF
                                $('#pdfViewerModal').modal('hide');

                                // Esperar a que se cierre y mostrar graduación nuevamente
                                setTimeout(() => {
                                    toggleGraduacion(ordenId);
                                }, 300);
                            };
                        }
                    }
                }, 1000);
            }

            // Función para cerrar el modal de graduación
            window.cerrarModalGraduacion = function() {
                // Múltiples métodos para asegurar que se cierre
                try {
                    // Método 1: Cerrar SweetAlert2
                    if (typeof Swal !== 'undefined') {
                        Swal.close();
                    }

                    // Método 2: Buscar y remover el modal manualmente
                    setTimeout(() => {
                        const swalContainer = document.querySelector('.swal2-container');
                        if (swalContainer) {
                            swalContainer.remove();
                        }

                        // Método 3: Remover backdrop si existe
                        const backdrop = document.querySelector('.swal2-backdrop-show');
                        if (backdrop) {
                            backdrop.remove();
                        }

                        // Método 4: Restaurar scroll del body
                        document.body.classList.remove('swal2-shown', 'swal2-height-auto');
                        document.body.style.overflow = '';
                        document.body.style.paddingRight = '';

                        // Método 5: Remover cualquier overlay restante
                        const overlays = document.querySelectorAll('.swal2-container, .swal2-backdrop');
                        overlays.forEach(overlay => overlay.remove());

                    }, 100);

                } catch (error) {
                    console.log('Error cerrando modal:', error);
                    // Forzar recarga de página como último recurso
                    // location.reload();
                }
            };

            // Función original para mostrar ticket (mantener compatibilidad)
            window.mostrarTicketGraduacion = function(folio, ordenId) {
                verTicketGraduacion(folio, ordenId);
            };

            // Función global para ver detalle en modal
            window.verDetalle = function(id) {
                currentOrderId = id;
                $('#detalleModal').modal('show');

                // Mostrar loading
                $('#detalleModalBody').html(`
                    <div class="d-flex flex-column justify-content-center align-items-center text-center p-4" style="min-height: 200px;">
                        <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
                        <h6 class="text-muted">Cargando detalles del ticket...</h6>
                        <small class="text-muted">Obteniendo información de la orden</small>
                    </div>
                `);

                // Cargar contenido del modal usando la nueva ruta
                $.ajax({
                    url: "{{ path('reporte_modal_detalle_orden', {'id': '__ID__'}) }}".replace('__ID__', id),
                    type: 'GET',
                    success: function(response) {
                        $('#detalleModalBody').html(response);
                    },
                    error: function() {
                        $('#detalleModalBody').html(`
                            <div class="text-center p-4 text-danger">
                                <i class="fas fa-exclamation-triangle fa-2x"></i>
                                <p class="mt-2">Error al cargar los detalles del ticket</p>
                                <button class="btn btn-outline-primary mt-2" onclick="verDetalle(${id})">
                                    <i class="fas fa-redo"></i> Reintentar
                                </button>
                            </div>
                        `);
                    }
                });
            };

            // Función para abrir el visor de PDF
            window.abrirPDFVisor = function(folio, opcion, empresaid) {
                console.log('abrirPDFVisor llamado con:', {folio, opcion, empresaid});

                // Cerrar el modal actual
                $('#detalleModal').modal('hide');

                // Usar setTimeout para asegurar que el modal se cierre antes de abrir el nuevo
                setTimeout(function() {
                    console.log('Abriendo modal PDF después del timeout');
                    mostrarPDFEnModal(folio, opcion, empresaid);
                }, 300); // 300ms debería ser suficiente para la transición
            };

            // Función para mostrar PDF en modal
            function mostrarPDFEnModal(folio, opcion, empresaid) {
                console.log('mostrarPDFEnModal llamado con:', {folio, opcion, empresaid});

                // Configurar el título del modal según el tipo
                const titulo = opcion === 'ticket' ? 'Ticket Completo' : 'Ticket de Graduación';
                $('#pdfViewerModalLabel').html(`<i class="fas fa-file-pdf mr-2"></i>${titulo}`);

                // Mostrar estado de carga
                $('#pdfViewerBody').html(`
                    <div class="pdf-loading">
                        <i class="fas fa-file-pdf fa-4x"></i>
                        <p>Cargando ${titulo}...</p>
                        <small>Preparando el documento para visualización</small>
                        <div class="mt-3">
                            <div class="spinner-border text-primary" role="status">
                                <span class="sr-only">Cargando...</span>
                            </div>
                        </div>
                    </div>
                `);

                // Abrir el modal
                $('#pdfViewerModal').modal('show');

                // Construir la URL del PDF
                const pdfUrl = "{{ path('visor-documentos', {'folio': '__FOLIO__', 'opcion': '__OPCION__', 'idempresa': '__EMPRESA__'}) }}"
                    .replace('__FOLIO__', folio)
                    .replace('__OPCION__', opcion)
                    .replace('__EMPRESA__', empresaid);

                console.log('URL del PDF construida:', pdfUrl);

                // Crear iframe para mostrar el PDF
                setTimeout(() => {
                    console.log('Creando iframe con URL:', pdfUrl);
                    const iframe = $('<iframe>', {
                        src: pdfUrl,
                        class: 'pdf-iframe',
                        frameborder: '0'
                    });

                    // Manejar carga exitosa
                    iframe.on('load', function() {
                        console.log('PDF cargado exitosamente');
                        $('#pdfViewerBody').html(iframe);

                        // Mostrar toast de éxito
                        Swal.fire({
                            toast: true,
                            position: 'top-end',
                            icon: 'success',
                            title: 'Documento cargado correctamente',
                            showConfirmButton: false,
                            timer: 2000,
                            timerProgressBar: true
                        });
                    });

                    // Manejar errores de carga
                    iframe.on('error', function() {
                        console.log('Error al cargar PDF');
                        mostrarErrorPDF(folio, opcion, empresaid);
                    });

                    // Timeout para detectar problemas de carga
                    setTimeout(() => {
                        if ($('#pdfViewerBody .pdf-loading').length > 0) {
                            console.log('Timeout: PDF no se cargó en 10 segundos');
                            mostrarErrorPDF(folio, opcion, empresaid);
                        }
                    }, 10000); // 10 segundos timeout

                    // Iniciar la carga del iframe
                    $('#pdfViewerBody').html(iframe);
                }, 500);
            }

            // Función para mostrar error en el visor de PDF
            function mostrarErrorPDF(folio, opcion, empresaid) {
                const tipoDocumento = opcion === 'ticket' ? 'Ticket Completo' : 'Ticket de Graduación';

                // Cerrar el modal del PDF
                $('#pdfViewerModal').modal('hide');

                // Mostrar SweetAlert con opciones
                Swal.fire({
                    title: 'Documento No Disponible',
                    html: `
                        <p>El <strong>${tipoDocumento}</strong> para el folio <strong>${folio}</strong> no se encuentra disponible en este momento.</p>
                        <br>
                        <p style="text-align: left; color: #6c757d;">Esto puede deberse a que:</p>
                        <ul style="text-align: left; color: #6c757d; margin: 1rem 0;">
                            <li>El documento aún no ha sido generado</li>
                            <li>El archivo se encuentra en proceso</li>
                            <li>Hay un problema temporal con el servidor</li>
                        </ul>
                    `,
                    icon: 'warning',
                    showCancelButton: true,
                    showDenyButton: true,
                    confirmButtonText: '<i class="fas fa-redo"></i> Reintentar',
                    denyButtonText: '<i class="fas fa-arrow-left"></i> Volver a Detalles',
                    cancelButtonText: 'Cerrar',
                    confirmButtonColor: '#007bff',
                    denyButtonColor: '#6c757d',
                    cancelButtonColor: '#dc3545',
                    reverseButtons: true
                }).then((result) => {
                    if (result.isConfirmed) {
                        // Reintentar cargar el PDF
                        mostrarPDFEnModal(folio, opcion, empresaid);
                    } else if (result.isDenied) {
                        // Volver al modal de detalles
                        setTimeout(() => {
                            if (currentOrderId) {
                                $('#detalleModal').modal('show');
                            }
                        }, 300);
                    }
                });
            }

            // Función para volver al modal de detalles
            $(document).on('click', '#btnVolverDetalle', function() {
                console.log('Volviendo al modal de detalles');

                // Cerrar el modal del PDF
                $('#pdfViewerModal').modal('hide');

                // Usar setTimeout para asegurar que el modal se cierre antes de abrir el de detalles
                setTimeout(function() {
                    if (currentOrderId) {
                        console.log('Reabriendo modal de detalles para orden:', currentOrderId);
                        $('#detalleModal').modal('show');
                    }
                }, 300);
            });

            // Event listeners
            $('#aplicarFiltros').click(function() {
                cargarDatos(1);
            });

            $('#limpiarFiltros').click(function() {
                $('.filtros-container input, .filtros-container select').val('');
                $('#sucursalFilter').val(null).trigger('change');
                cargarDatos(1);
            });

            // Restaurar vista preferida
            const vistaPreferida = sessionStorage.getItem('vistaPreferida') || 'cards';
            if (vistaPreferida === 'table') {
                cambiarVista('table');
            }

            // Cargar datos iniciales
            cargarDatos(1);
        });
    </script>
{% endblock %}
