<style>
    .ticket-container {
        background: white;
        font-family: Arial, sans-serif;
        color: #333;
    }

    .ticket-header {
        background: #f8f9fa;
        border-bottom: 2px solid #dee2e6;
        padding: 15px 20px;
        text-align: center;
    }

    .ticket-number {
        font-size: 1.3rem;
        font-weight: bold;
        color: #495057;
        margin-bottom: 5px;
    }

    .ticket-dates {
        font-size: 0.9rem;
        color: #6c757d;
    }

    .ticket-body {
        padding: 20px;
    }

    .section {
        margin-bottom: 20px;
        padding: 15px;
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 4px;
    }

    .section-title {
        font-weight: bold;
        color: #495057;
        margin-bottom: 12px;
        font-size: 1rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
        border-bottom: 1px solid #dee2e6;
        padding-bottom: 5px;
    }
    
    .info-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 8px;
        padding: 5px 0;
    }
    
    .info-label {
        font-weight: 600;
        color: #6c757d;
        flex: 0 0 40%;
    }
    
    .info-value {
        color: #495057;
        flex: 1;
        text-align: right;
    }
    
    .status-badge {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        border: 1px solid;
    }

    .status-activa {
        background: #e8f5e9;
        color: #2e7d32;
        border-color: #4caf50;
    }

    .status-cotizacion {
        background: #fff8e1;
        color: #f57c00;
        border-color: #ff9800;
    }
    
    .productos-table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 10px;
    }
    
    .productos-table th,
    .productos-table td {
        padding: 10px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }
    
    .productos-table th {
        background: #e9ecef;
        font-weight: 600;
        color: #495057;
        border-bottom: 2px solid #dee2e6;
    }

    .productos-table tr:hover {
        background: #f8f9fa;
    }
    
    .graduacion-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 10px;
        margin-top: 10px;
    }
    
    .graduacion-item {
        background: white;
        padding: 8px 12px;
        border-radius: 3px;
        border: 1px solid #dee2e6;
    }

    .graduacion-label {
        font-size: 0.8rem;
        color: #6c757d;
        margin-bottom: 2px;
        font-weight: 500;
    }

    .graduacion-value {
        font-weight: 600;
        color: #495057;
    }
    
    .no-data {
        text-align: center;
        color: #6c757d;
        font-style: italic;
        padding: 20px;
    }

    .pdf-buttons {
        text-align: center;
        margin-top: 20px;
        padding-top: 15px;
        border-top: 1px solid #dee2e6;
    }

    .btn-pdf {
        display: inline-block;
        padding: 8px 16px;
        margin: 0 5px;
        background: #f8f9fa;
        color: #495057;
        text-decoration: none;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        font-size: 0.9rem;
        font-weight: 500;
        transition: all 0.3s ease;
        cursor: pointer;
        font-family: inherit;
    }

    .btn-pdf:hover {
        background: #e9ecef;
        color: #495057;
        text-decoration: none;
        border-color: #adb5bd;
        transform: translateY(-1px);
    }

    .btn-pdf:active {
        transform: translateY(0);
    }

    .btn-pdf i {
        margin-right: 5px;
    }
</style>

<div class="ticket-container">
    <!-- Header del Ticket -->
    <div class="ticket-header">
        <div class="ticket-number">Folio: {{ venta.folio }}</div>
        <div class="ticket-dates">
            Creación: {{ venta.fechacreacion ? venta.fechacreacion|date('d/m/Y H:i') : 'N/A' }} |
            Venta: {{ venta.fechaventa ? venta.fechaventa|date('d/m/Y') : 'N/A' }}
        </div>
    </div>

    <div class="ticket-body">
        <!-- Datos de Venta -->
        <div class="section">
            <div class="section-title">Datos de Venta</div>
            <div class="info-row">
                <span class="info-label">Venta:</span>
                <span class="info-value">
                    {% if venta.cotizacion == 1 %}
                        <span class="status-badge status-cotizacion">Cotización</span>
                    {% else %}
                        <span class="status-badge status-activa">Activa</span>
                    {% endif %}
                </span>
            </div>
            <div class="info-row">
                <span class="info-label">Folio:</span>
                <span class="info-value">{{ venta.folio }}</span>
            </div>
            {% if venta.authorizationnumber %}
            <div class="info-row">
                <span class="info-label">Autorización:</span>
                <span class="info-value">{{ venta.authorizationnumber }}</span>
            </div>
            {% endif %}
            <div class="info-row">
                <span class="info-label">Cliente:</span>
                <span class="info-value">{{ venta.clienteIdcliente.nombre }} {{ venta.clienteIdcliente.apellidopaterno }} {{ venta.clienteIdcliente.apellidomaterno }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Empresa:</span>
                <span class="info-value">{{ venta.clienteIdcliente.empresaclienteIdempresacliente.nombre }}</span>
            </div>
        </div>

        <!-- Sucursal y Vendedor -->
        <div class="section">
            <div class="section-title">Sucursal & Vendedor</div>
            <div class="info-row">
                <span class="info-label">Sucursal:</span>
                <span class="info-value">{{ venta.sucursalIdsucursal.nombre }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">Vendedor:</span>
                <span class="info-value">{{ venta.usuarioIdusuario ? venta.usuarioIdusuario.nombre : 'N/A' }}</span>
            </div>
        </div>

        <!-- Graduación -->
        {% if ordenLaboratorio %}
        <div class="section">
            <div class="section-title">Graduación</div>
            <div class="graduacion-grid">
                <div class="graduacion-item">
                    <div class="graduacion-label">OD Esfera:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.esferaod ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">OD Cilindro:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.cilindrood ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">OD Eje:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.ejeod ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">OI Esfera:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.esferaoi ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">OI Cilindro:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.cilindrooi ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">OI Eje:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.ejeoi ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">DIP:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.dip ?: 'N/A' }}</div>
                </div>
                <div class="graduacion-item">
                    <div class="graduacion-label">AO:</div>
                    <div class="graduacion-value">{{ ordenLaboratorio.ao ?: 'N/A' }}</div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Productos -->
        <div class="section">
            <div class="section-title">Productos</div>
            {% if productos is not empty %}
                <table class="productos-table">
                    <thead>
                        <tr>
                            <th>Artículo</th>
                            <th>Cant.</th>
                            <th>U.Precio</th>
                            <th>Total</th>
                            <th>Estado</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for producto in productos %}
                        <tr>
                            <td>
                                <strong>{{ producto.modelo }}</strong><br>
                                <small>{{ producto.descripcion }}</small><br>
                                <small class="text-muted">{{ producto.codigobarras }}</small>
                            </td>
                            <td>{{ producto.cantidad }}</td>
                            <td>${{ producto.precio|number_format(2, '.', ',') }}</td>
                            <td>${{ producto.preciofinal|number_format(2, '.', ',') }}</td>
                            <td>
                                {% if producto.status == 1 %}
                                    <span class="status-badge status-activa">Activo</span>
                                {% else %}
                                    <span class="status-badge">Inactivo</span>
                                {% endif %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% else %}
                <div class="no-data">No hay productos registrados</div>
            {% endif %}
        </div>

        <!-- Totales -->
        <div class="section">
            <div class="section-title">Detalle de la Venta</div>
            <div class="info-row">
                <span class="info-label">Subtotal:</span>
                <span class="info-value">${{ ((venta.pagado - venta.iva)|default(0))|number_format(2, '.', ',') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label">IVA ({{ venta.porcentajeiva|default(0) }}%):</span>
                <span class="info-value">${{ venta.iva|default(0)|number_format(2, '.', ',') }}</span>
            </div>
            <div class="info-row">
                <span class="info-label"><strong>Total:</strong></span>
                <span class="info-value"><strong>${{ venta.pagado|default(0)|number_format(2, '.', ',') }}</strong></span>
            </div>
            <div class="info-row">
                <span class="info-label">Estado:</span>
                <span class="info-value">
                    {% if venta.liquidada == 1 %}
                        <span class="status-badge status-activa">Liquidada</span>
                    {% else %}
                        <span class="status-badge status-cotizacion">Pendiente</span>
                    {% endif %}
                </span>
            </div>
            {% if venta.deuda > 0 %}
            <div class="info-row">
                <span class="info-label">Deuda:</span>
                <span class="info-value" style="color: #dc3545;">${{ venta.deuda|number_format(2, '.', ',') }}</span>
            </div>
            {% endif %}
        </div>

        <!-- Pagos -->
        {% if pagos is not empty %}
        <div class="section">
            <div class="section-title">Pagos</div>
            <table class="productos-table">
                <thead>
                    <tr>
                        <th>Fecha</th>
                        <th>Tipo</th>
                        <th>Monto</th>
                        <th>Meses</th>
                    </tr>
                </thead>
                <tbody>
                    {% for pago in pagos %}
                    <tr>
                        <td>{{ pago.fecha|date('d/m/Y') }}</td>
                        <td>{{ pago.tipopago }}</td>
                        <td>${{ pago.monto|number_format(2, '.', ',') }}</td>
                        <td>{{ pago.mesesintereses ?: 'N/A' }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- Botones para PDFs -->
        <div class="pdf-buttons">
            <button type="button" class="btn-pdf" onclick="abrirPDFVisor('{{ venta.folio }}', 'ticket', {{ empresaid }})">
                <i class="fas fa-file-pdf"></i>Ticket Completo
            </button>
            <button type="button" class="btn-pdf" onclick="abrirPDFVisor('{{ venta.folio }}', 'graduacion', {{ empresaid }})">
                <i class="fas fa-eye"></i>Ticket Graduación
            </button>
        </div>
    </div>
</div>
