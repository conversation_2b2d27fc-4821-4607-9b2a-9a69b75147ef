document.addEventListener('DOMContentLoaded', function () {
    const cpInput = document.getElementById('codigo_postal');
    const estadoSelect = document.getElementById('estado');
    const municipioSelect = document.getElementById('municipio_delegacion');
    const ciudadSelect = document.getElementById('ciudad');

    if (!cpInput || !estadoSelect || !municipioSelect || !ciudadSelect) return;

    cpInput.addEventListener('blur', function () {
        const cp = cpInput.value.trim();
        if (cp.length < 5) return;

        fetch(`/api/codigos-postales?cp=${cp}`)
            .then(res => res.json())
            .then(data => {
                if (data.estado && data.municipio && data.ciudad) {
                    // Estado
                    estadoSelect.innerHTML = `<option value="${data.estado}" selected>${data.estado}</option>`;

                    // Municipio
                    municipioSelect.innerHTML = `<option value="${data.municipio}" selected>${data.municipio}</option>`;

                    // Ciudad
                    ciudadSelect.innerHTML = `<option value="${data.ciudad}" selected>${data.ciudad}</option>`;
                } else {
                    alert('Código postal no encontrado.');
                }
            })
            .catch(error => {
                console.error(error);
                alert('Error consultando código postal');
            });
    });
});
