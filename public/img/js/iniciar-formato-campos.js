
$(document).ready(function(){
  

/*$('.valor-pesos').number( true, 2,".",",","" );
$('.valor-pesos-4').number( true, 4,".","," );
$('.valor-entero').number( true, 0 ,".",",");
$('.valor-decimal').number( true, 2 ,".",",");
$('.valor-porcentaje').number( true, 2 ,".",",");*/

/*
$('.valor-pesos').blur(function() {

var valor = parseFloat(  $(this).val().replace(/,/g, ""))
                 .toFixed(2)
                 .toString()
                 .replace(/\B(?=(\d{3})+(?!\d))/g, ",");

// document.getElementById("display").value = this.value.replace(/,/g, "")
$(this).val("$"+valor.replace(/,/g, ""));
*//*
$('.valor-pesos').maskMoney({prefix:'$', allowNegative: true, thousands:',', decimal:'.', affixesStay: true});
$('.valor-pesos-4').maskMoney({prefix:'$', allowNegative: true, thousands:',', decimal:'.', affixesStay: true,precision:4});
$('.valor-porcentaje').maskMoney({suffix:'%', allowNegative: true, thousands:',', decimal:'.', affixesStay: true,precision:2});
$('.valor-decimal').maskMoney({allowNegative: true, thousands:',', decimal:'.', affixesStay: true,precision:2});
$('.valor-entero').maskMoney({allowNegative: true, thousands:',', decimal:'.', affixesStay: true,precision:0});*/
$(".valor-pesos").on({
    keyup: function() {
      formattCurrency($(this),"",2);
    },
    blur: function() {
      formattCurrency($(this), "blur",2);
    }

});
$(".valor-porcentaje").on({
    keyup: function() {
      formattCurrency($(this), "",2,"","");
    },
    blur: function() {
      formattCurrency($(this), "blur",2,"","%");
    },
    focus: function() {
      formattCurrency($(this), "blur",2,"","");

    },

});
$(".valor-pesos-4").on({
  keyup: function() {
    formattCurrency($(this),"",4);
  },
  blur: function() {
    formattCurrency($(this), "blur",4);
  }

});
$(".valor-entero").on({
    keyup: function() {
      formattCurrency($(this), "",0,"","");
    },
    blur: function() {
      formattCurrency($(this), "blur",0,"","");
    }

});
$(".valor-decimal").on({
    keyup: function() {
      formattCurrency($(this), "",2,"","");
    },
    blur: function() {
      formattCurrency($(this), "blur",2,"","");
    }

});

});
