const CLIENT_ID = '920138527269-euigacmlshkhcm2493hrj2kuebooknro.apps.googleusercontent.com';
const API_KEY = 'AIzaSyASjUy-4_5mpfdivkuqZ4I3c3L3jVX84WI';
const DISCOVERY_DOC = 'https://www.googleapis.com/discovery/v1/apis/calendar/v3/rest';
const SCOPES = 'https://www.googleapis.com/auth/calendar';

let tokenClient;
let gapiInited = false;
let gisInited = false;

/**
 * Callback after api.js is loaded.
 */
function gapiLoaded() {
    gapi.load('client', initializeGapiClient);
}

/**
 * Callback after the API client is loaded. Loads the
 * discovery doc to initialize the API.
 */
async function initializeGapiClient() {
    await gapi.client.init({
        apiKey: API_KEY,
        discoveryDocs: [DISCOVERY_DOC],
    });
    gapiInited = true;

    maybeEnableButtons();

}

/**
 * Callback after Google Identity Services are loaded.
 */
function gisLoaded() {
    tokenClient = google.accounts.oauth2.initTokenClient({
        client_id: CLIENT_ID,
        scope: SCOPES,
        callback: '', // defined later
    });
    gisInited = true;

    maybeEnableButtons();

}

/**
 * Enables user interaction after all libraries are loaded.
 */
function maybeEnableButtons() {
    if (gapiInited && gisInited) {

        handleAuthClick();

    }
}

/**
 *  Sign in the user upon button click.
 */
function handleAuthClick() {

    if (gapi.client.getToken() === null) {
        // Prompt the user to select a Google Account and ask for consent to share their data
        // when establishing a new session.
        tokenClient.requestAccessToken({ prompt: 'consent' });
    } else {
        // Skip display of account chooser and consent dialog for an existing session.
        tokenClient.requestAccessToken({ prompt: '' });
    }
}

function formatToRFC5545(date) {
    // Get the year, month, and day
    var year = date.getFullYear();
    var month = (date.getMonth() + 1).toString().padStart(2, '0'); // Add leading zero if needed
    var day = date.getDate().toString().padStart(2, '0'); // Add leading zero if needed

    // Get the hours, minutes, and seconds
    var hours = date.getHours().toString().padStart(2, '0'); // Add leading zero if needed
    var minutes = date.getMinutes().toString().padStart(2, '0'); // Add leading zero if needed
    var seconds = date.getSeconds().toString().padStart(2, '0'); // Add leading zero if needed

    // Format the date into RFC 5545 format
    var formattedDate = `${year}-${month}-${day}T${hours}:${minutes}:00`;

    return formattedDate;
}

// Add event to calendar when "Agregar a Google Calendar" button is clicked


async function insertNewEvent(eventTitle, eventDescription, eventStart) {
    try {

        var startDate = new Date(eventStart);
        var endDate = new Date(eventStart);
        endDate.setMinutes(startDate.getMinutes() + 30);

        const startDateRFC5545 = formatToRFC5545(startDate);
        const endDateRFC5545 = formatToRFC5545(endDate); // Adding 30 minutes to the start date

        console.log(startDateRFC5545);
        console.log(endDateRFC5545);


        const event = {
            'summary': eventTitle,
            'description': eventDescription,
            'start': {
                'dateTime': startDateRFC5545,
                'timeZone': 'America/Mexico_City',
            },
            'end': {
                'dateTime': endDateRFC5545,
                'timeZone': 'America/Mexico_City',
            },
            'reminders': {
                'useDefault': false,
                'overrides': [
                    { 'method': 'email', 'minutes': 12 * 60 },
                    { 'method': 'popup', 'minutes': 10 },
                ],
            },
        };
        const response = await gapi.client.calendar.events.insert({
            'calendarId': 'primary',
            'resource': event,
        });
        console.log('Event created: ', response);
        Swal.fire({
            icon: 'success',
            title: 'Success',
            text: 'Event created successfully!',
        });
    } catch (error) {
        console.error('Error creating event: ', error);
        Swal.fire({
            icon: 'error',
            title: 'Error',
            text: 'Error creating event. Please try again.' + error,
        });
    }
}
