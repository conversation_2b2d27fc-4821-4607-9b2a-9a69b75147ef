function calcularPagos(){
    let anticipoTotalPagado=0;
    let anticipoRestan=0;
    let total=parseFloat(quitarFormato($("#total").val())).toFixed(2);

    let totallimit = parseFloat(quitarFormato($("#total-limit").val()));

    let convenio = $("#pagoAlFinal").val();

   // console.log("total "+total);
    $(".pagos").each(function(){
      //  console.log("producto "+quitarFormato($(this).val()));
        anticipoTotalPagado+=(quitarFormato($(this).val()));
    });
    //console.log("anticipoTotalPagado "+anticipoTotalPagado);
    $(".fix-payment-btn").removeClass("d-none");
    if(totallimit <= anticipoTotalPagado && convenio == 1)
        //$(".fix-payment-btn").addClass("d-none");

    if(!isNaN(anticipoTotalPagado)){
        anticipoTotalPagado=anticipoTotalPagado.toFixed(2);
    }

    anticipoRestan=  parseFloat(total).toFixed(2) - parseFloat(anticipoTotalPagado).toFixed(2);
    console.log("anticipoTotalPagado ", anticipoTotalPagado);
    console.log("total", total);
    console.log("anticipoRestan ", anticipoRestan);
    
    $("#anticipo-total-pagado").val(anticipoTotalPagado);
    $("#anticipo-restan").val(anticipoRestan);
    ponerFormatoPesos("#anticipo-total-pagado");
    ponerFormatoPesos("#anticipo-restan");

}

function agregarPago(total = null, type = null, idType = null){
    let anticipo = (total) ? parseFloat(quitarFormato(total)) : parseFloat(quitarFormato($("#anticipo").val()));
    let tipoPagoAnticipo= (idType) ? idType : $("#tipo-pago-anticipo").val();

    let id=Date.now()+Math.floor(Math.random() * 100);
    if(anticipo > 0){
        const selectedOption = $("#tipo-pago-anticipo").children('option:selected');
        const paymentName = (type) ? type : selectedOption.data('payment-name');
        if(paymentName ){
            
            $("#tabla-body-pagos").append('<tr class="payment" id="'+id+'">\n' +
                '                            <td>' +

                '<input id="valor-'+id+'" value="'+anticipo+'" type="text" class="form-control-plaintext valor-pesos pagos" data-idpago="" data-tipopagoanticipo="'+tipoPagoAnticipo+'" readonly></td>\n' +
                '                            <td>'+paymentName+'</td>\n' +
                '                            <td>\n' +
                '                              <button class="btn"  style="background:#fff"  onclick="quitarPago('+id+')">\n' +
                '                                <img src="/img/icon-remove.jpg" alt="" width="25">\n' +
                '                              </button>\n' +
                '                            </td>\n' +
                '                          </tr>');
            ponerFormatoPesos("#valor-"+id);
            
            calcularPagos();
            $("#anticipo").val("");
            //$("#tipo-pago-anticipo").val("");
        }else{
            $("#tipo-pago-anticipo").focus();
            Swal.fire(
                'Campo(s) vacios!',
                'debe seleccionar un tipo de pago!',
                'warning'
            );
        }
    }else{
        
        if (idType == null){
            $("#anticipo").focus();
            Swal.fire(
                'Campo(s) vacios!',
                'La cantidad debe ser mayor a cero!1027',
                'warning'
            );
        }
        
    }



}

function quitarPago(id){

    $("#"+id).remove();
    calcularPagos();
}

function reset(){
    $(".reset").each(function(){
        $(this).val("");
    });



    $("#anticipo-total-pagado").val("");
    $("#productos").html("");
    $("#tabla-body-pagos").html("");
    $("#idventa").val("");
    $("#anticipo-restan").val("");

    $("#tabla-productos-seleccionados").addClass("d-none");
    $("#productos").html("");
    $("#tabla-body-pagos").html("");
    $("#usuario-venta").val("");
    $("#usuario-donde-nos-conocio").val("");
    $("#unidad").val("");
    $("#numero-empleado").val("");
    $("#cliente").val("");
    $("#cliente-id").val("");
    $("#beneficiario-id").val("");
    $("#beneficiario-nombre").val("");
    $("#beneficiario").val("");
    $("#cliente-telefono").val("");
    $("#convenio").val("").trigger("change");



   /* $("#guardar-venta").show();
    $("#guardar-coitizacion").show();*/
}

function nuevaFuncion() {
    Swal.fire({
        title: 'Función Adicional',
        text: 'Has activado la nueva funcionalidad',
        icon: 'info',
        confirmButtonText: 'Entendido'
    });
    
    // Aquí puedes añadir la lógica que necesites para tu nueva funcionalidad
    console.log("Nueva función activada");
}