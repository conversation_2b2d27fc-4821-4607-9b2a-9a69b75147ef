/* ===== DASHBOARD OFICIAL - SISTEMA DE DISEÑO CORPORATIVO PROFESIONAL ===== */

.dashboard-oficial-container {
    /* Paleta de colores corporativa refinada */
    --do-primary-blue: #2563eb;
    --do-primary-blue-light: #3b82f6;
    --do-primary-blue-dark: #1d4ed8;
    --do-secondary-blue: #1e40af;

    --do-success-green: #059669;
    --do-success-green-light: #10b981;
    --do-warning-amber: #d97706;
    --do-danger-red: #dc2626;
    --do-info: #0ea5e9;
    --do-info-light: #e0f2fe;
    --do-info-dark: #0369a1;

    /* Escala de grises profesional */
    --do-gray-50: #f8fafc;
    --do-gray-100: #f1f5f9;
    --do-gray-200: #e2e8f0;
    --do-gray-300: #cbd5e1;
    --do-gray-400: #94a3b8;
    --do-gray-500: #64748b;
    --do-gray-600: #475569;
    --do-gray-700: #334155;
    --do-gray-800: #1e293b;
    --do-gray-900: #0f172a;
    --do-white: #ffffff;

    /* Sistema de sombras corporativo */
    --do-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --do-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --do-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --do-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --do-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

    /* Sistema de bordes */
    --do-radius-sm: 6px;
    --do-radius-md: 8px;
    --do-radius-lg: 12px;
    --do-radius-xl: 16px;

    /* Espaciado consistente */
    --do-spacing-xs: 0.25rem;
    --do-spacing-sm: 0.5rem;
    --do-spacing-md: 1rem;
    --do-spacing-lg: 1.5rem;
    --do-spacing-xl: 2rem;
    --do-spacing-2xl: 3rem;
}

/* ===== BASE Y TIPOGRAFÍA CORPORATIVA - SOLO PARA DASHBOARD OFICIAL ===== */
.dashboard-oficial-container {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background-color: var(--do-gray-50);
    color: var(--do-gray-700);
    line-height: 1.6;
    font-size: 14px;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Asegurar que todos los elementos dentro del dashboard tengan acceso a las variables */
.dashboard-oficial-container *,
.dashboard-oficial-container *::before,
.dashboard-oficial-container *::after {
    box-sizing: border-box;
}

/* Jerarquía tipográfica profesional - SOLO dentro del contenedor */
.dashboard-oficial-container h1,
.dashboard-oficial-container h2,
.dashboard-oficial-container h3,
.dashboard-oficial-container h4,
.dashboard-oficial-container h5,
.dashboard-oficial-container h6 {
    font-family: 'Inter', sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
    color: var(--do-gray-900);
    margin-bottom: var(--do-spacing-md);
}

.dashboard-header {
    background: var(--do-white);
    border-bottom: 1px solid var(--do-gray-200);
    padding: var(--do-spacing-lg) 0;
    margin-bottom: var(--do-spacing-xl);
}

.dashboard-title {
    font-size: 1.875rem;
    font-weight: 700;
    color: var(--do-gray-900);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--do-spacing-sm);
}

.dashboard-title i {
    color: var(--do-primary-blue);
    font-size: 1.5rem;
}

.section-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--do-gray-800);
    margin-bottom: var(--do-spacing-lg);
    display: flex;
    align-items: center;
    gap: var(--do-spacing-sm);
}

.section-title i {
    color: var(--do-primary-blue);
    font-size: 1rem;
}

/* ===== SISTEMA DE CARDS CORPORATIVO ===== */
.corporate-card {
    background: var(--do-white);
    border: 1px solid var(--do-gray-200);
    border-radius: var(--do-radius-lg);
    box-shadow: var(--do-shadow-sm);
    transition: all 0.2s ease-in-out;
    overflow: hidden;
}

.corporate-card:hover {
    box-shadow: var(--do-shadow-md);
    border-color: var(--do-gray-300);
}

.corporate-card-header {
    padding: var(--do-spacing-lg);
    border-bottom: 1px solid var(--do-gray-100);
    background: var(--do-gray-50);
}

.corporate-card-body {
    padding: var(--do-spacing-lg);
}

.corporate-card-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--do-gray-800);
    margin: 0;
    display: flex;
    align-items: center;
    gap: var(--do-spacing-sm);
}

.corporate-card-title i {
    color: var(--do-primary-blue);
}

/* ===== MÉTRICAS PRINCIPALES CORPORATIVAS ===== */
.metric-card {
    background: var(--do-white);
    border: 1px solid var(--do-gray-200);
    border-radius: var(--do-radius-lg);
    padding: var(--do-spacing-xl);
    text-align: center;
    transition: all 0.2s ease-in-out;
    position: relative;
    overflow: hidden;
    height: 100%;
}

.metric-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: var(--do-primary-blue);
}

.metric-card.success::before {
    background: var(--do-success-green);
}

.metric-card.warning::before {
    background: var(--do-warning-amber);
}

.metric-card.danger::before {
    background: var(--do-danger-red);
}

.metric-card:hover {
    box-shadow: var(--do-shadow-md);
    border-color: var(--do-gray-300);
}

.metric-icon {
    width: 48px;
    height: 48px;
    border-radius: var(--do-radius-md);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--do-spacing-md);
    font-size: 1.25rem;
    color: var(--do-white);
}

.metric-icon.success {
    background: var(--do-success-green);
}

.metric-icon.warning {
    background: var(--do-warning-amber);
}

.metric-icon.primary {
    background: var(--do-primary-blue);
}

.metric-value {
    font-size: 2rem;
    font-weight: 700;
    color: var(--do-gray-900);
    margin-bottom: var(--do-spacing-sm);
    line-height: 1.2;
}

.metric-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--do-gray-600);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: var(--do-spacing-xs);
}

.metric-description {
    font-size: 0.75rem;
    color: var(--do-gray-500);
    line-height: 1.4;
}

/* ===== FORMULARIOS Y CONTROLES CORPORATIVOS ===== */
.form-group {
    margin-bottom: var(--do-spacing-lg);
}

.form-label {
    font-size: 0.875rem;
    font-weight: 600;
    color: var(--do-gray-700);
    margin-bottom: var(--do-spacing-sm);
    display: block;
}

.form-control, .form-select {
    background: var(--do-white);
    border: 1px solid var(--do-gray-300);
    border-radius: var(--do-radius-md);
    padding: 0.75rem 1rem;
    font-size: 0.875rem;
    color: var(--do-gray-700);
    transition: all 0.2s ease-in-out;
    width: 100%;
}

.form-control:focus, .form-select:focus {
    outline: none;
    border-color: var(--do-primary-blue);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.form-control:hover, .form-select:hover {
    border-color: var(--do-gray-400);
}

.form-control::placeholder {
    color: var(--do-gray-400);
}

/* Select2 personalizado */
.select2-container--bootstrap-5 .select2-selection {
    border: 1px solid var(--do-gray-300) !important;
    border-radius: var(--do-radius-md) !important;
    padding: 0.5rem !important;
    min-height: 44px !important;
    background: var(--do-white) !important;
}

.select2-container--bootstrap-5 .select2-selection:focus-within {
    border-color: var(--do-primary-blue) !important;
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
}

/* ===== COMPONENTES ESPECÍFICOS DEL DASHBOARD ===== */

/* Header con controles */
.header-controls {
    position: relative;
    margin-bottom: 1rem;
}

/* Theme Toggle Button */
.theme-toggle {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 1000;
    background: var(--do-white);
    border: 1px solid var(--do-gray-200);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--do-shadow-md);
    transition: all 0.3s ease;
}

.theme-toggle:hover {
    transform: translateY(-2px);
    box-shadow: var(--do-shadow-lg);
}

/* Botón de Cerrar Sesión */
.logout-button-container {
    position: absolute;
    top: 0;
    right: 0;
    z-index: 999;
}

.logout-button {
    padding: 0.75rem 1.5rem;
    background: var(--do-danger-red);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--do-shadow-lg);
}

.logout-button:hover {
    background: #c53030;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.3);
}

.logout-button:not(:hover) {
    background: var(--do-danger-red);
    transform: translateY(0);
    box-shadow: var(--do-shadow-lg);
}

/* Hero Section */
.hero-metrics {
    margin-bottom: 2rem;
}

.hero-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.hero-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--do-gray-900);
    margin: 0;
    display: flex;
    align-items: center;
}

.hero-title i {
    color: var(--do-primary-blue);
    margin-right: 0.5rem;
}

.hero-subtitle {
    font-size: 0.875rem;
    color: var(--do-gray-600);
    margin: 0.25rem 0 0 0;
    display: flex;
    align-items: center;
}

.hero-subtitle i {
    margin-right: 0.25rem;
}

.date-time-display {
    text-align: right;
    color: var(--do-gray-600);
    font-size: 0.875rem;
    margin-right: 180px;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Quick Actions Section */
.quick-actions {
    margin-bottom: 2rem;
}

.quick-actions-container {
    background: var(--do-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--do-shadow-sm);
    border: 1px solid var(--do-gray-200);
}

.section-heading {
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--do-gray-900);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.section-heading i {
    color: var(--do-primary-blue);
}

.controls-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.control-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--do-gray-700);
    margin-bottom: 0.5rem;
}

.date-input {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--do-gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
}

.summary-display {
    padding: 0.75rem;
    border: 1px solid var(--do-gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    background: var(--do-white);
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.empresa-select {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--do-gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    background: var(--do-white);
}

.locked-display {
    padding: 0.75rem;
    border: 1px solid var(--do-gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    background: var(--do-gray-50);
    color: var(--do-gray-600);
    display: flex;
    align-items: center;
}

.locked-display i {
    margin-right: 0.5rem;
}

.search-button {
    width: 100%;
    padding: 0.75rem 1rem;
    background: var(--do-primary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.search-button:hover {
    background: var(--do-primary-blue-dark);
}

/* Analysis Section */
.analysis-section {
    background: var(--do-white);
    border-radius: 12px;
    box-shadow: var(--do-shadow-sm);
    border: 1px solid var(--do-gray-200);
    margin-bottom: 2rem;
}

.analysis-summary {
    padding: 1.5rem;
    cursor: pointer;
    font-size: 1.125rem;
    font-weight: 600;
    color: var(--do-gray-900);
    display: flex;
    align-items: center;
    gap: 0.5rem;
    user-select: none;
}

.analysis-summary i.section-icon {
    color: var(--do-primary-blue);
}

.analysis-summary i.chevron {
    margin-left: auto;
    transition: transform 0.3s ease;
}

.analysis-content {
    padding: 0 1.5rem 1.5rem 1.5rem;
    border-top: 1px solid var(--do-gray-200);
}

.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-container {
    background: var(--do-gray-50);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--do-gray-200);
}

.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: var(--do-gray-900);
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.chart-title i {
    color: var(--do-success-green);
}

.chart-area {
    height: 300px;
    min-height: 300px;
}

.table-container {
    background: var(--do-gray-50);
    border-radius: 8px;
    padding: 1.5rem;
    border: 1px solid var(--do-gray-200);
}

.data-table {
    width: 100%;
    margin: 0;
}

/* Year selector */
.year-selector-container {
    margin-bottom: 1.5rem;
}

.year-select {
    padding: 0.75rem;
    border: 1px solid var(--do-gray-300);
    border-radius: 8px;
    font-size: 0.875rem;
    background: var(--do-white);
}

/* Info box */
.info-box {
    margin-bottom: 1.5rem;
    padding: 0.75rem;
    background: var(--do-info-light);
    border-radius: 8px;
    border: 1px solid var(--do-info);
}

.info-text {
    margin: 0;
    font-size: 0.875rem;
    color: var(--do-info-dark);
}

/* ===== ESTILOS ADICIONALES PARA ELEMENTOS DEL DASHBOARD ===== */

/* Botones dentro del dashboard */
.dashboard-oficial-container .btn,
.dashboard-oficial-container button {
    border-radius: var(--do-radius-md);
    font-weight: 500;
    transition: all 0.2s ease-in-out;
}

.dashboard-oficial-container .btn-primary {
    background: var(--do-primary-blue);
    border-color: var(--do-primary-blue);
    color: var(--do-white);
}

.dashboard-oficial-container .btn-primary:hover {
    background: var(--do-primary-blue-dark);
    border-color: var(--do-primary-blue-dark);
}

.dashboard-oficial-container .btn-success {
    background: var(--do-success-green);
    border-color: var(--do-success-green);
    color: var(--do-white);
}

.dashboard-oficial-container .btn-warning {
    background: var(--do-warning-amber);
    border-color: var(--do-warning-amber);
    color: var(--do-white);
}

.dashboard-oficial-container .btn-danger {
    background: var(--do-danger-red);
    border-color: var(--do-danger-red);
    color: var(--do-white);
}

/* Cards y contenedores */
.dashboard-oficial-container .card {
    background: var(--do-white);
    border: 1px solid var(--do-gray-200);
    border-radius: var(--do-radius-lg);
    box-shadow: var(--do-shadow-sm);
}

.dashboard-oficial-container .card:hover {
    box-shadow: var(--do-shadow-md);
}

.dashboard-oficial-container .card-header {
    background: var(--do-gray-50);
    border-bottom: 1px solid var(--do-gray-200);
    padding: var(--do-spacing-lg);
}

.dashboard-oficial-container .card-body {
    padding: var(--do-spacing-lg);
}

/* Tablas */
.dashboard-oficial-container .table {
    color: var(--do-gray-700);
}

.dashboard-oficial-container .table th {
    background: var(--do-gray-50);
    color: var(--do-gray-800);
    font-weight: 600;
    border-bottom: 2px solid var(--do-gray-200);
}

.dashboard-oficial-container .table td {
    border-bottom: 1px solid var(--do-gray-200);
}

.dashboard-oficial-container .table-striped tbody tr:nth-of-type(odd) {
    background: var(--do-gray-50);
}

/* Alertas */
.dashboard-oficial-container .alert {
    border-radius: var(--do-radius-md);
    border: none;
    padding: var(--do-spacing-md) var(--do-spacing-lg);
}

.dashboard-oficial-container .alert-info {
    background: var(--do-info-light);
    color: var(--do-info-dark);
}

.dashboard-oficial-container .alert-success {
    background: var(--do-green-50);
    color: var(--do-success-green);
}

.dashboard-oficial-container .alert-warning {
    background: #fef3c7;
    color: var(--do-warning-amber);
}

.dashboard-oficial-container .alert-danger {
    background: #fef2f2;
    color: var(--do-danger-red);
}
