/* ===== REPORTE ÓRDENES DE LABORATORIO - ESTILOS ===== */

/* ===== ESCALA TIPOGRÁFICA ESPECÍFICA PARA REPORTE ÓRDENES ===== */
.reporte-ordenes-container {
    /* Escala tipográfica base (accesible y consistente) */
    --ro-font-size-xs: 0.75rem;      /* 12px - Texto muy pequeño */
    --ro-font-size-sm: 0.875rem;     /* 14px - Texto pequeño (mínimo accesible) */
    --ro-font-size-base: 1rem;       /* 16px - Texto base */
    --ro-font-size-md: 1.125rem;     /* 18px - Texto mediano */
    --ro-font-size-lg: 1.25rem;      /* 20px - Texto grande */
    --ro-font-size-xl: 1.5rem;       /* 24px - Títulos secundarios */
    --ro-font-size-2xl: 1.875rem;    /* 30px - T<PERSON><PERSON>los principales */
    --ro-font-size-3xl: 2.25rem;     /* 36px - Títulos de página */

    /* Pesos de fuente */
    --ro-font-weight-normal: 400;
    --ro-font-weight-medium: 500;
    --ro-font-weight-semibold: 600;
    --ro-font-weight-bold: 700;

    /* Alturas de línea */
    --ro-line-height-tight: 1.25;
    --ro-line-height-normal: 1.5;
    --ro-line-height-relaxed: 1.75;

    /* Espaciado de letras */
    --ro-letter-spacing-tight: -0.025em;
    --ro-letter-spacing-normal: 0;
    --ro-letter-spacing-wide: 0.025em;
    --ro-letter-spacing-wider: 0.05em;
}

/* Estilos base para tipografía - SOLO para el contenedor del reporte */
.reporte-ordenes-container {
    font-size: var(--ro-font-size-base);
    line-height: var(--ro-line-height-normal);
    font-weight: var(--ro-font-weight-normal);
}

/* Jerarquía de encabezados - SOLO dentro del contenedor */
.reporte-ordenes-container h1,
.reporte-ordenes-container .h1 {
    font-size: var(--ro-font-size-3xl);
    font-weight: var(--ro-font-weight-bold);
    line-height: var(--ro-line-height-tight);
    letter-spacing: var(--ro-letter-spacing-tight);
}
.reporte-ordenes-container h2,
.reporte-ordenes-container .h2 {
    font-size: var(--ro-font-size-2xl);
    font-weight: var(--ro-font-weight-bold);
    line-height: var(--ro-line-height-tight);
}
.reporte-ordenes-container h3,
.reporte-ordenes-container .h3 {
    font-size: var(--ro-font-size-xl);
    font-weight: var(--ro-font-weight-semibold);
    line-height: var(--ro-line-height-tight);
}
.reporte-ordenes-container h4,
.reporte-ordenes-container .h4 {
    font-size: var(--ro-font-size-lg);
    font-weight: var(--ro-font-weight-semibold);
}
.reporte-ordenes-container h5,
.reporte-ordenes-container .h5 {
    font-size: var(--ro-font-size-md);
    font-weight: var(--ro-font-weight-semibold);
}
.reporte-ordenes-container h6,
.reporte-ordenes-container .h6 {
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-semibold);
}

/* Clases utilitarias de texto - SOLO dentro del contenedor */
.reporte-ordenes-container .text-xs { font-size: var(--ro-font-size-xs); }
.reporte-ordenes-container .text-sm { font-size: var(--ro-font-size-sm); }
.reporte-ordenes-container .text-base { font-size: var(--ro-font-size-base); }
.reporte-ordenes-container .text-md { font-size: var(--ro-font-size-md); }
.reporte-ordenes-container .text-lg { font-size: var(--ro-font-size-lg); }
.reporte-ordenes-container .text-xl { font-size: var(--ro-font-size-xl); }

/* ===== HEADER MODERNO REDISEÑADO ===== */
.modern-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 20px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 10px 30px rgba(0, 123, 255, 0.3);
    color: white;
    position: relative;
    overflow: hidden;
}

.modern-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.1;
}

.header-content {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    text-align: center;
}

.header-icon {
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    width: 70px;
    height: 70px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--ro-font-size-2xl);
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.header-text {
    flex: 1;
}

.header-title {
    font-size: var(--ro-font-size-3xl);
    font-weight: var(--ro-font-weight-bold);
    margin: 0;
    line-height: var(--ro-line-height-tight);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.header-subtitle {
    font-size: var(--ro-font-size-md);
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    font-weight: var(--ro-font-weight-normal);
    line-height: var(--ro-line-height-normal);
}



/* ===== ESTILOS DE TARJETAS ===== */
.orden-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.orden-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.cards-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 1.5rem;
    padding: 1rem 0;
}

/* ===== ESTILOS DE BADGES ===== */
.etapa-badge {
    font-size: var(--ro-font-size-sm);
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
    font-weight: var(--ro-font-weight-semibold);
    line-height: var(--ro-line-height-tight);
}

.badge {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    line-height: var(--ro-line-height-tight);
}

/* ===== PANEL DE FILTROS MODERNO ===== */
.filters-panel {
    background: white;
    border-radius: 16px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.filters-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.filters-title {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: var(--ro-font-size-md);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
}

.filters-title i {
    color: #0056b3;
    font-size: var(--ro-font-size-lg);
}

.filters-toggle .toggle-btn {
    background: none;
    border: none;
    color: #6c757d;
    font-size: var(--ro-font-size-lg);
    padding: 0.5rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filters-toggle .toggle-btn:hover {
    background: rgba(0, 86, 179, 0.1);
    color: #0056b3;
    transform: scale(1.1);
}

.filters-toggle .toggle-btn.active {
    transform: rotate(180deg);
}

.filters-content {
    padding: 1.5rem;
    display: none;
}

.filters-content.show {
    display: block;
    animation: slideDown 0.3s ease-out;
}

/* Filtros rápidos */
.quick-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    flex-wrap: wrap;
}

.quick-filter-item {
    position: relative;
    flex: 1;
    min-width: 200px;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    margin-bottom: 0;
}

.quick-search,
.quick-select {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: var(--ro-font-size-base);
    transition: all 0.3s ease;
    background: white;
}

.quick-search:focus,
.quick-select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Filtros avanzados */
.advanced-filters {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.filter-group {
    background: #f8f9fa;
    border-radius: 12px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.filter-group-header {
    background: white;
    padding: 1rem 1.25rem;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    border-bottom: 1px solid #e9ecef;
}

.filter-group-header i {
    color: #0056b3;
    font-size: var(--ro-font-size-base);
}

.filter-group-content {
    padding: 1.25rem;
}

/* Filtros de fecha */
.date-range {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.date-input-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.date-input-group label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
}

.date-inputs {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.date-separator {
    font-size: var(--ro-font-size-sm);
    color: #6c757d;
    font-weight: var(--ro-font-weight-medium);
}

/* Filtros de cliente */
.client-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.client-input-group {
    position: relative;
}

.client-input-group input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: var(--ro-font-size-base);
    transition: all 0.3s ease;
}

.client-input-group input:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
}

/* Filtros adicionales */
.additional-filters {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-item label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
}

/* Botones de acción de filtros */
.filter-actions {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e9ecef;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    border: none;
    border-radius: 12px;
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-semibold);
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 160px;
    justify-content: center;
}

.action-btn.primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
}

.action-btn.primary:hover {
    background: linear-gradient(135deg, #0056b3, #004085);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
}

.action-btn.secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.action-btn.secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

/* ===== PANEL DE RESULTADOS ===== */
.results-panel {
    background: white;
    border-radius: 16px;
    margin-bottom: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    border: 1px solid #e9ecef;
    overflow: hidden;
}

.results-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 1.25rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
}

.results-info {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.results-count {
    display: flex;
    align-items: baseline;
    gap: 0.5rem;
}

.count-number {
    font-size: var(--ro-font-size-2xl);
    font-weight: var(--ro-font-weight-bold);
    color: #0056b3;
}

.count-label {
    font-size: var(--ro-font-size-sm);
    color: #6c757d;
    font-weight: var(--ro-font-weight-medium);
}

.results-status {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: var(--ro-font-size-sm);
    color: #28a745;
}

.results-status i {
    font-size: 8px;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

.view-controls {
    display: flex;
    align-items: center;
}

.view-toggle {
    display: flex;
    background: white;
    border-radius: 12px;
    padding: 0.25rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.view-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.25rem;
    border: none;
    background: transparent;
    border-radius: 8px;
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-medium);
    color: #6c757d;
    transition: all 0.3s ease;
    cursor: pointer;
}

.view-btn:hover {
    background: #f8f9fa;
    color: #495057;
}

.view-btn.active {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);
}

.view-btn i {
    font-size: var(--ro-font-size-base);
}

/* ===== ESTILOS DE BOTONES ===== */
.btn-aplicar-filtros {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: var(--ro-font-weight-semibold);
    font-size: var(--ro-font-size-base);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.btn-aplicar-filtros:hover {
    background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
}

.btn-limpiar-filtros {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
    border: none;
    color: white;
    padding: 0.75rem 2rem;
    border-radius: 8px;
    font-weight: var(--ro-font-weight-semibold);
    font-size: var(--ro-font-size-base);
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

.btn-limpiar-filtros:hover {
    background: linear-gradient(135deg, #5a6268 0%, #343a40 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.4);
}

.btn-detalle {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border: none;
    color: white;
    padding: 6px 12px;
    border-radius: 4px;
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-medium);
    transition: all 0.3s ease;
    line-height: var(--ro-line-height-tight);
}

.btn-detalle:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Estilos para botones generales */
.btn {
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-medium);
    line-height: var(--ro-line-height-tight);
}

.btn-sm {
    font-size: var(--ro-font-size-sm);
    padding: 0.375rem 0.75rem;
}

.btn-lg {
    font-size: var(--ro-font-size-md);
    padding: 0.75rem 1.5rem;
}

/* ===== ESTILOS DE FORMULARIOS ===== */
.info-label {
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    font-size: var(--ro-font-size-sm);
    line-height: var(--ro-line-height-normal);
}

.stage-select {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: var(--ro-font-size-base);
    background-color: white;
    transition: all 0.3s ease;
    width: 100%;
    font-weight: var(--ro-font-weight-medium);
    line-height: var(--ro-line-height-normal);
}

/* Estilos para inputs y selects del formulario */
.form-control, .form-select {
    font-size: var(--ro-font-size-base);
    line-height: var(--ro-line-height-normal);
    font-weight: var(--ro-font-weight-normal);
}

.form-label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    margin-bottom: 0.5rem;
}

/* ===== ESTILOS DE TIEMPO Y DÍAS ===== */
.tiempo-info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 12px;
    margin-top: 10px;
    border-left: 4px solid #007bff;
}

.tiempo-creacion {
    font-size: var(--ro-font-size-base);
    color: #495057;
    margin-bottom: 6px;
    font-weight: var(--ro-font-weight-medium);
    line-height: var(--ro-line-height-normal);
}

.dias-transcurridos {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    line-height: var(--ro-line-height-tight);
}

.dias-recientes {
    color: #28a745;
}

.dias-normales {
    color: #ffc107;
}

.dias-antiguos {
    color: #dc3545;
}

/* ===== ESTILOS DE MODAL ===== */
#pdfViewerModal .modal-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-bottom: none;
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

#pdfViewerModal .modal-title {
    font-weight: var(--ro-font-weight-semibold);
    font-size: var(--ro-font-size-md);
    line-height: var(--ro-line-height-tight);
}

#pdfViewerModal .modal-body {
    height: calc(100% - 70px);
    overflow: hidden;
    padding: 0;
    background: #f8f9fa;
}

#btnVolverDetalle {
    background-color: rgba(255, 255, 255, 0.15) !important;
    border-color: rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    font-weight: var(--ro-font-weight-medium);
    padding: 6px 12px !important;
    border-radius: 4px !important;
    transition: all 0.3s ease;
    font-size: var(--ro-font-size-sm);
    line-height: var(--ro-line-height-tight);
}

#btnVolverDetalle:hover {
    background-color: rgba(255, 255, 255, 0.25) !important;
    border-color: rgba(255, 255, 255, 0.5) !important;
    transform: translateY(-1px);
}

#pdfViewerModal .close {
    color: white;
    opacity: 0.8;
    font-size: var(--ro-font-size-xl);
}

#pdfViewerModal .close:hover {
    color: white;
    opacity: 1;
}

/* ===== ESTILOS DE CARGA Y ERROR ===== */
.pdf-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    color: #495057;
    background: white;
    border-radius: 8px;
    margin: 1rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pdf-loading i {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.pdf-loading p {
    color: #495057;
    font-size: var(--ro-font-size-md);
    margin-bottom: 0.5rem;
    font-weight: var(--ro-font-weight-medium);
    line-height: var(--ro-line-height-normal);
}

.pdf-loading small {
    color: #6c757d;
}

.pdf-error {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 400px;
    text-align: center;
    color: #dc3545;
    padding: 2rem;
}

.pdf-error i {
    font-size: 4rem;
    margin-bottom: 1.5rem;
    color: #dc3545;
}

.pdf-error h4 {
    font-size: var(--ro-font-size-lg);
    font-weight: var(--ro-font-weight-semibold);
    color: #dc3545;
    margin-bottom: 1rem;
}

.pdf-error p {
    font-size: var(--ro-font-size-base);
    color: #6c757d;
    line-height: var(--ro-line-height-normal);
}

/* ===== ESTILOS RESPONSIVE ===== */
@media (max-width: 768px) {
    .page-title-container {
        flex-direction: column;
        text-align: center;
    }

    .page-title {
        font-size: var(--ro-font-size-xl);
    }

    .page-subtitle {
        font-size: var(--ro-font-size-base);
    }

    .filtros-section {
        padding: 1rem;
    }

    .filtros-section-title {
        font-size: var(--ro-font-size-base);
    }

    .cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
}

/* ===== ESTILOS DE GRADUACIÓN ===== */
.graduacion-title {
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    margin-bottom: 12px;
    font-size: var(--ro-font-size-md);
    line-height: var(--ro-line-height-tight);
}

.graduacion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(140px, 1fr));
    gap: 12px;
}

.graduacion-item {
    font-size: var(--ro-font-size-base);
    padding: 4px 0;
    line-height: var(--ro-line-height-normal);
}

.graduacion-item label {
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
    font-size: var(--ro-font-size-xs);
    text-transform: uppercase;
    letter-spacing: var(--ro-letter-spacing-wider);
    margin-bottom: 5px;
    display: block;
    line-height: var(--ro-line-height-tight);
}

.graduacion-item .value {
    color: #212529;
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-medium);
    line-height: var(--ro-line-height-normal);
}

/* ===== ESTILOS DE TABLA ===== */
.table-container {
    margin-top: 20px;
}

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* ===== ESTILOS DATATABLE MEJORADOS ===== */
#ordenesTable {
    font-size: var(--ro-font-size-base);
    min-width: 1400px;
    table-layout: fixed;
    line-height: var(--ro-line-height-normal);
}

#ordenesTable th {
    font-weight: var(--ro-font-weight-semibold);
    text-transform: uppercase;
    font-size: var(--ro-font-size-sm);
    letter-spacing: var(--ro-letter-spacing-wide);
    border-bottom: 2px solid #dee2e6;
    vertical-align: middle;
    padding: 16px 12px;
    line-height: var(--ro-line-height-tight);
    background-color: #f8f9fa;
    color: #495057;
}

#ordenesTable td {
    vertical-align: middle;
    padding: 16px 12px;
    font-size: var(--ro-font-size-base);
    line-height: var(--ro-line-height-normal);
    font-weight: var(--ro-font-weight-normal);
    color: #212529;
}

/* Mejorar legibilidad de badges en tabla */
#ordenesTable .badge {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    padding: 0.4rem 0.8rem;
    line-height: var(--ro-line-height-tight);
}

/* Mejorar legibilidad de montos */
#ordenesTable .text-end {
    font-weight: var(--ro-font-weight-semibold);
    font-size: var(--ro-font-size-base);
}

/* ===== ESTILOS DE ACCIONES DE TABLA ===== */
.table-actions {
    min-width: 180px;
    padding: 8px;
    white-space: nowrap;
}

/* Asegurar que la columna de opciones sea visible */
#ordenesTable th:last-child,
#ordenesTable td:last-child {
    min-width: 180px !important;
    width: 180px !important;
}

.table-actions .btn {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-medium);
    padding: 8px 14px;
    border-radius: 4px;
    transition: all 0.3s ease;
    line-height: var(--ro-line-height-tight);
}

.table-actions .btn-primary {
    background: linear-gradient(45deg, #007bff, #0056b3);
    border: none;
}

.table-actions .btn-primary:hover {
    background: linear-gradient(45deg, #0056b3, #004085);
    transform: translateY(-1px);
}

.table-actions .dropdown-menu {
    min-width: 200px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-actions .dropdown-item {
    padding: 10px 16px;
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-normal);
    transition: all 0.3s ease;
    line-height: var(--ro-line-height-normal);
}

.table-actions .dropdown-item:hover {
    background: #f8f9fa;
    transform: translateX(5px);
}

.table-actions .dropdown-header {
    color: #495057;
    font-weight: var(--ro-font-weight-semibold);
    text-transform: uppercase;
    font-size: var(--ro-font-size-xs);
    letter-spacing: var(--ro-letter-spacing-wider);
    line-height: var(--ro-line-height-tight);
}

/* ===== ESTILOS PARA BOTONES DE VISTA ===== */
.btn-group .btn {
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-medium);
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.btn-group .btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-group .btn:not(.active):hover {
    background-color: #e9ecef;
    border-color: #007bff;
    color: #007bff;
}

/* ===== ESTILOS PARA SECCIÓN EXPANDIBLE DE GRADUACIÓN ===== */
.btn-expand-graduacion {
    background: none;
    border: none;
    color: #007bff;
    font-size: var(--ro-font-size-md);
    cursor: pointer;
    transition: transform 0.3s ease;
    padding: 8px;
}

.btn-expand-graduacion:hover {
    color: #0056b3;
    transform: scale(1.1);
}

.btn-expand-graduacion.expanded {
    transform: rotate(90deg);
}

.graduacion-details {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    margin: 10px 0;
    display: none;
}

.graduacion-details.show {
    display: block;
    animation: slideDown 0.3s ease;
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
    }
    to {
        opacity: 1;
        max-height: 200px;
    }
}

.graduacion-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.graduacion-item {
    background: white;
    padding: 10px;
    border-radius: 4px;
    border-left: 3px solid #007bff;
}

/* ===== ESTILOS RESPONSIVE ADICIONALES ===== */
@media (max-width: 768px) {
    .cards-container {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 0.5rem;
    }

    .orden-card {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: var(--ro-font-size-sm);
    }

    #ordenesTable th,
    #ordenesTable td {
        padding: 12px 8px;
        font-size: var(--ro-font-size-sm);
    }

    .table-actions .btn {
        font-size: var(--ro-font-size-xs);
        padding: 6px 10px;
    }

    .filtros-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .btn-aplicar-filtros,
    .btn-limpiar-filtros {
        padding: 0.5rem 1rem;
        font-size: var(--ro-font-size-sm);
    }
}

/* ===== UTILIDADES ADICIONALES ===== */
.text-truncate {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.font-weight-medium {
    font-weight: var(--ro-font-weight-medium);
}

.font-weight-semibold {
    font-weight: var(--ro-font-weight-semibold);
}

.line-height-tight {
    line-height: var(--ro-line-height-tight);
}

.line-height-normal {
    line-height: var(--ro-line-height-normal);
}

.letter-spacing-wide {
    letter-spacing: var(--ro-letter-spacing-wide);
}

/* ===== ESTILOS PARA MODAL DE GRADUACIÓN MEJORADO ===== */
.graduacion-modal-simple {
    padding: 1rem;
    max-width: 100%;
}

/* Sección de ojos */
.graduacion-eyes-section {
    margin-bottom: 2rem;
}

.eye-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 2px solid #e9ecef;
    border-radius: 16px;
    padding: 1.5rem;
    height: 100%;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.eye-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.eye-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
    border-color: #007bff;
}

.eye-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.eye-header i {
    font-size: var(--ro-font-size-lg);
    color: #007bff;
    background: rgba(0, 123, 255, 0.1);
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.eye-header h5 {
    margin: 0;
    font-size: var(--ro-font-size-md);
    font-weight: var(--ro-font-weight-semibold);
    color: #2c3e50;
}

.eye-values {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.value-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border-radius: 8px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.value-item:hover {
    background: #f8f9fa;
    border-color: #007bff;
}

.value-item .label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: var(--ro-letter-spacing-wide);
}

.value-item .value {
    font-size: var(--ro-font-size-lg);
    font-weight: var(--ro-font-weight-bold);
    color: #2c3e50;
    background: linear-gradient(135deg, #007bff, #0056b3);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Medidas adicionales */
.additional-measures {
    margin-bottom: 2rem;
}

.measure-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.25rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    transition: all 0.3s ease;
    height: 100%;
}

.measure-card:hover {
    border-color: #28a745;
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.15);
}

.measure-icon {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--ro-font-size-lg);
    flex-shrink: 0;
}

.measure-content {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.measure-label {
    font-size: var(--ro-font-size-sm);
    font-weight: var(--ro-font-weight-semibold);
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: var(--ro-letter-spacing-wide);
}

.measure-value {
    font-size: var(--ro-font-size-xl);
    font-weight: var(--ro-font-weight-bold);
    color: #2c3e50;
}

/* Información adicional */
.additional-info {
    margin-top: 2rem;
}

.info-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.25rem;
    margin-bottom: 1rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #dee2e6;
}

.info-header i {
    font-size: var(--ro-font-size-base);
    color: #6c757d;
    background: white;
    padding: 0.5rem;
    border-radius: 8px;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info-header span {
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-semibold);
    color: #495057;
}

.info-content {
    font-size: var(--ro-font-size-base);
    line-height: var(--ro-line-height-relaxed);
    color: #2c3e50;
    background: white;
    padding: 1rem;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

/* Responsive para modal de graduación */
@media (max-width: 768px) {
    .graduacion-modal-simple {
        padding: 0.5rem;
    }

    .eye-card {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .eye-header h5 {
        font-size: var(--ro-font-size-base);
    }

    .value-item {
        padding: 0.5rem;
    }

    .value-item .value {
        font-size: var(--ro-font-size-base);
    }

    .measure-card {
        padding: 1rem;
    }

    .measure-icon {
        width: 40px;
        height: 40px;
        font-size: var(--ro-font-size-base);
    }

    .measure-value {
        font-size: var(--ro-font-size-lg);
    }
}

/* ===== ESTILOS PARA MODAL PERSONALIZADO ===== */
.graduacion-modal-popup {
    border-radius: 20px !important;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2) !important;
    border: none !important;
    overflow: hidden !important;
}

.graduacion-modal-title {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white !important;
    padding: 1.5rem 2rem !important;
    margin: 0 !important;
    border-radius: 0 !important;
    font-size: var(--ro-font-size-lg) !important;
    font-weight: var(--ro-font-weight-semibold) !important;
}

.modal-title-custom {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.modal-title-custom i {
    font-size: var(--ro-font-size-xl);
    opacity: 0.9;
}

.graduacion-modal-container {
    padding: 0 !important;
    margin: 0 !important;
}

.graduacion-modal-wrapper {
    background: #f8f9fa;
    min-height: 400px;
}

.modal-actions {
    background: white;
    padding: 1.5rem 2rem;
    text-align: center;
    border-top: 1px solid #e9ecef;
    margin-top: 1rem;
}

.modal-actions .btn {
    font-size: var(--ro-font-size-base);
    font-weight: var(--ro-font-weight-semibold);
    padding: 0.75rem 2rem;
    border-radius: 12px;
    transition: all 0.3s ease;
    min-width: 180px;
}

.modal-actions .btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.modal-actions .btn-success:hover {
    background: linear-gradient(135deg, #218838, #1e7e34);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4);
}

.modal-actions .btn-secondary {
    background: linear-gradient(135deg, #6c757d, #495057);
    border: none;
    box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
    color: white;
}

.modal-actions .btn-secondary:hover {
    background: linear-gradient(135deg, #5a6268, #343a40);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
    color: white;
}

/* Espaciado entre botones */
.modal-actions .d-flex {
    gap: 1rem;
}

/* Responsive para botones del modal */
@media (max-width: 576px) {
    .modal-actions .d-flex {
        flex-direction: column;
        gap: 0.75rem;
    }

    .modal-actions .btn {
        width: 100%;
        min-width: auto;
    }
}

/* Animaciones para el modal */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

@keyframes slideDown {
    from {
        opacity: 0;
        max-height: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        max-height: 200px;
        transform: translateY(0);
    }
}

/* Ocultar completamente el botón X del modal */
.graduacion-modal-popup .swal2-close {
    display: none !important;
}

/* Asegurar que no aparezca ningún botón de cerrar automático */
.swal2-close {
    display: none !important;
}

/* Estilos para prevenir problemas de cierre del modal */
.graduacion-modal-popup {
    position: relative !important;
}

.graduacion-modal-popup .swal2-header {
    position: relative !important;
}

/* Asegurar que el botón de cerrar personalizado funcione */
.modal-actions .btn-secondary {
    cursor: pointer !important;
    pointer-events: auto !important;
}

/* Prevenir interferencias con el cierre del modal */
.swal2-container.swal2-backdrop-show {
    background-color: rgba(0, 0, 0, 0.4) !important;
}

/* Estilos para cuando el modal se está cerrando */
.swal2-popup.swal2-hide {
    animation: modalFadeOut 0.3s ease-in !important;
}

@keyframes modalFadeOut {
    from {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
    to {
        opacity: 0;
        transform: scale(0.8) translateY(-30px);
    }
}

/* Estilos para valores vacíos */
.value-item .value:empty::after,
.measure-value:empty::after {
    content: '--';
    color: #6c757d;
    font-style: italic;
}

/* Mejoras de accesibilidad */
.eye-card:focus-within {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

.measure-card:focus-within {
    outline: 2px solid #28a745;
    outline-offset: 2px;
}

/* Estados de hover mejorados */
.value-item:hover .label {
    color: #007bff;
}

.value-item:hover .value {
    transform: scale(1.05);
}

/* Indicadores visuales para datos importantes */
.value-item[data-important="true"] {
    border-left: 4px solid #007bff;
    background: linear-gradient(90deg, rgba(0, 123, 255, 0.05) 0%, white 100%);
}

.measure-card[data-critical="true"] {
    border-left: 4px solid #28a745;
    background: linear-gradient(90deg, rgba(40, 167, 69, 0.05) 0%, white 100%);
}

/* ===== ESTILOS PARA LOADING DEL TICKET ===== */
.ticket-loading-modal {
    border-radius: 16px !important;
}

.ticket-loading-modal .swal2-title {
    font-size: var(--ro-font-size-lg) !important;
    color: #2c3e50 !important;
    margin-bottom: 1rem !important;
}

.ticket-loading-content {
    padding: 2rem;
    text-align: center;
}

.ticket-loading-content i {
    color: #007bff;
    animation: spin 1s linear infinite;
}

.ticket-loading-content p {
    color: #6c757d;
    font-size: var(--ro-font-size-base);
    margin-top: 1rem;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* ===== ESTILOS PARA BOTÓN DE REGRESO ===== */
#btnVolverDetalle {
    transition: all 0.3s ease !important;
}

#btnVolverDetalle:hover {
    background-color: rgba(255, 255, 255, 0.25) !important;
    transform: translateY(-1px) !important;
}

/* Mejoras para la experiencia del usuario */
.swal2-popup {
    animation: modalSlideIn 0.3s ease-out !important;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(30px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Estilos para mejorar la accesibilidad */
.modal-actions .btn:focus {
    outline: 3px solid rgba(0, 123, 255, 0.5);
    outline-offset: 2px;
}

.eye-card:focus,
.measure-card:focus {
    outline: 2px solid #007bff;
    outline-offset: 2px;
}

/* Mejoras para dispositivos táctiles */
@media (hover: none) and (pointer: coarse) {
    .eye-card:hover,
    .measure-card:hover,
    .value-item:hover {
        transform: none;
    }

    .eye-card:active,
    .measure-card:active {
        transform: scale(0.98);
    }
}

/* ===== ESTILOS RESPONSIVE PARA HEADER Y FILTROS MODERNOS ===== */
@media (max-width: 1024px) {
    .quick-filters {
        flex-direction: column;
    }

    .quick-filter-item {
        min-width: auto;
    }
}

@media (max-width: 768px) {
    .modern-header {
        padding: 1.5rem;
        margin-bottom: 1.5rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .header-icon {
        width: 60px;
        height: 60px;
        font-size: var(--ro-font-size-xl);
    }

    .header-title {
        font-size: var(--ro-font-size-2xl);
    }

    .header-subtitle {
        font-size: var(--ro-font-size-base);
    }

    .filters-header {
        padding: 1rem;
    }

    .filters-content {
        padding: 1rem;
    }

    .date-inputs {
        flex-direction: column;
        gap: 0.5rem;
    }

    .date-separator {
        display: none;
    }

    .client-filters {
        grid-template-columns: 1fr;
    }

    .additional-filters {
        grid-template-columns: 1fr;
    }

    .filter-actions {
        flex-direction: column;
        gap: 0.75rem;
    }

    .action-btn {
        min-width: auto;
        width: 100%;
    }

    .results-header {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .results-info {
        flex-direction: column;
        gap: 0.75rem;
        text-align: center;
    }

    .view-toggle {
        width: 100%;
    }

    .view-btn {
        flex: 1;
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modern-header {
        padding: 1rem;
    }

    .header-title {
        font-size: var(--ro-font-size-xl);
    }

    .stat-card {
        padding: 0.75rem;
    }

    .stat-number {
        font-size: var(--ro-font-size-lg);
    }

    .filters-panel {
        margin-bottom: 1rem;
    }

    .quick-search,
    .quick-select {
        padding: 0.625rem 1rem 0.625rem 2.25rem;
    }

    .client-input-group input {
        padding: 0.625rem 1rem 0.625rem 2.25rem;
    }
}
