/* ===== AJUSTES PARA SONATA ADMIN LAYOUT ===== */

/* Variables de colores específicas para ventas generales */
.ventas-generales-container {
    --vg-sucursal-color: #4A90E2;      /* Azul corporativo elegante */
    --vg-bodega-color: #F5A623;        /* Naranja vibrante pero suave */
    --vg-campaña-color: #7B68EE;       /* Morado medio slate */
    --vg-success-soft: #50C878;        /* Verde esmeralda suave */
    --vg-error-soft: #FF6B6B;          /* Rojo coral suave */
    --vg-warning-soft: #FFB347;        /* Naranja melocotón */
    --vg-bg-light: #F8F9FA;           /* Gris muy claro */
    --vg-bg-medium: #E9ECEF;          /* Gris medio */
    --vg-border-soft: #DEE2E6;        /* Borde suave */
    --vg-text-soft: #6C757D;          /* Texto suave */

    /* Variables adicionales necesarias */
    --vg-primary-blue: #3b82f6;
    --vg-success-green: #10b981;
    --vg-warning-amber: #f59e0b;
    --vg-error-red: #ef4444;
    --vg-gray-50: #f9fafb;
    --vg-gray-100: #f3f4f6;
    --vg-gray-200: #e5e7eb;
    --vg-gray-300: #d1d5db;
    --vg-gray-400: #9ca3af;
    --vg-gray-500: #6b7280;
    --vg-gray-600: #4b5563;
    --vg-gray-700: #374151;
    --vg-gray-800: #1f2937;
    --vg-gray-900: #111827;
    --vg-white: #ffffff;
    --vg-shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --vg-shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
    --vg-info: #3b82f6;
    --vg-info-light: #dbeafe;
    --vg-info-dark: #1e40af;
    --vg-blue-50: #eff6ff;
    --vg-green-50: #f0fdf4;
}

/* Contenedor principal */
.executive-dashboard {
    max-width: 100%;
    margin: 0;
    padding: 0.5rem;
    box-sizing: border-box;
}

/* Sección de filtros */
.filtro-step {
    margin-bottom: 1rem !important;
    padding: 1rem !important;
    max-width: 100%;
    box-sizing: border-box;
}

/* Sección de métricas */
.metrics-section {
    margin-bottom: 1.5rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Cards de métricas */
.metric-card {
    min-width: 0; /* Permite que se contraigan */
    box-sizing: border-box;
}

/* ===== GRÁFICAS RESPONSIVAS ===== */

/* Contenedores de gráficas */
.chart-container,
.chart-grid > div,
[id*="grafica"],
[id*="chart"],
#graficaSucursal,
#tiposPago,
#deudaTotal,
#ventasMensuales,
#recuentoMarcas {
    width: 100% !important;
    max-width: 100% !important;
    overflow: hidden;
    box-sizing: border-box;
}

/* ApexCharts específico */
.apexcharts-canvas {
    max-width: 100% !important;
    width: 100% !important;
}

.apexcharts-svg {
    width: 100% !important;
    max-width: 100% !important;
}

/* ===== GRIDS RESPONSIVOS ===== */

/* Grid principal de gráficas */
.chart-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Grid de 2 columnas para gráficas grandes */
.chart-grid-2 {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* ===== TABLAS RESPONSIVAS ===== */

.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    margin-bottom: 1.5rem;
}

.table-responsive table {
    min-width: 600px;
}

/* ===== BREAKPOINTS ESPECÍFICOS ===== */

/* Pantallas medianas (tablets) */
@media (max-width: 1200px) {
    .chart-grid {
        grid-template-columns: 1fr !important;
    }

    .chart-grid-2 {
        grid-template-columns: 1fr !important;
    }

    .metrics-grid {
        grid-template-columns: 1fr !important;
    }
}

/* Pantallas pequeñas (móviles) */
@media (max-width: 768px) {
    .executive-dashboard {
        padding: 0.25rem;
    }

    .filtro-step {
        padding: 0.75rem !important;
        margin-bottom: 0.75rem !important;
    }

    .chart-container {
        height: 250px !important;
    }
}

/* ===== CORRECCIONES ESPECÍFICAS ===== */

/* Evitar overflow horizontal */
body, html {
    overflow-x: hidden;
}

/* Contenedor principal sin scroll horizontal */
.container-fluid {
    max-width: 100%;
    overflow-x: hidden;
}

/* Wizard de filtros */
.wizard-summary {
    max-width: 100%;
    box-sizing: border-box;
    word-wrap: break-word;
}

/* Botones responsivos */
.btn {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* ===== AJUSTES PARA SONATA ADMIN ESPECÍFICOS ===== */

/* Compensar por el sidebar de admin */
@media (min-width: 992px) {
    .executive-dashboard {
        max-width: calc(100vw - 250px); /* Ancho del sidebar */
    }
}

/* Ajustar altura de gráficas en admin */
.chart-container {
    min-height: 300px;
    max-height: 400px;
}

/* ===== ESTILOS PARA FILTROS DE SUCURSALES ===== */

/* Botones con hover suave */
.toggle-details:hover {
    background: var(--vg-sucursal-color) !important;
    opacity: 0.9;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Efectos hover para botones de acción rápida */
button[onclick*="selectAllSucursales"]:hover {
    background: var(--vg-success-soft) !important;
    opacity: 0.9;
    transform: translateY(-1px);
}

button[onclick*="selectNoneSucursales"]:hover {
    background: var(--vg-error-soft) !important;
    opacity: 0.9;
    transform: translateY(-1px);
}

/* Efectos hover específicos por tipo de sucursal */
button[onclick*="selectByType('sucursal')"]:hover {
    background: #357ABD !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(74, 144, 226, 0.4) !important;
}

button[onclick*="selectByType('bodega')"]:hover {
    background: #E8940F !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(245, 166, 35, 0.4) !important;
}

button[onclick*="selectByType('campaña')"]:hover {
    background: #6A5ACD !important;
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(123, 104, 238, 0.4) !important;
}

/* Mejoras en checkboxes */
input[type="checkbox"][name="sucursal"] {
    transition: all 0.2s ease;
}

input[type="checkbox"][name="sucursal"]:hover {
    transform: scale(1.1);
}

/* Headers de tipo con gradientes específicos */
.tipo-header {
    position: relative;
    overflow: hidden;
}

/* Gradientes específicos por tipo */
.tipo-section[data-tipo="sucursal"] .tipo-header {
    background: linear-gradient(135deg, #4A90E2 0%, #357ABD 100%) !important;
    box-shadow: 0 2px 8px rgba(74, 144, 226, 0.2);
}

.tipo-section[data-tipo="bodega"] .tipo-header {
    background: linear-gradient(135deg, #F5A623 0%, #E8940F 100%) !important;
    box-shadow: 0 2px 8px rgba(245, 166, 35, 0.2);
}

.tipo-section[data-tipo="campaña"] .tipo-header {
    background: linear-gradient(135deg, #7B68EE 0%, #6A5ACD 100%) !important;
    box-shadow: 0 2px 8px rgba(123, 104, 238, 0.2);
}

/* Efecto de brillo sutil en headers */
.tipo-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s ease;
}

.tipo-section:hover .tipo-header::before {
    left: 100%;
}

/* Animación suave para detalles */
.tipo-details {
    transition: all 0.3s ease;
}

/* Mejora visual para el campo de búsqueda */
#search-sucursales:focus,
#search-tipos-venta:focus {
    border-color: var(--vg-sucursal-color);
    box-shadow: 0 0 0 3px rgba(107, 127, 187, 0.1);
    outline: none;
}

/* ===== ESTILOS PARA TABLA DE INGRESOS ===== */

/* Mejorar apariencia de DataTables */
#tablaIngresosDiarios {
    border-collapse: separate !important;
    border-spacing: 0 !important;
}

#tablaIngresosDiarios thead th {
    background: #F8F9FA !important;
    color: #495057 !important;
    font-weight: 600 !important;
    text-align: center !important;
    padding: 15px 12px !important;
    border: 1px solid #DEE2E6 !important;
    font-size: 1rem !important;
    border-bottom: 2px solid #DEE2E6 !important;
}

#tablaIngresosDiarios tbody td {
    padding: 12px 10px !important;
    vertical-align: middle !important;
    border-bottom: 1px solid #E9ECEF !important;
    font-size: 1rem !important;
    line-height: 1.4 !important;
}

#tablaIngresosDiarios tbody tr:hover {
    background-color: #F8F9FA !important;
}

/* Estilos para badges en la tabla - más legibles */
.badge-tipo-venta {
    background: #E8F4FD;
    color: #0D47A1;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    margin: 2px;
    display: inline-block;
    border: 1px solid #BBDEFB;
}

.badge-metodo-pago {
    background: #F3E5F5;
    color: #4A148C;
    padding: 4px 8px;
    border-radius: 6px;
    font-size: 0.85rem;
    font-weight: 500;
    margin: 2px;
    display: inline-block;
    border: 1px solid #E1BEE7;
}

/* Mejorar legibilidad general */
#tablaIngresosDiarios_wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

#tablaIngresosDiarios_wrapper .dataTables_length,
#tablaIngresosDiarios_wrapper .dataTables_filter,
#tablaIngresosDiarios_wrapper .dataTables_info,
#tablaIngresosDiarios_wrapper .dataTables_paginate {
    font-size: 0.95rem !important;
}

/* Mejorar contraste de texto */
#tablaIngresosDiarios tbody td {
    color: #212529 !important;
}

/* Estilos para botones de exportación */
.dt-buttons {
    margin-bottom: 1rem !important;
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 0.5rem !important;
}

.dt-buttons .btn {
    margin-right: 0 !important;
    margin-bottom: 0 !important;
    font-size: 0.875rem !important;
    padding: 0.5rem 1rem !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
    transition: all 0.2s ease !important;
    white-space: nowrap !important;
    display: inline-flex !important;
    align-items: center !important;
    min-width: auto !important;
    width: auto !important;
}

.dt-buttons .btn:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(0,0,0,0.15) !important;
}

.dt-buttons .btn i {
    font-size: 0.875rem !important;
    flex-shrink: 0 !important;
}

/* Estilos específicos para tipos de venta */
.tipos-venta-grid {
    transition: all 0.3s ease;
}

.tipos-venta-grid label:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* Animación para el toggle de tipos de venta */
#tipos-venta-chevron {
    transition: transform 0.3s ease;
}

/* Mejoras en el contador de tipos de venta */
#tipos-venta-count {
    transition: color 0.3s ease;
}

/* ===== ESTILOS PARA DASHBOARD DE INVENTARIO ===== */

/* Cards de inventario */
.inventory-card {
    background: var(--vg-white);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--vg-shadow-sm);
    border: 1px solid var(--vg-gray-200);
    transition: all 0.3s ease;
}

.inventory-card:hover {
    box-shadow: var(--vg-shadow-md);
    transform: translateY(-2px);
}

/* Responsive para inventario */
@media (max-width: 1200px) {
    .inventory-section [style*="grid-template-columns: 1fr 1fr 1fr"] {
        display: grid !important;
        grid-template-columns: 1fr 1fr !important;
        gap: 1rem;
    }
}

@media (max-width: 768px) {
    .inventory-section [style*="grid-template-columns: 1fr 1fr 1fr"],
    .inventory-section [style*="grid-template-columns: 1fr 1fr"] {
        display: grid !important;
        grid-template-columns: 1fr !important;
        gap: 1rem;
    }
}

/* ===== ESTILOS PARA PRESET BUTTONS ===== */

.preset-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--vg-gray-300);
    background: var(--vg-white);
    color: var(--vg-gray-700);
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.875rem;
    font-weight: 500;
}

.preset-btn:hover {
    background: var(--vg-gray-50);
    border-color: var(--vg-primary-blue);
    color: var(--vg-primary-blue);
}

.preset-btn.active {
    background: var(--vg-primary-blue);
    border-color: var(--vg-primary-blue);
    color: var(--vg-white);
}

/* ===== ESTILOS PARA ANÁLISIS SECTIONS ===== */

.analysis-section {
    transition: all 0.3s ease;
}

.analysis-section[open] {
    box-shadow: var(--vg-shadow-md);
}

.analysis-section summary {
    transition: all 0.3s ease;
}

.analysis-section summary:hover {
    background: var(--vg-gray-50);
}

/* ===== ESTILOS PARA PROGRESS BAR ===== */

.progress-bar {
    width: 100%;
    height: 4px;
    background: var(--vg-gray-200);
    border-radius: 2px;
    overflow: hidden;
    position: relative;
}

.progress-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background: linear-gradient(90deg, var(--vg-primary-blue), var(--vg-success-green));
    border-radius: 2px;
    animation: progress 2s ease-in-out infinite;
}

@keyframes progress {
    0% { transform: translateX(-100%); }
    50% { transform: translateX(0%); }
    100% { transform: translateX(100%); }
}

/* ===== ESTILOS PARA SCROLL EN CARDS DE INVENTARIO ===== */

/* Contenedores de sucursales y campañas con scroll */
#top-sucursales-container,
#top-campanas-container {
    max-height: 300px;
    overflow-y: auto;
    overflow-x: hidden;
    scroll-behavior: smooth;
    padding-right: 4px; /* Espacio para el scrollbar */
}

/* Personalizar scrollbar para las cards */
#top-sucursales-container::-webkit-scrollbar,
#top-campanas-container::-webkit-scrollbar {
    width: 6px;
}

#top-sucursales-container::-webkit-scrollbar-track,
#top-campanas-container::-webkit-scrollbar-track {
    background: var(--vg-gray-100);
    border-radius: 3px;
}

#top-sucursales-container::-webkit-scrollbar-thumb,
#top-campanas-container::-webkit-scrollbar-thumb {
    background: var(--vg-gray-300);
    border-radius: 3px;
    transition: background 0.3s ease;
}

#top-sucursales-container::-webkit-scrollbar-thumb:hover,
#top-campanas-container::-webkit-scrollbar-thumb:hover {
    background: var(--vg-gray-400);
}

/* Para Firefox */
#top-sucursales-container,
#top-campanas-container {
    scrollbar-width: thin;
    scrollbar-color: var(--vg-gray-300) var(--vg-gray-100);
}

/* Estilo para los elementos dentro de las cards con scroll */
#top-sucursales-container > div,
#top-campanas-container > div {
    margin-bottom: 0.5rem;
}

#top-sucursales-container > div:last-child,
#top-campanas-container > div:last-child {
    margin-bottom: 0;
}

/* Efecto hover en los elementos de la lista */
#top-sucursales-container > div:hover,
#top-campanas-container > div:hover {
    background: var(--vg-gray-50);
    border-radius: 6px;
    transition: background 0.2s ease;
}

/* ===== ESTILOS PARA LOADING INICIAL ===== */

/* Contenedor principal con posición relativa */
.executive-dashboard {
    position: relative;
}

/* Overlay de carga inicial */
#initial-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

/* Animación del spinner */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Estilos para los pasos completados */
.step-completed {
    color: var(--vg-success-green) !important;
}

.step-completed i {
    color: var(--vg-success-green) !important;
}

/* Animación para ocultar el overlay */
.fade-out {
    opacity: 0;
    transition: opacity 0.5s ease;
}

/* ===== ESTILOS ADICIONALES PARA ELEMENTOS DE VENTAS GENERALES ===== */

/* Asegurar que todos los elementos dentro del contenedor tengan acceso a las variables */
.ventas-generales-container *,
.ventas-generales-container *::before,
.ventas-generales-container *::after {
    box-sizing: border-box;
}

/* Botones específicos de ventas generales */
.ventas-generales-container .btn-sucursal {
    background: var(--vg-sucursal-color);
    border-color: var(--vg-sucursal-color);
    color: var(--vg-white);
}

.ventas-generales-container .btn-bodega {
    background: var(--vg-bodega-color);
    border-color: var(--vg-bodega-color);
    color: var(--vg-white);
}

.ventas-generales-container .btn-campaña {
    background: var(--vg-campaña-color);
    border-color: var(--vg-campaña-color);
    color: var(--vg-white);
}

/* Cards específicas de ventas generales */
.ventas-generales-container .metric-card {
    background: var(--vg-white);
    border: 1px solid var(--vg-gray-200);
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: var(--vg-shadow-sm);
    transition: all 0.3s ease;
}

.ventas-generales-container .metric-card:hover {
    box-shadow: var(--vg-shadow-md);
    transform: translateY(-2px);
}

/* Elementos de filtros */
.ventas-generales-container .filtro-step {
    background: var(--vg-white);
    border: 1px solid var(--vg-gray-200);
    border-radius: 8px;
    box-shadow: var(--vg-shadow-sm);
}

.ventas-generales-container .form-control,
.ventas-generales-container .form-select {
    background: var(--vg-white);
    border: 1px solid var(--vg-gray-300);
    border-radius: 6px;
    color: var(--vg-gray-700);
}

.ventas-generales-container .form-control:focus,
.ventas-generales-container .form-select:focus {
    border-color: var(--vg-primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Elementos de gráficas */
.ventas-generales-container .chart-container {
    background: var(--vg-gray-50);
    border: 1px solid var(--vg-gray-200);
    border-radius: 8px;
    padding: 1.5rem;
}

.ventas-generales-container .chart-title {
    color: var(--vg-gray-900);
    font-weight: 600;
}

/* Elementos de análisis */
.ventas-generales-container .analysis-section {
    background: var(--vg-white);
    border: 1px solid var(--vg-gray-200);
    border-radius: 12px;
    box-shadow: var(--vg-shadow-sm);
}

.ventas-generales-container .analysis-section[open] {
    box-shadow: var(--vg-shadow-md);
}

/* Elementos de progreso */
.ventas-generales-container .progress-bar {
    background: var(--vg-gray-200);
    border-radius: 2px;
}

.ventas-generales-container .progress-fill {
    background: linear-gradient(90deg, var(--vg-primary-blue), var(--vg-success-green));
}

/* Elementos de scroll personalizado */
.ventas-generales-container ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
}

.ventas-generales-container ::-webkit-scrollbar-track {
    background: var(--vg-gray-100);
    border-radius: 3px;
}

.ventas-generales-container ::-webkit-scrollbar-thumb {
    background: var(--vg-gray-300);
    border-radius: 3px;
}

.ventas-generales-container ::-webkit-scrollbar-thumb:hover {
    background: var(--vg-gray-400);
}