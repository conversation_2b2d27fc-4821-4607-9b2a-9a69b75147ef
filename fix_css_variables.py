#!/usr/bin/env python3
"""
Script para actualizar las variables CSS en el template de ventas generales
para usar los nuevos prefijos que evitan conflictos con el menú lateral.
"""

import re

def fix_css_variables():
    # Mapeo de variables CSS antiguas a nuevas
    css_mappings = {
        # Variables del dashboard oficial (prefijo do-)
        'var(--primary-blue)': 'var(--do-primary-blue)',
        'var(--primary-blue-light)': 'var(--do-primary-blue-light)',
        'var(--primary-blue-dark)': 'var(--do-primary-blue-dark)',
        'var(--secondary-blue)': 'var(--do-secondary-blue)',
        'var(--success-green)': 'var(--do-success-green)',
        'var(--success-green-light)': 'var(--do-success-green-light)',
        'var(--warning-amber)': 'var(--do-warning-amber)',
        'var(--danger-red)': 'var(--do-danger-red)',
        'var(--info)': 'var(--do-info)',
        'var(--info-light)': 'var(--do-info-light)',
        'var(--info-dark)': 'var(--do-info-dark)',
        'var(--gray-50)': 'var(--do-gray-50)',
        'var(--gray-100)': 'var(--do-gray-100)',
        'var(--gray-200)': 'var(--do-gray-200)',
        'var(--gray-300)': 'var(--do-gray-300)',
        'var(--gray-400)': 'var(--do-gray-400)',
        'var(--gray-500)': 'var(--do-gray-500)',
        'var(--gray-600)': 'var(--do-gray-600)',
        'var(--gray-700)': 'var(--do-gray-700)',
        'var(--gray-800)': 'var(--do-gray-800)',
        'var(--gray-900)': 'var(--do-gray-900)',
        'var(--white)': 'var(--do-white)',
        'var(--shadow-sm)': 'var(--do-shadow-sm)',
        'var(--shadow-md)': 'var(--do-shadow-md)',
        'var(--shadow-lg)': 'var(--do-shadow-lg)',
        'var(--shadow-xl)': 'var(--do-shadow-xl)',
        'var(--blue-50)': 'var(--do-blue-50)',
        'var(--green-50)': 'var(--do-green-50)',
        
        # Variables específicas de ventas generales (prefijo vg-)
        'var(--sucursal-color)': 'var(--vg-sucursal-color)',
        'var(--bodega-color)': 'var(--vg-bodega-color)',
        'var(--campaña-color)': 'var(--vg-campaña-color)',
        'var(--success-soft)': 'var(--vg-success-soft)',
        'var(--error-soft)': 'var(--vg-error-soft)',
        'var(--warning-soft)': 'var(--vg-warning-soft)',
        'var(--bg-light)': 'var(--vg-bg-light)',
        'var(--bg-medium)': 'var(--vg-bg-medium)',
        'var(--border-soft)': 'var(--vg-border-soft)',
        'var(--text-soft)': 'var(--vg-text-soft)',
        'var(--error-red)': 'var(--vg-error-red)',
    }
    
    # Leer el archivo
    with open('templates/ventas_generales/index.html.twig', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Aplicar los reemplazos
    for old_var, new_var in css_mappings.items():
        content = content.replace(old_var, new_var)
    
    # Escribir el archivo actualizado
    with open('templates/ventas_generales/index.html.twig', 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Variables CSS actualizadas exitosamente")
    print(f"📝 Se aplicaron {len(css_mappings)} reemplazos")

if __name__ == '__main__':
    fix_css_variables()
