security:
  encoders:
    App\Entity\Usuario: # use bcrypt if you are using "symfony/security-bundle" < 4.3
      algorithm: bcrypt
    App\Entity\Cliente:
      algorithm: bcrypt
  role_hierarchy:
    ROLE_SUPER_ADMIN: [ ROLE_ADMIN, R<PERSON><PERSON>_ALLOWED_TO_SWITCH, ROL<PERSON>_VENDEDOR, ROLE_EJECUTIVO_COBRANZA, R<PERSON><PERSON>_MENSAJERO, ROLE_LAB, ROLE_CALIDAD, ROLE_OPTOMETRISTA ]
    ROLE_ADMIN: [ ROLE_USER, ROLE_SONATA_ADMIN, ROLE_VENDEDOR, ROLE_MENSAJERO, ROLE_LAB, ROLE_OPTOMETRISTA ]
    ROLE_SUPERVISOR: [ ROLE_USER ]
    ROLE_VENDEDOR: [ ROLE_USER ]
    ROLE_EJECUTIVO_COBRANZA: [ ROLE_USER ]
    ROLE_LAB: [ ROLE_USER ]
    ROLE_CALIDAD: [ ROLE_LAB ]
    ROLE_OPTOMETRISTA: [ ROLE_USER ]
    ROLE_MENSAJERO: [ ROLE_USER ]
    ROLE_CLIENT: [ ROLE_USER ]
    SONATA:
      - ROLE_SONATA_PAGE_ADMIN_PAGE_EDIT

  providers:
    users:
      id: App\Security\UserProvider
    cliente:
      id: App\Security\ClienteProvider

  firewalls:
    dev:
      pattern: ^/(_(profiler|wdt)|css|images|js)/
      security: false

    # JWT: Endpoint de login para la API
    api:
      pattern: ^/cliente-api
      stateless: true
      anonymous: true
      provider: cliente

      json_login:
        check_path: /cliente-api/login_check
        username_path: trimmedEmail
        password_path: password
        success_handler: lexik_jwt_authentication.handler.authentication_success
        failure_handler: lexik_jwt_authentication.handler.authentication_failure

      guard:
        authenticators:
          - lexik_jwt_authentication.jwt_token_authenticator

      entry_point: lexik_jwt_authentication.jwt_token_authenticator


    admin:
      pattern: /(.*)
      provider: users
      form_login:
        login_path: admin_login
        use_forward: false
        check_path: admin_login
        failure_path: null
        always_use_default_target_path: false
      logout:
        path: admin_logout
        target: admin_login
      anonymous: true
      guard:
        authenticators:
          - App\Security\AdminLoginAuthenticator
      two_factor:
        auth_form_path: 2fa_login
        check_path: 2fa_login_check
        success_handler: App\Security\TwoFactorSuccessHandler

    main:
      anonymous: ~

  access_control:
    - { path: ^/api/doc, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/api/*, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/venta/agregar-graduacion, role: [ IS_AUTHENTICATED_ANONYMOUSLY ] }
    - { path: ^/venta/, role: [ ROLE_VENDEDOR ] }
    - { path: ^/admin/login$, role: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/admin/logout$, role: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/reporte/dashboard, roles: [ ROLE_SUPERVISOR, ROLE_SUPER_ADMIN ] }
    - { path: ^/admin/app/prod, role: [ ROLE_SUPER_ADMIN ] }
    - { path: ^/admin/, role: [ ROLE_ADMIN, ROLE_SONATA_ADMIN, ROLE_VENDEDOR ] }
    - { path: ^/dashboard/, role: [ ROLE_ADMIN, ROLE_SONATA_ADMIN ] }
    - { path: ^/inventario/, role: [ ROLE_SUPER_ADMIN ] }
    - { path: ^/almacen/, role: [ ROLE_SUPER_ADMIN ] }
    - { path: ^/informes/, role: [ ROLE_ADMIN, ROLE_SUPER_ADMIN ] }
    - { path: ^/corte-caja/, roles: [ ROLE_ADMIN, ROLE_SUPER_ADMIN ] }
    - { path: ^/reportes/, roles: [ ROLE_ADMIN, ROLE_SUPER_ADMIN ] }
    - { path: ^/cobranza/, roles: [ ROLE_EJECUTIVO_COBRANZA, ROLE_ADMIN, ROLE_SUPER_ADMIN ] }
    - { path: ^/envios/, roles: [ ROLE_MENSAJERO ] }
    - { path: ^/lab/, roles: [ ROLE_LAB, ROLE_CALIDAD, ROLE_OPTOMETRISTA ] }
    - { path: ^/.*, role: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/2fa, role: IS_AUTHENTICATED_2FA_IN_PROGRESS }
    - { path: ^/cliente-api/login_check, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/cliente-api, roles: IS_AUTHENTICATED_FULLY }
    - { path: ^/api/codigos-postales, roles: IS_AUTHENTICATED_ANONYMOUSLY }

