sonata_admin:
  title: 'Optimo'
  title_logo: 'img/Mesa de trabajo 21.png'
  options:
    html5_validate: true
    sort_admins: false

  dashboard:
    groups:
      # Consolidar puntos de venta
      PuntoVenta:
        label: Punto de venta
        label_catalogue: Punto de venta
        icon: '<i class="fa-solid fa-hand-holding-dollar"></i>'
        items:
          - admin.venta
          - admin.stock
          - admin.stockventa
          - admin.pago
          - admin.cupon
          - admin.cuponmarca
          - admin.ventacupon
          - admin.tipoventa
          - admin.sellreference
          - route: nueva-venta
            label: Nueva venta
        roles: [ 'ROLE_VENDEDOR' ]

      Almacen:
        label: Almacén
        label_catalogue: Inventario
        icon: '<i class="fa-solid fa-shop-lock"></i>'
        items:
          - admin.ventaalmacen
          - admin.stockalmacen
          - admin.producto
          - admin.reglaabastecimiento
          - admin.traspasoalmacen
          - admin.productotraspasoalmacen
          - admin.cargainventariolog
          - admin.ordensalida
          - route: seleccionar-productos
            label: Traspaso de productos
          - route: app_merma
            label: Merma
          - route: r_Inventario_cargaMasivaIndex
            label: Cargar Inventario
          - route: actualizacion-update-index
            label: Actualización de productos
        roles: [ 'ROLE_SUPER_ADMIN' ]

      # Grupo específico para EJECUTIVO_COBRANZA
      Cobranza:
        label: Gestión de Cobranza
        label_catalogue: Cobranza
        icon: '<i class="fa-solid fa-money-bill-wave"></i>'
        items:
          - route: app_cobranza
            label: Ventas a Crédito
          - admin.cliente
        roles: [ 'ROLE_EJECUTIVO_COBRANZA' ]

      # Reportes solo para ADMIN y SUPER_ADMIN
      ReportesInformes:
        label: Reportes e Informes
        label_catalogue: Reportes e Informes
        icon: '<i class="fa fa-line-chart" aria-hidden="true"></i>'
        items:
          - route: app_comisiones
            label: Reporte de Comisiones
          - route: app_ventas_generales
            label: Ventas Generales
          - route: corte_caja
            label: Corte de caja
          - route: informe-ventas
            label: Informe de ventas
          - route: app_reporte_ordenes_laboratorio
            label: Reporte Graduaciones
        roles: [ 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN' ]

      # Laboratorio actualizado con OPTOMETRISTA
      Laboratorio:
        label: Laboratorio
        label_catalogue: Laboratorio
        icon: '<i class="fa fa-flask" aria-hidden="true"></i>'
        items:
          - route: app_lab_dashboarda
            label: Dashboard
          - admin.ordenlab
          - admin.flujoexpediente
          - route: app_laboratory_order
            label: Configuración ordenes de laboratorio
        roles: [ 'ROLE_LAB', 'ROLE_CALIDAD', 'ROLE_OPTOMETRISTA' ]

      Catalogos:
        label: Catálogos
        label_catalogue: Catálogos
        icon: '<i class="fa fa-book" aria-hidden="true"></i>'
        items:
          - admin.producto
          - admin.sucursal
          - admin.proveedor
          - admin.proveedorcontacto
          - admin.proveedoremail
          - admin.proveedortelefono
          - admin.medida
          - admin.unidad_medida
          - admin.clase
          - admin.categoria
          - admin.marca
          - admin.empresacliente
          - admin.tratamiento
          - admin.tipobisel
          - admin.disenolentes
          - admin.material
          - admin.categoriadocumentos
          - admin.categoriaanuncio
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_ADMIN' ]

      Clientes:
        label: Clientes
        label_catalogue: Clientes
        icon: '<i class="fa-solid fa-users" aria-hidden="true"></i>'
        items:
          - admin.cliente
          - admin.unidad
          - route: subir-clientes-excel
            label: Subir Excel Clientes
        roles: [ 'ROLE_SUPER_ADMIN','ROLE_ADMIN','ROLE_VENDEDOR' ]

      # Operaciones sin ventas a crédito para MENSAJERO
      Operaciones:
        label: Operaciones
        label_catalogue: Operaciones
        icon: '<i class="fa-solid fa-cogs"></i>'
        items:
          - route: app_dates_calendar
            label: Citas
          - route: app_shipment
            label: Configuración de envíos
          - route: shipment-get-delivery-dashboard
            label: Dashboard de envíos
        roles: [ 'ROLE_MENSAJERO' ]

      Autorizaciones:
        label: 'Autorizaciones'
        label_catalogue: 'Autorizaciones'
        icon: '<i class="fa-solid fa-check-circle"></i>'
        items:
          - route: app_dashboard_autorizaciones_dash
            label: Dashboard de Autorizaciones
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 1
            label: 'Mandar autorizaciones'
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 2
            label: 'Recibir autorizaciones'
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 3
            label: 'Mandar a facturar'
          - route: 'app_factura_autorizacion'
            route_params:
              stage: 4
            label: 'Facturar autorizaciones'
          - admin.ventagroup
        roles: [ 'ROLE_VENDEDOR', 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN' ]

      Facturacion:
        label: Facturación
        label_catalogue: Facturación
        icon: '<i class="fa fa-file-text" aria-hidden="true"></i>'
        items:
          - admin.ventafactura
          - admin.clientefacturadatos
          - route: app_reporte_facturacion
            label: Reporte de Ventas Facturadas
        roles: [ 'ROLE_ADMIN', 'ROLE_SUPER_ADMIN' ]

      Compras:
        label: Compras
        label_catalogue: Compras
        icon: '<i class="fa-solid fa-cart-shopping" aria-hidden="true"></i>'
        items:
          - route: app_proveedor
            label: Registro de Proveedores
          - route: app_orden_compra
            label: Creador de Orden de compra
          - route: app_administrador_orden_compra
            label: Administrador de Ordenes de Compra
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Configuracion:
        label: Configuración
        label_catalogue: Configuración
        icon: '<i class="fa fa-cog" aria-hidden="true"></i>'
        items:
          - admin.usuario
          - admin.empresa
          - admin.usuarioempresapermiso
          - route: config_entry_points
            label: Accessos
          - admin.documentos
          - admin.anuncios
        roles: [ 'ROLE_SUPER_ADMIN' ]

      Marketing:
        label: Marketing
        label_catalogue: Marketing
        icon: '<i class="fa-solid fa-bullhorn" aria-hidden="true"></i>'
        items:
          - route: app_marketing_campaign
            label: Campaña
          - route: app_messages
            label: Mensajes
        roles: [ 'ROLE_SUPER_ADMIN' ]


  templates:
    dashboard: 'admin/dashboard.html.twig'
    layout: 'admin/layout.html.twig'
    tab_menu_template: 'admin/menu.html.twig'
    base_list_field: 'admin/base_list_field.html.twig'
    #list: 'admin/base_list.html.twig'
    user_block: 'admin/user_block.html.twig'

sonata_block:
  blocks:
    sonata.admin.block.admin_list:
      contexts: [ admin ]